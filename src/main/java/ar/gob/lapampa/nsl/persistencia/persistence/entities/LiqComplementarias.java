package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_COMPLEMENTARIAS")
@NamedQueries(value = {
    @NamedQuery(name = "LiqComplementarias.findAll", query = "SELECT l FROM LiqComplementarias l")})
public class LiqComplementarias implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MES")
  private long mes;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANIO")
  private long anio;
  @Column(name = "ESTADO")
  private Character estado;
  @JoinColumn(name = "PRC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqProcesos prcId;
  @OneToMany(mappedBy = "cplId", fetch = FetchType.LAZY)
  private Set<RrhhOcupacionesConceptos> rrhhOcupacionesConceptosSet;

  public LiqComplementarias(Long id) {
    this.id = id;
  }

  public LiqComplementarias(Long id, String descripcion, long mes, long anio) {
    this.id = id;
    this.descripcion = descripcion;
    this.mes = mes;
    this.anio = anio;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqComplementarias[ id=" + id + " ]";
  }

}
