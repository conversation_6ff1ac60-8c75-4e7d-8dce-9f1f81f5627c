package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_ROLES")
@SequenceGenerator(name = "RRHH_ROL_SEQ", sequenceName = "RRHH_ROL_SEQ", allocationSize = 1)
public class RrhhRol implements Serializable {

  @Serial
  private static final long serialVersionUID = 3228910745434690082L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_ROL_SEQ")
  private Long id;

  @Basic(optional = false)
  @Column(name = "NOMBRE")
  private String nombre;

  @Basic(optional = true)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @ManyToMany(fetch = FetchType.LAZY)
  @JoinTable(name = "RRHH_ACCIONES_ROLES",
      joinColumns = {@JoinColumn(name = "RRHH_ROL_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "RRHH_ACCION_ID", referencedColumnName = "ID")})
  private List<RrhhAcciones> acciones;

  @ManyToMany(fetch = FetchType.LAZY)
  @JoinTable(name = "RRHH_MENU_ROLES",
      joinColumns = {@JoinColumn(name = "RRHH_ROL_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "RRHH_MENU_ID", referencedColumnName = "ID")})
  private List<RrhhMenu> menues;

  @ManyToMany(fetch = FetchType.LAZY)
  @JoinTable(name = "RRHH_ROLES_EMPRESAS",
      joinColumns = {@JoinColumn(name = "RRHH_ROL_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "RRHH_EMP_ID", referencedColumnName = "ID")})
  private List<RrhhEmpresa> empresas;

  @ManyToMany(fetch = FetchType.LAZY)
  @JoinTable(name = "RRHH_USUARIOS_ROLES",
      joinColumns = {@JoinColumn(name = "RRHH_ROL_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "RRHH_USR_ID", referencedColumnName = "ID")})
  private List<RrhhUsuario> usuarios;

}
