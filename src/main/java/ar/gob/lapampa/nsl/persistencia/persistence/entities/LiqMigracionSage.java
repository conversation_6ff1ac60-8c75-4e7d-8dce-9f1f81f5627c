package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_MIGRACION_SAGE")
@NamedQueries(value = {
    @NamedQuery(name = "LiqMigracionSage.findAll", query = "SELECT l FROM LiqMigracionSage l")})
public class LiqMigracionSage implements Serializable {
  private static final long serialVersionUID = 1L;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private BigDecimal id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "NOMBRE_TABLA")
  private String nombreTabla;
  @Column(name = "CANTIDAD_TUPLAS")
  private BigInteger cantidadTuplas;
  @Column(name = "MAX_SECUENCIA")
  private BigInteger maxSecuencia;
  @Column(name = "FECHA_MIGRACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaMigracion;
  @Column(name = "CANTIDAD_TUPLAS_MAPEADAS")
  private BigInteger cantidadTuplasMapeadas;
  @Column(name = "FECHA_MAPEO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaMapeo;
  @Column(name = "ORDEN")
  private BigInteger orden;
  @Size(max = 4000)
  @Column(name = "CONSULTA_SELECT")
  private String consultaSelect;
  @Size(max = 4000)
  @Column(name = "CONSULTA_INSERT")
  private String consultaInsert;
  @Size(max = 4000)
  @Column(name = "NOMBRE_PROC_VALIDACION")
  private String nombreProcValidacion;
  @Size(max = 4000)
  @Column(name = "NOMBRE_PROC_MAPEO")
  private String nombreProcMapeo;
  @Column(name = "CANT_TUPLAS_DETECTADAS")
  private Long cantTuplasDetectadas;

  public LiqMigracionSage(BigDecimal id) {
    this.id = id;
  }

  public LiqMigracionSage(BigDecimal id, String nombreTabla) {
    this.id = id;
    this.nombreTabla = nombreTabla;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqMigracionSage[ id=" + id + " ]";
  }

}
