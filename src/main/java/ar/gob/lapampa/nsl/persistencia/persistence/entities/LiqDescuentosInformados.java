package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_DESCUENTOS_INFORMADOS")
@NamedQueries(value = {@NamedQuery(name = "LiqDescuentosInformados.findAll",
    query = "SELECT l FROM LiqDescuentosInformados l")})
public class LiqDescuentosInformados implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MONTO_PORCENTAJE")
  private BigDecimal montoPorcentaje;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "DIGITO")
  private short digito;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MES")
  private short mes;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANIO")
  private short anio;
  @Column(name = "ARCHIVO_ID")
  private Long archivoId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PROCESADO")
  private Character procesado;
  @Column(name = "OCU_ID")
  private Long ocuId;
  @Column(name = "SECUENCIAL")
  private Long secuencial;
  @OneToMany(mappedBy = "ldiId", fetch = FetchType.LAZY)
  private Set<RrhhAfiliaciones> rrhhAfiliacionesSet;
  @JoinColumn(name = "COD_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqCodigos codId;
  @OneToMany(mappedBy = "ldiId", fetch = FetchType.LAZY)
  private Set<LiqDescuentos> liqDescuentosSet;

  public LiqDescuentosInformados(Long id) {
    this.id = id;
  }

  public LiqDescuentosInformados(Long id, BigDecimal montoPorcentaje, short digito, short mes,
      short anio, Character procesado) {
    this.id = id;
    this.montoPorcentaje = montoPorcentaje;
    this.digito = digito;
    this.mes = mes;
    this.anio = anio;
    this.procesado = procesado;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqDescuentosInformados[ id=" + id + " ]";
  }

}
