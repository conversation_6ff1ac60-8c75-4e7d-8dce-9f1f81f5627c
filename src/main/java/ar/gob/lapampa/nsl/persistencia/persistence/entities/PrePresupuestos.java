package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_PRESUPUESTOS")
@SequenceGenerator(name = "PRE_PRE_SEQ", sequenceName = "PRE_PRE_SEQ", allocationSize = 1)
public class PrePresupuestos implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PRE_PRE_SEQ")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Basic(optional = true)
  @Column(name = "NOR_NRO")
  private Long norNro;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  public PrePresupuestos(Long id) {

    this.id = id;
  }

  public PrePresupuestos(Long id, String descripcion, Long norNro, Boolean activo) {

    this.id = id;
    this.descripcion = descripcion;
    this.norNro = norNro;
    this.activo = activo;
  }

}
