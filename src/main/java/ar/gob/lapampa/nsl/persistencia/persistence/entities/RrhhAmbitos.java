package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_AMBITOS")
@NamedQueries(
    value = {@NamedQuery(name = "RrhhAmbitos.findAll", query = "SELECT r FROM RrhhAmbitos r")})
public class RrhhAmbitos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "LIQUIDABLE")
  private Character liquidable;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "ambId", fetch = FetchType.LAZY)
  private Set<RrhhAntiguedadesReconocidas> rrhhAntiguedadesReconocidasSet;

  public RrhhAmbitos(Long id) {
    this.id = id;
  }

  public RrhhAmbitos(Long id, Character liquidable) {
    this.id = id;
    this.liquidable = liquidable;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhAmbitos[ id=" + id + " ]";
  }

}
