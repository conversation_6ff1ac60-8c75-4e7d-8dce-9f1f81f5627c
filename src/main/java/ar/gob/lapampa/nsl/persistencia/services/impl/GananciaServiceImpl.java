package ar.gob.lapampa.nsl.persistencia.services.impl;

import java.util.List;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.GananciaRequestDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.responses.GananciaPasoResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.responses.GananciaResponseDTO;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QBduPersonasFisicas;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QGanPaso;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QGanPasoTipo;
import ar.gob.lapampa.nsl.persistencia.services.GananciaService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

@Service
public class GananciaServiceImpl implements GananciaService {

  @PersistenceContext
  private EntityManager entityManager;

  @Override
  public GenericResponseDTO listar(GananciaRequestDTO request) {

    final GenericResponseDTO response = new GenericResponseDTO();

    final QGanPaso ganPaso = QGanPaso.ganPaso;

    final QGanPasoTipo ganPasoTipo = QGanPasoTipo.ganPasoTipo;

    final QBduPersonasFisicas bduPersonasFisicas = QBduPersonasFisicas.bduPersonasFisicas;

    final JPAQuery<GananciaResponseDTO> query = new JPAQuery<>(entityManager).distinct()
        .select(Projections.constructor(GananciaResponseDTO.class, ganPaso.agente,
            bduPersonasFisicas.apellido, bduPersonasFisicas.nombres))
        .from(ganPaso).innerJoin(ganPasoTipo).on(ganPasoTipo.id.eq(ganPaso.tipo))
        .innerJoin(bduPersonasFisicas).on(bduPersonasFisicas.id.eq(ganPaso.agente))
        .where(ganPaso.corrida.eq(request.getCorrida()));

    final Integer totalRegistros = query.fetch().size();

    int pageSize = request.getPageSize();
    Long offSet = request.getOffset();
    final List<GananciaResponseDTO> listaRegistros = query.limit(pageSize).offset(offSet).fetch();

    final PageImpl<GananciaResponseDTO> page =
        new PageImpl<>(listaRegistros, request, totalRegistros);

    response.setEstadoExito(page);
    return response;
  }

  @Override
  public GenericResponseDTO listarPasos(GananciaRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();
    final Long corrida = request.getCorrida();
    final Long agente = request.getAgente();
    final QGanPaso ganPaso = QGanPaso.ganPaso;
    final QGanPasoTipo ganPasoTipo = QGanPasoTipo.ganPasoTipo;

    final JPAQuery<GananciaPasoResponseDTO> query = new JPAQuery<>(entityManager)
        .select(Projections.constructor(GananciaPasoResponseDTO.class, ganPaso.agente,
            ganPasoTipo.descripcion, ganPaso.vlrAlf, ganPaso.vlrNum, ganPaso.fechaMov,
            ganPaso.tipo))
        .from(ganPaso).join(ganPasoTipo).on(ganPasoTipo.id.eq(ganPaso.tipo))
        .where(ganPaso.corrida.eq(corrida).and(ganPaso.agente.eq(agente)));

    final Integer totalRegistros = query.fetch().size();

    final List<GananciaPasoResponseDTO> listaRegistros = query.orderBy(ganPaso.id.asc())
        .limit(request.getPageSize()).offset(request.getOffset()).fetch();

    final PageImpl<GananciaPasoResponseDTO> page =
        new PageImpl<>(listaRegistros, request, totalRegistros);

    response.setEstadoExito(page);
    return response;
  }

}
