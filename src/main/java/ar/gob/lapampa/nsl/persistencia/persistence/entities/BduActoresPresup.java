package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ACTORES_PRESUP")
@NamedQueries(value = {
    @NamedQuery(name = "BduActoresPresup.findAll", query = "SELECT b FROM BduActoresPresup b")})
public class BduActoresPresup implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected BduActoresPresupPK bduActoresPresupPK;
  @Column(name = "ACTIVO")
  private Character activo;
  @Column(name = "PROPAGA")
  private Character propaga;
  @Column(name = "MODALIDAD")
  private Long modalidad;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;
  @JoinColumn(name = "ACT_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduActores bduActores;
  @JoinColumn(name = "SIS_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduSistemas bduSistemas;

  public BduActoresPresup(BduActoresPresupPK bduActoresPresupPK) {
    this.bduActoresPresupPK = bduActoresPresupPK;
  }

  public BduActoresPresup(BduActoresPresupPK bduActoresPresupPK, Date fecha) {
    this.bduActoresPresupPK = bduActoresPresupPK;
    this.fecha = fecha;
  }

  public BduActoresPresup(long sisId, long actId, long orpId) {
    bduActoresPresupPK = new BduActoresPresupPK(sisId, actId, orpId);
  }

}
