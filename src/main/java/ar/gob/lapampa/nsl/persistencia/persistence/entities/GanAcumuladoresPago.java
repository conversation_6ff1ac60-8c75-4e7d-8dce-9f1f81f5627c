package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_ACUMULADORES_PGO")
@SequenceGenerator(name = "GAN_ACUMULADORES_PGO_SQ", sequenceName = "GAN_ACUMULADORES_PGO_SQ",
    allocationSize = 1)
public class GanAcumuladoresPago implements Serializable {

  private static final long serialVersionUID = 1775351475018602107L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_ACUMULADORES_PGO_SQ")
  private Long id;

  @Column(name = "COD_NRO")
  private Integer codNro;

  @JoinColumn(name = "ACUMULADOR", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private GanAcumuladores acuId;

}
