package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_LICENCIAS_TIPOS")
public class RrhhLicenciasTipos implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Basic(optional = false)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Column(name = "CDAD_DIAS_TOPE")
  private Long cdadDiasTope;

  @Basic(optional = false)
  @Column(name = "CONTINUA")
  private String continua;

  @Basic(optional = false)
  @Column(name = "HABILES")
  private String habiles;

  @Basic(optional = false)
  @Column(name = "FAMILIAR")
  private String familiar;

  @Basic(optional = false)
  @Column(name = "PORC_GOCE_HABERES")
  private BigDecimal porcGoceHaberes;

  @Column(name = "SEXO")
  private String sexo;

  @Basic(optional = false)
  @Column(name = "ACTIVO")
  private String activo;

  @Basic(optional = false)
  @Column(name = "REQUIERE_NORMA")
  private String requiereNorma;

  @Basic(optional = false)
  @Column(name = "APLICA_AGENTE")
  private String aplicaAgente;

  @Basic(optional = false)
  @Column(name = "TIPO_ENFERMEDAD")
  private String tipoEnfermedad;

  @Column(name = "HORAS_ADICIONALES")
  private Long horasAdicionales;

  @Column(name = "NRO")
  private Long nro;

  @Column(name = "FE_HASTA_EDITABLE")
  private String feHastaEditable;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "AFECTA_ANTIG")
  @Convert(converter = YesNoConverter.class)
  private Boolean afectaAntig;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "AFECTA_PRESENT")
  @Convert(converter = YesNoConverter.class)
  private Boolean afectaPresent;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "litId", fetch = FetchType.LAZY)
  private Set<RrhhLicenciasRangosAnt> rrhhLicenciasRangosAntSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "ltiId", fetch = FetchType.LAZY)
  private Set<RrhhLicencias> rrhhLicenciasSet;

  @OneToMany(mappedBy = "ltiId", fetch = FetchType.LAZY)
  private Set<RrhhLicenciasAcumuladas> rrhhLicenciasAcumuladasSet;

  public RrhhLicenciasTipos(Long id) {
    this.id = id;
  }

}
