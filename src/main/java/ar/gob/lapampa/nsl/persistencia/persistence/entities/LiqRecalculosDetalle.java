package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_RECALCULOS_DETALLE")
@NamedQueries(value = {@NamedQuery(name = "LiqRecalculosDetalle.findAll",
    query = "SELECT l FROM LiqRecalculosDetalle l")})
public class LiqRecalculosDetalle implements Serializable {
  private static final long serialVersionUID = 1L;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "OCU_ID")
  private long ocuId;
  @Column(name = "MES")
  private Short mes;
  @Column(name = "ANIO")
  private Short anio;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MONTO")
  private BigDecimal monto;
  @Column(name = "PRS_ID")
  private Long prsId;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Column(name = "MONTO_VIEJO")
  private BigDecimal montoViejo;
  @Column(name = "MONTO_NUEVO")
  private BigDecimal montoNuevo;
  @Size(max = 20)
  @Column(name = "COD_TIPO")
  private String codTipo;
  @Size(max = 20)
  @Column(name = "COD_NRO")
  private String codNro;
  @Size(max = 10)
  @Column(name = "COD_NRO_NUEVO")
  private String codNroNuevo;
  @Column(name = "COD_ID_NUEVO")
  private Long codIdNuevo;
  @JoinColumn(name = "COD_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqCodigos codId;
  @JoinColumn(name = "PRC_PADRE_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqProcesos prcPadreId;
  @JoinColumn(name = "PRC_VIEJO_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqProcesos prcViejoId;
  @JoinColumn(name = "PRC_NUEVO_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqProcesos prcNuevoId;

  public LiqRecalculosDetalle(Long id) {
    this.id = id;
  }

  public LiqRecalculosDetalle(Long id, long ocuId, BigDecimal monto) {
    this.id = id;
    this.ocuId = ocuId;
    this.monto = monto;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqRecalculosDetalle[ id=" + id + " ]";
  }

}
