package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_OTROS_MEDIOS_FNC")
@NamedQueries(value = {
    @NamedQuery(name = "BduOtrosMediosFnc.findAll", query = "SELECT b FROM BduOtrosMediosFnc b")})
public class BduOtrosMediosFnc implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 10, max = 10)
  @Column(name = "VALOR")
  private String valor;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ORF_ID")
  private long orfId;
  @JoinColumn(name = "OMT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduOtrosMediosTipos omtId;

  public BduOtrosMediosFnc(Long id) {
    this.id = id;
  }

  public BduOtrosMediosFnc(Long id, String valor, long orfId) {
    this.id = id;
    this.valor = valor;
    this.orfId = orfId;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduOtrosMediosFnc[ id=" + id + " ]";
  }

}
