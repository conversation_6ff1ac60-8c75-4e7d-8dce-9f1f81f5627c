package ar.gob.lapampa.nsl.persistencia.services;

import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.request.LiqCodigoRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.request.LiqCodigoTipoRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.request.ListadoConceptoConvenioRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;

@Service
public interface LiqCodigoService {
  GenericResponseDTO listarTiposdeCodigo(LiqCodigoTipoRequestDTO request);

  GenericResponseDTO listar(LiqCodigoRequestDTO request);

  GenericResponseDTO listarConceptosConvenio(ListadoConceptoConvenioRequestDTO request);
}
