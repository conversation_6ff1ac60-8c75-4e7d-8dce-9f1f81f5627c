package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_COMBINACION_PRESUPUESTARIA")
@SequenceGenerator(name = "PRE_COM_SEQ", sequenceName = "PRE_COM_SEQ", allocationSize = 1)
public class PreCombinacionPresupuestaria implements Serializable {

  private static final long serialVersionUID = -8842657806109892526L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PRE_COM_SEQ")
  private Long id;

  private String activo;
  @JoinColumn(name = "CAR_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private BduOrganismosPre carId; // caracter NVL 1

  @JoinColumn(name = "CUE_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private PreCuentas cueId;

  private String denominacion;

  @Temporal(TemporalType.DATE)
  @Column(name = "FECHA_ALTA")
  private Date fechaAlta;

  @Temporal(TemporalType.DATE)
  @Column(name = "FECHA_BAJA")
  private Date fechaBaja;

  @JoinColumn(name = "FUN_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private PreFunciones funId;

  @JoinColumn(name = "JUR_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private BduOrganismosPre jurId; // jurisdiccion NVL 2

  private Long nropar;

  @JoinColumn(name = "PPR_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private PrePartidasPrincipales pprId;

  @JoinColumn(name = "PPS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private PrePartidasParciales ppsId;

  @JoinColumn(name = "PPSUB_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private PrePartidasSubparciales ppsubId;

  @JoinColumn(name = "SEC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private PreSecciones secId;

  @Column(name = "CLA_ID")
  private Long claId;

  @Column(name = "SUBCLA")
  private Long subcla;

  @Column(name = "SUBSE")
  private Long subse;

  @JoinColumn(name = "UOR_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private BduOrganismosPre uorId; // Unidad Organizacional NVL 3

}
