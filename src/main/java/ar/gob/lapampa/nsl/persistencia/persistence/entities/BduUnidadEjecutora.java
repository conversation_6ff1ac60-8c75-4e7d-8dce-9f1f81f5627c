package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_UNIDAD_EJECUTORA")
@NamedQueries(value = {
    @NamedQuery(name = "BduUnidadEjecutora.findAll", query = "SELECT b FROM BduUnidadEjecutora b")})
public class BduUnidadEjecutora implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Size(max = 1000)
  @Column(name = "USUARIO_MOV")
  private String usuarioMov;
  @Column(name = "FECHA_MOV")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaMov;
  @OneToMany(mappedBy = "bueId", fetch = FetchType.LAZY)
  private Set<BduOrganismosPre> bduOrganismosPreSet;

  public BduUnidadEjecutora(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduUnidadEjecutora[ id=" + id + " ]";
  }

}
