package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ACT_LDAP")
@NamedQueries(
    value = {@NamedQuery(name = "BduActLdap.findAll", query = "SELECT b FROM BduActLdap b")})
public class BduActLdap implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 400)
  @Column(name = "USUARIO")
  private String usuario;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 400)
  @Column(name = "NOMBRE")
  private String nombre;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 400)
  @Column(name = "APELLIDO")
  private String apellido;
  @Size(max = 400)
  @Column(name = "U_ID")
  private String uId;
  @Size(max = 400)
  @Column(name = "CN")
  private String cn;
  @OneToMany(mappedBy = "alpId", fetch = FetchType.LAZY)
  private Set<BduActores> bduActoresSet;

  public BduActLdap(Long id) {
    this.id = id;
  }

  public BduActLdap(Long id, String usuario, String nombre, String apellido) {
    this.id = id;
    this.usuario = usuario;
    this.nombre = nombre;
    this.apellido = apellido;
  }

}
