package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.List;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_CLASES")
@SequenceGenerator(name = "PRE_CLA_SEQ", sequenceName = "PRE_CLA_SEQ", allocationSize = 1)
public class PreClases implements Serializable {

  private static final long serialVersionUID = -7461634337722222110L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PRE_CLA_SEQ")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Column(name = "NRO")
  private Long nro;

  @JoinColumn(name = "ESC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private PreEscalafones escId;

  @JoinColumn(name = "CAT_TESTIGO_ID", referencedColumnName = "ID")
  @OneToOne(optional = true, fetch = FetchType.LAZY)
  private PreCategorias catTesId;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "claId", fetch = FetchType.LAZY)
  private List<PreGrupos> preGruposList;

}
