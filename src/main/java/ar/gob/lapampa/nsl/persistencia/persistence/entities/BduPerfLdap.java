package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PERF_LDAP")
@NamedQueries(
    value = {@NamedQuery(name = "BduPerfLdap.findAll", query = "SELECT b FROM BduPerfLdap b")})
public class BduPerfLdap implements Serializable {
  private static final long serialVersionUID = 1L;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 400)
  @Column(name = "CN")
  private String cn;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 2000)
  @Column(name = "UNIQUE_MEMBER")
  private String uniqueMember;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 400)
  @Column(name = "DISPLAY_NAME")
  private String displayName;
  @Size(max = 1000)
  @Column(name = "OWNER")
  private String owner;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @OneToMany(mappedBy = "plpId", fetch = FetchType.LAZY)
  private Set<BduPerfiles> bduPerfilesSet;

  public BduPerfLdap(Long id) {
    this.id = id;
  }

  public BduPerfLdap(Long id, String cn, String uniqueMember, String displayName) {
    this.id = id;
    this.cn = cn;
    this.uniqueMember = uniqueMember;
    this.displayName = displayName;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduPerfLdap[ id=" + id + " ]";
  }

}
