package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_RECIBOS")
public class LiqRecibos implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Size(max = 200)
  @Column(name = "APELLIDO")
  private String apellido;

  @Size(max = 200)
  @Column(name = "NOMBRE")
  private String nombre;

  @Column(name = "DOCUMENTO")
  private BigInteger documento;

  @Column(name = "LEGAJO")
  private BigInteger legajo;

  @Column(name = "ANTIGUEDAD_MES")
  private BigInteger antiguedadMes;

  @Column(name = "ANTIGUEDAD_ANIO")
  private BigInteger antiguedadAnio;

  @Column(name = "MES_LIQUIDACION")
  private BigInteger mesLiquidacion;

  @Column(name = "ANIO_LIQUIDACION")
  private BigInteger anioLiquidacion;

  @Size(max = 200)
  @Column(name = "CUENTA_DEPOSITAR")
  private String cuentaDepositar;

  @Size(max = 200)
  @Column(name = "SUCURSAL")
  private String sucursal;

  @Column(name = "TOTAL_HABERES")
  private BigDecimal totalHaberes;

  @Column(name = "TOTAL_DESCUENTOS")
  private BigDecimal totalDescuentos;

  @Column(name = "TOTAL_BANCO")
  private BigDecimal totalBanco;

  @Column(name = "TOTAL_BRUTO")
  private BigDecimal totalBruto;

  @Column(name = "TOTAL_NETO")
  private BigDecimal totalNeto;

  @Column(name = "LIQUIDO_A_PAGAR")
  private BigDecimal liquidoAPagar;

  @Column(name = "NRO_RECIBO")
  private BigInteger nroRecibo;

  @Size(max = 200)
  @Column(name = "ESTABLECIMIENTO")
  private String establecimiento;

  @JoinColumn(name = "DAD_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private SiafDireccionesAdm dadId;

  @JoinColumn(name = "PRO_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private SiafProgramas proId;

  @JoinColumn(name = "ACT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduOrganismosPre actId;

  @JoinColumn(name = "CAT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private PreCategorias catId;

  @JoinColumn(name = "EST_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhEstablecimientos estId;

  @JoinColumn(name = "CEQ_CAT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private PreCategorias ceqCatId;

  @Column(name = "CGE_REC_ID")
  private BigInteger cgeRecId;

  @Column(name = "TOTAL_PATRONALES")
  private BigDecimal totalPatronales;

  @Column(name = "FECHA_CREACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaCreacion;

  @Column(name = "AFILIADO")
  private Long afiliado;

  public LiqRecibos(Long id) {
    this.id = id;
  }

}
