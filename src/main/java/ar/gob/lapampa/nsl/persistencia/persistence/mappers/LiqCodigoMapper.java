package ar.gob.lapampa.nsl.persistencia.persistence.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import ar.gob.lapampa.nsl.datatransfer.LiqCodigosDTO;
import ar.gob.lapampa.nsl.persistencia.models.CodigoModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.LiqCodigos;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LiqCodigoMapper {

  LiqCodigoMapper INSTANCE = Mappers.getMapper(LiqCodigoMapper.class);

  // TODO: Ignoramos todas la listas para evitar un dto ciclico ver de mejorar
  // @Mapping(target = "usuarios", ignore = true)
  LiqCodigosDTO liqCodigoToLiqCodigoDto(LiqCodigos liqCodigos);

  static CodigoModel liqCodigoToCodigoModel(LiqCodigos liqCodigos) {
    CodigoModel model = new CodigoModel(liqCodigos.getId());
    model.setNro(liqCodigos.getNro());
    model.setNombreCorto(liqCodigos.getNombreCorto());
    model.setCotId(liqCodigos.getCotId().getId());
    return model;
  }
}
