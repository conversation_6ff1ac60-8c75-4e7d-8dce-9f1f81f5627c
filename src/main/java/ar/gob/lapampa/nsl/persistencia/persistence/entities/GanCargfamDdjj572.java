package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_CARGFAM_DDJJ572")
@SequenceGenerator(name = "GAN_CARGFAM_DDJJ572_SQ", sequenceName = "GAN_CARGFAM_DDJJ572_SQ",
    allocationSize = 1)
public class GanCargfamDdjj572 implements Serializable {

  private static final long serialVersionUID = 4778599275459971084L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_CARGFAM_DDJJ572_SQ")
  private Long id;

  @Column(name = "APELLIDO")
  private String apellido;

  @Temporal(TemporalType.TIMESTAMP)
  private Date fechanacimiento;

  @Column(name = "MES_DESDE")
  private Long mesDesde;

  @Column(name = "MES_HASTA")
  private Long mesHasta;

  @Column(name = "NOMBRES")
  private String nombres;

  @Column(name = "NRODOC")
  private String nrodoc;

  @Column(name = "PARENTESCO")
  private Long parentesco;

  @JoinColumn(name = "PRESENTACION", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER, cascade = CascadeType.ALL)
  private GanPresentDdjj572 presentacion;

  @Column(name = "TIPODOCUMENTO")
  private Long tipodocumento;

  @Column(name = "VIGENTE_PROXIMOS_PERIODOS", length = 2)
  private String vigenteProximosPeriodos;

  @Column(name = "PORCENTAJEDED")
  private Long porcentajeDed;

}
