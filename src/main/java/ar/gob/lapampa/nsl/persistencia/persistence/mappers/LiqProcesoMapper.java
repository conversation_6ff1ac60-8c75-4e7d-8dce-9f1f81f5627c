package ar.gob.lapampa.nsl.persistencia.persistence.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import ar.gob.lapampa.nsl.datatransfer.LiqProcesosDTO;
import ar.gob.lapampa.nsl.persistencia.models.ProcesoModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.LiqProcesos;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LiqProcesoMapper {

    LiqProcesoMapper INSTANCE = Mappers.getMapper(LiqProcesoMapper.class);

    // TODO: Ignoramos todas la listas para evitar un dto ciclico ver de mejorar
    //@Mapping(target = "", ignore = true)
    LiqProcesosDTO liqProcesoToLiqProcesoDto(LiqProcesos liqProcesos);

    static ProcesoModel liqProcesoToProcesoModel(LiqProcesos liqProcesos) {
        ProcesoModel model = new ProcesoModel(liqProcesos.getId());
        model.setId(liqProcesos.getId());
        model.setNombreLiquidacion(liqProcesos.getLitId().getDescripcion());
        model.setAnio(liqProcesos.getAnio());
        model.setTipoProceso(liqProcesos.getTipoProceso());
        model.setMes(liqProcesos.getMes());
        model.setFechaCierre(liqProcesos.getFechaCierre());
        model.setFechaFin(liqProcesos.getFechaFin());
        model.setFechaInicio(liqProcesos.getFechaInicio());
        model.setEstado(liqProcesos.getEstado());
        return model;
    }
}
