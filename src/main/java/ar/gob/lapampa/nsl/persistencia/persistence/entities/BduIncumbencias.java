package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_INCUMBENCIAS")
@NamedQueries(value = {
    @NamedQuery(name = "BduIncumbencias.findAll", query = "SELECT b FROM BduIncumbencias b")})
public class BduIncumbencias implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 50)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @JoinTable(name = "BDU_TITULOS_INCUMBENCIAS",
      joinColumns = {@JoinColumn(name = "INC_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "TIT_ID", referencedColumnName = "ID")})
  @ManyToMany(fetch = FetchType.LAZY)
  private Set<BduTitulos> bduTitulosSet;

  public BduIncumbencias(Long id) {
    this.id = id;
  }

  public BduIncumbencias(Long id, String descripcion) {
    this.id = id;
    this.descripcion = descripcion;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduIncumbencias[ id=" + id + " ]";
  }

}
