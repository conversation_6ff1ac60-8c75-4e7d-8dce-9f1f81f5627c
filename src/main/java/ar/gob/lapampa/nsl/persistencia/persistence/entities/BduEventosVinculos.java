package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_EVENTOS_VINCULOS")
@NamedQueries(value = {
    @NamedQuery(name = "BduEventosVinculos.findAll", query = "SELECT b FROM BduEventosVinculos b")})
public class BduEventosVinculos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "CIRCUITO")
  private short circuito;
  @JoinColumn(name = "TEV_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduEventosTipos tevId;
  @JoinColumn(name = "TEV_POSIBLE_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduEventosTipos tevPosibleId;

  public BduEventosVinculos(Long id) {
    this.id = id;
  }

  public BduEventosVinculos(Long id, short circuito) {
    this.id = id;
    this.circuito = circuito;
  }

}
