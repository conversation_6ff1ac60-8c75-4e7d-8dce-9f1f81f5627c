package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_ARBOL_PRESUP")
@NamedQueries(value = {
    @NamedQuery(name = "LiqArbolPresup.findAll", query = "SELECT l FROM LiqArbolPresup l")})
public class LiqArbolPresup implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "DAD_ID")
  private long dadId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PROG_ID")
  private long progId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "SUBPROG_ID")
  private long subprogId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PROY_ID")
  private long proyId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACT_ID")
  private long actId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ENT_ID")
  private long entId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "JURIS_ID")
  private long jurisId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "SUBJURIS_ID")
  private long subjurisId;
  @JoinColumn(name = "PRC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqProcesos prcId;

  public LiqArbolPresup(Long id) {
    this.id = id;
  }

  public LiqArbolPresup(Long id, long dadId, long progId, long subprogId, long proyId, long actId,
      long entId, long jurisId, long subjurisId) {
    this.id = id;
    this.dadId = dadId;
    this.progId = progId;
    this.subprogId = subprogId;
    this.proyId = proyId;
    this.actId = actId;
    this.entId = entId;
    this.jurisId = jurisId;
    this.subjurisId = subjurisId;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqArbolPresup[ id=" + id + " ]";
  }

}
