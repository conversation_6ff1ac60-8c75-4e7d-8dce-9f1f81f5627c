package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_LIQUIDACIONES_TIPOS")
public class LiqLiquidacionesTipos implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "RETROACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean retroactivo;

  @OneToMany(mappedBy = "lqtId", fetch = FetchType.LAZY)
  private Set<LiqPasos> liqPasosSet;

  @OneToMany(mappedBy = "litId", fetch = FetchType.LAZY)
  private Set<LiqProcesos> liqProcesosSet;

  public LiqLiquidacionesTipos(Long id) {
    this.id = id;
  }

  public LiqLiquidacionesTipos(Long id, String descripcion, Boolean activo) {
    this.id = id;
    this.descripcion = descripcion;
    this.activo = activo;
  }

}
