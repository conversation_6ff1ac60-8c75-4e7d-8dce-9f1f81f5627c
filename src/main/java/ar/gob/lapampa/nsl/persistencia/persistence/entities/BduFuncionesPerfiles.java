package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_FUNCIONES_PERFILES")
@NamedQueries(value = {@NamedQuery(name = "BduFuncionesPerfiles.findAll",
    query = "SELECT b FROM BduFuncionesPerfiles b")})
public class BduFuncionesPerfiles implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected BduFuncionesPerfilesPK bduFuncionesPerfilesPK;
  @Column(name = "NIVEL")
  private Short nivel;
  @JoinColumn(name = "FSI_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduFuncionesSis bduFuncionesSis;
  @JoinColumn(name = "PFL_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduPerfiles bduPerfiles;

  public BduFuncionesPerfiles(BduFuncionesPerfilesPK bduFuncionesPerfilesPK) {
    this.bduFuncionesPerfilesPK = bduFuncionesPerfilesPK;
  }

  public BduFuncionesPerfiles(long pflId, long fsiId) {
    this.bduFuncionesPerfilesPK = new BduFuncionesPerfilesPK(pflId, fsiId);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduFuncionesPerfiles[ bduFuncionesPerfilesPK="
        + bduFuncionesPerfilesPK + " ]";
  }

}
