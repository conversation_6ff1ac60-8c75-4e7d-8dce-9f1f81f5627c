package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PERSONA_REL_MOTIVO")
@NamedQueries(value = {@NamedQuery(name = "BduPersonaRelMotivo.findAll",
    query = "SELECT b FROM BduPersonaRelMotivo b")})
public class BduPersonaRelMotivo implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "PARA_PERSONA_FIS")
  private Character paraPersonaFis;
  @Column(name = "PARA_PERSONA_JUR")
  private Character paraPersonaJur;
  @Column(name = "ACTIVO")
  private Character activo;

  public BduPersonaRelMotivo(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduPersonaRelMotivo[ id=" + id + " ]";
  }

}
