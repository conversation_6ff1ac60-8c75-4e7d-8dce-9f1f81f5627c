package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_LOTE_DDJJ572")
@SequenceGenerator(name = "GAN_LOTE_DDJJ572_SQ", sequenceName = "GAN_LOTE_DDJJ572_SQ",
    allocationSize = 1)
public class GanLoteDdjj572 implements Serializable {

  private static final long serialVersionUID = 3222888150074616252L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_LOTE_DDJJ572_SQ")
  private Long id;

  @Column(name = "FECHA")
  private Timestamp fecha;

  @Column(name = "USUARIO")
  private String usuario;

  @Column(name = "TOTAL")
  private Long total;

  @Column(name = "TOTAL_IMPORTADAS")
  private Long totalImportadas;

  @Column(name = "RESULTADO")
  private String resultado;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "lote", fetch = FetchType.LAZY)
  private List<GanPresentDdjj572> presentaciones;

  @JoinColumn(name = "EMPRESA", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhEmpresa empresa;

}
