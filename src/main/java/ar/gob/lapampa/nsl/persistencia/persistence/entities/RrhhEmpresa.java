package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.List;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_EMPRESAS")
@SequenceGenerator(name = "RRHH_EMP_SEQ", sequenceName = "RRHH_EMP_SEQ", allocationSize = 1)
public class RrhhEmpresa implements Serializable {

  private static final long serialVersionUID = -3239824324921150902L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_EMP_SEQ")
  private Long id;

  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Size(min = 1, max = 200)
  @Column(name = "CODIGO")
  private String codigo;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "empresa", fetch = FetchType.LAZY)
  private List<GanLoteDdjj572> lotes;

  @JoinTable(name = "RRHH_ROLES_EMPRESAS",
      joinColumns = {@JoinColumn(name = "RRHH_EMP_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "RRHH_ROL_ID", referencedColumnName = "ID")})
  @ManyToMany(fetch = FetchType.LAZY)
  private List<RrhhRol> roles;


  @JoinTable(name = "RRHH_EMPRESAS_ESCALAFONES",
          joinColumns = {@JoinColumn(name = "EMP_ID", referencedColumnName = "ID")},
          inverseJoinColumns = {@JoinColumn(name = "ESC_ID", referencedColumnName = "ID")})
  @ManyToMany(fetch = FetchType.LAZY)
  private List<PreEscalafones> convenios;

  public RrhhEmpresa(Long id) {
    this.id = id;
  }

}
