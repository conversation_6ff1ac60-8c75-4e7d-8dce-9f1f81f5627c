package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_EMPLEADO_DDJJ572")
@SequenceGenerator(name = "GAN_EMPLEADO_DDJJ572_SQ", sequenceName = "GAN_EMPLEADO_DDJJ572_SQ",
    allocationSize = 1)
public class GanEmpleadoDdjj572 implements Serializable {

  private static final long serialVersionUID = 391923072864291242L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_EMPLEADO_DDJJ572_SQ")
  private Long id;

  @Column(name = "APELLIDO")
  private String apellido;

  @Column(name = "CALLE")
  private String calle;

  @Column(name = "CP")
  private String cp;

  @Column(name = "CUIT")
  private String cuit;

  @Column(name = "LOCALIDAD")
  private String localidad;

  @Column(name = "NOMBRES")
  private String nombres;

  @Column(name = "NRO")
  private String nro;

  @Column(name = "PISO")
  private String piso;

  @Column(name = "PROVINCIA")
  private Long provincia;

  @Column(name = "NRODOC")
  private String nroDoc;

}
