package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_ANTIGUEDADES_RECONOCIDAS")
public class RrhhAntiguedadesReconocidas implements Serializable {

  private static final long serialVersionUID = -4084948774212710701L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;

  @Column(name = "LUGAR_TRABAJO")
  private String lugarTrabajo;

  @Column(name = "ANIOS")
  private Short anios;

  @Column(name = "MESES")
  private Short meses;

  @Column(name = "DIAS")
  private Short dias;

  @Column(name = "OCU_ID")
  private Long ocuId;

  @Column(name = "FECHA_APROBACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAprobacion;

  @Column(name = "ESTADO")
  private String estado;

  @Column(name = "COMENTARIO")
  private String comentario;

  @Column(name = "FECHA_ANULACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAnulacion;

  @Column(name = "FECHA_VIG_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigDesde;

  @Basic(optional = false)
  @Column(name = "ADMIN_DOCENTE")
  private String adminDocente;

  @Column(name = "FECHA_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDesde;

  @Column(name = "FECHA_HASTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaHasta;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduPersonas prsId;

  @JoinColumn(name = "ESC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private PreEscalafones escId;

  @JoinColumn(name = "AMB_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhAmbitos ambId;

}
