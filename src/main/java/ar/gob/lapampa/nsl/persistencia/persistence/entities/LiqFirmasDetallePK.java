package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class LiqFirmasDetallePK implements Serializable {

  private static final long serialVersionUID = -4402945837701718559L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FIR_ID")
  private long firId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ITEM")
  private long item;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqFirmasDetallePK[ firId=" + firId + ", item="
        + item + " ]";
  }

}
