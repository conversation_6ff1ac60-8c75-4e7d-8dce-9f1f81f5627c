package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PERSONAS_TELEFONOS")
@NamedQueries(value = {@NamedQuery(name = "BduPersonasTelefonos.findAll",
    query = "SELECT b FROM BduPersonasTelefonos b")})
public class BduPersonasTelefonos implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected BduPersonasTelefonosPK bduPersonasTelefonosPK;
  @JoinColumn(name = "TEL_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduTelefonos bduTelefonos;

  public BduPersonasTelefonos(BduPersonasTelefonosPK bduPersonasTelefonosPK) {
    this.bduPersonasTelefonosPK = bduPersonasTelefonosPK;
  }

  public BduPersonasTelefonos(long prsId, long telId) {
    this.bduPersonasTelefonosPK = new BduPersonasTelefonosPK(prsId, telId);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduPersonasTelefonos[ bduPersonasTelefonosPK="
        + bduPersonasTelefonosPK + " ]";
  }

}
