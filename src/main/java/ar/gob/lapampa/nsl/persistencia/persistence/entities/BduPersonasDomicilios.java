package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PERSONAS_DOMICILIOS")
@NamedQueries(value = {@NamedQuery(name = "BduPersonasDomicilios.findAll",
    query = "SELECT b FROM BduPersonasDomicilios b")})
public class BduPersonasDomicilios implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ODT_ID")
  private long odtId;
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @JoinColumn(name = "DMC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduDomicilios dmcId;
  @JoinColumn(name = "TDM_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduDomiciliosTipos tdmId;
  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduPersonas prsId;

  public BduPersonasDomicilios(Long id) {
    this.id = id;
  }

  public BduPersonasDomicilios(Long id, long odtId, Character activo) {
    this.id = id;
    this.odtId = odtId;
    this.activo = activo;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduPersonasDomicilios[ id=" + id + " ]";
  }

}
