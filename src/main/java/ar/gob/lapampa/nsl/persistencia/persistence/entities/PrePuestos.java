package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_PUESTOS")
@SequenceGenerator(name = "PRE_PST_SEQ", sequenceName = "PRE_PST_SEQ", allocationSize = 1)
public class PrePuestos implements Serializable {

  private static final long serialVersionUID = 9084565772409332030L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PRE_PST_SEQ")
  private Long id;

  @Column(name = "NROPUESTO")
  private Long nroPuesto;

  @Column(name = "NROHISTORIA")
  private Long nroHistoria;

  @Column(name = "NROLEGAJO")
  private String nroLegajo;

  @Column(name = "ANIODECRETO")
  private Long anioDecreto;

  @Column(name = "ANIORESOLUCION")
  private Long anioResolucion;

  @Column(name = "ANIOEXPEDIENTE")
  private Long anioExpediente;

  @Column(name = "NROLEY")
  private Long nroley;

  @Column(name = "NROEXPEDIENTE")
  private String nroExpediente;

  @Column(name = "NRODECRETO")
  private Long nroDecreto;

  @Column(name = "NRORESOLUCION")
  private String nroResolucion;

  @Column(name = "FECHAPROCESO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaProceso;

  @Column(name = "FECHAVALOR")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaValor;

  @Column(name = "OBSERVACIONES")
  private String observaciones;

  @JoinColumn(name = "OCU_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private RrhhOcupaciones ocuId;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduPersonasFisicas prsId;

  @Column(name = "NROREESTRUCTURA")
  private Long nroReestructura;

  @Column(name = "ANIOREESTRUCTURA")
  private Long anioReestructura;

  @OneToMany(mappedBy = "nroPuesto", fetch = FetchType.LAZY)
  private List<PrePuestosHist> historia;

  @JoinColumn(name = "CAT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private PreCategorias catId;

  @JoinColumn(name = "COMBPRES_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private PreCombinacionPresupuestaria combpresId;

  @OneToMany(mappedBy = "puestoId", cascade = CascadeType.ALL, orphanRemoval = true,
      fetch = FetchType.EAGER)
  private List<PrePuestosEstados> estados;

  @Column(name = "FECHA_CREACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaCreacion;

  @Column(name = "TIPO_NORMA")
  private String tipoNorma;

  @Column(name = "NRO_NORMA")
  private Long nroNorma;

  @Column(name = "ANIO_NORMA")
  private Long anioNorma;

  @JoinColumn(name = "FUNCION_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private RrhhFunciones funcionId;

  @JoinColumn(name = "ESTAB_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private RrhhEstablecimientos estabId;

  @Column(name = "FUNCION_EST")
  private String funcionEst;

  public PrePuestos(Long id) {
    this.id = id;
  }

  public PrePuestos(PrePuestos prePuesto) {
    nroPuesto = prePuesto.nroPuesto;
    nroHistoria = prePuesto.nroHistoria;
    nroLegajo = prePuesto.nroLegajo;
    anioDecreto = prePuesto.anioDecreto;
    anioResolucion = prePuesto.anioResolucion;
    anioExpediente = prePuesto.anioExpediente;
    nroley = prePuesto.nroley;
    nroExpediente = prePuesto.nroExpediente;
    nroDecreto = prePuesto.nroDecreto;
    nroResolucion = prePuesto.nroResolucion;
    fechaProceso = prePuesto.fechaProceso;
    fechaValor = prePuesto.fechaValor;
    observaciones = prePuesto.observaciones;
    ocuId = prePuesto.ocuId;
    prsId = prePuesto.prsId;
    nroReestructura = prePuesto.nroReestructura;
    combpresId = prePuesto.combpresId;
    catId = prePuesto.catId;
    estados = prePuesto.estados;
    fechaCreacion = prePuesto.fechaCreacion;
    tipoNorma = prePuesto.tipoNorma;
    nroNorma = prePuesto.nroNorma;
    anioNorma = prePuesto.anioNorma;
  }
}
