package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_AJUSTES_DDJJ572")
@SequenceGenerator(name = "GAN_AJUSTES_DDJJ572_SQ", sequenceName = "GAN_AJUSTES_DDJJ572_SQ",
    allocationSize = 1)
public class GanAjustesDdjj572 implements Serializable {

  private static final long serialVersionUID = -1798697099407777660L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_AJUSTES_DDJJ572_SQ")
  private Long id;

  @JoinColumn(name = "PRESENTACION", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER, cascade = CascadeType.ALL)
  private GanPresentDdjj572 presentacion;

  @Column(name = "DESCBASICA")
  private String descbasica;

  @Column(name = "DESCADICIONAL")
  private String descadicional;

  @Column(name = "MONTOTOTAL")
  private BigDecimal montototal;

  @JoinColumn(name = "EMPLEADOR", referencedColumnName = "ID")
  @OneToOne(optional = true, fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  private GanEmpleadorDdjj572 empleador;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "ajuste", fetch = FetchType.LAZY)
  private List<GanAjustesDetDdjj572> detalles;

}
