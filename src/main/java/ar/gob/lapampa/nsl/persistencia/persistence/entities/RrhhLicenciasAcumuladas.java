package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_LICENCIAS_ACUMULADAS")
@NamedQueries(value = {@NamedQuery(name = "RrhhLicenciasAcumuladas.findAll",
    query = "SELECT r FROM RrhhLicenciasAcumuladas r")})
public class RrhhLicenciasAcumuladas implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Column(name = "ANIO")
  private Short anio;
  @Column(name = "LICENCIA_ACUMULADA")
  private Long licenciaAcumulada;
  @Column(name = "OCU_ID")
  private Long ocuId;
  @JoinColumn(name = "LTI_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhLicenciasTipos ltiId;

  public RrhhLicenciasAcumuladas(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhLicenciasAcumuladas[ id=" + id + " ]";
  }

}
