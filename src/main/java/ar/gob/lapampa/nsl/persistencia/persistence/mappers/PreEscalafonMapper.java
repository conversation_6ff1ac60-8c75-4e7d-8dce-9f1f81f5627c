package ar.gob.lapampa.nsl.persistencia.persistence.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import ar.gob.lapampa.nsl.datatransfer.PreEscalafonesDTO;
import ar.gob.lapampa.nsl.persistencia.models.EscalafonModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.PreEscalafones;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PreEscalafonMapper {

    PreEscalafonMapper INSTANCE = Mappers.getMapper(PreEscalafonMapper.class);

    // TODO: Ignoramos todas la listas para evitar un dto ciclico ver de mejorar
    @Mapping(target = "usuarios", ignore = true)
    PreEscalafonesDTO preEscalafonToPreEscalafonDto(PreEscalafones preEscalafones);

    static EscalafonModel preEscalafonToEscalafonModel(PreEscalafones preEscalafones) {
        EscalafonModel model = new EscalafonModel(preEscalafones.getId());
        model.setNro(preEscalafones.getNro());
        model.setDescripcion(preEscalafones.getDescripcion());
        return model;
    }
}
