package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_OCUPACIONES_PART_POL")
@SequenceGenerator(name = "LIQ_OCU_PAR_POL_SEQ", sequenceName = "LIQ_OCU_PAR_POL_SEQ",
    allocationSize = 1)
public class LiqOcupacionesPartPol implements Serializable {

  private static final long serialVersionUID = 6358161832028312694L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_OCU_PAR_POL_SEQ")
  @Column(name = "ID")
  private Long id;

  @JoinColumn(name = "CAL_ID", referencedColumnName = "ID")
  @OneToOne(optional = false, fetch = FetchType.LAZY)
  private ParmCalPartPol calId;

  @JoinColumn(name = "DEL_ID", referencedColumnName = "ID")
  @OneToOne(optional = false, fetch = FetchType.LAZY)
  private ParmDelegPartPol delId;

  @JoinColumn(name = "PAR_POL_ID", referencedColumnName = "ID")
  @OneToOne(optional = false, fetch = FetchType.LAZY)
  private ParmPartidosPolitico parPolId;

  @Temporal(TemporalType.DATE)
  private Date fecha;

  @Temporal(TemporalType.DATE)
  @Column(name = "FECHA_ANULACION")
  private Date fechaAnulacion;

  @Temporal(TemporalType.DATE)
  @Column(name = "FECHA_APROBACION")
  private Date fechaAprobacion;

  @Temporal(TemporalType.DATE)
  @Column(name = "FECHA_BAJA")
  private Date fechaBaja;

  @Column(name = "IMPORTE_FIJO")
  private BigDecimal importeFijo;

  @JoinColumn(name = "OCU_ID", referencedColumnName = "ID")
  @OneToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhOcupaciones ocuId;

  @Column(name = "PORCENTAJE")
  private BigDecimal porcentaje;

  @Column(name = "ESTADO")
  private String estado;

}
