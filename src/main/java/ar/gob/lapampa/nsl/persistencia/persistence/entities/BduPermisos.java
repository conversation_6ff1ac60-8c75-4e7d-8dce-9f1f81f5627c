package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PERMISOS")
@NamedQueries(
    value = {@NamedQuery(name = "BduPermisos.findAll", query = "SELECT b FROM BduPermisos b")})
public class BduPermisos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "ACTIVO")
  private Character activo;
  @Size(max = 200)
  @Column(name = "MAPEO_LDAP")
  private String mapeoLdap;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "prmId", fetch = FetchType.LAZY)
  private Set<BduPermisosTablas> bduPermisosTablasSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "prmId", fetch = FetchType.LAZY)
  private Set<BduPerfilesPermisos> bduPerfilesPermisosSet;

  public BduPermisos(Long id) {
    this.id = id;
  }

  public BduPermisos(Long id, String descripcion) {
    this.id = id;
    this.descripcion = descripcion;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduPermisos[ id=" + id + " ]";
  }

}
