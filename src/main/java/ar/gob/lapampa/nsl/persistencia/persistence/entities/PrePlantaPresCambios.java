package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_PLANTA_PRES_CAMBIOS")
@NamedQueries(value = {@NamedQuery(name = "PrePlantaPresCambios.findAll",
    query = "SELECT p FROM PrePlantaPresCambios p")})
public class PrePlantaPresCambios implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected PrePlantaPresCambiosPK prePlantaPresCambiosPK;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "CDAD_ANTERIOR")
  private long cdadAnterior;
  @Column(name = "CDAD_RESERVADOS")
  private Long cdadReservados;
  @Column(name = "CDAD_OCU_EFECTIVOS")
  private Long cdadOcuEfectivos;
  @Column(name = "NOR_ID")
  private Long norId;

  @Size(max = 4000)
  @Column(name = "OBSERVACION")
  private String observacion;
  @Column(name = "CDAD_AMPLIADA")
  private Long cdadAmpliada;

  @Size(max = 1000)
  @Column(name = "USUARIO_MOV")
  private String usuarioMov;
  @Column(name = "FECHA_MOV")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaMov;
  @Column(name = "NOR_NRO")
  private Long norNro;
  @JoinColumn(name = "PCT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PrePlantaPresCambiosTipos pctId;
  @JoinColumn(name = "PPR_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PrePlantaPresupuestaria prePlantaPresupuestaria;

  public PrePlantaPresCambios(PrePlantaPresCambiosPK prePlantaPresCambiosPK) {
    this.prePlantaPresCambiosPK = prePlantaPresCambiosPK;
  }

  public PrePlantaPresCambios(PrePlantaPresCambiosPK prePlantaPresCambiosPK, Date fecha,
      long cdadAnterior) {
    this.prePlantaPresCambiosPK = prePlantaPresCambiosPK;
    this.fecha = fecha;
    this.cdadAnterior = cdadAnterior;
  }

  public PrePlantaPresCambios(long pprId, long item) {
    this.prePlantaPresCambiosPK = new PrePlantaPresCambiosPK(pprId, item);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.PrePlantaPresCambios[ prePlantaPresCambiosPK="
        + prePlantaPresCambiosPK + " ]";
  }

}
