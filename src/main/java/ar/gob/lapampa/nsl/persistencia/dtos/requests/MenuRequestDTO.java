package ar.gob.lapampa.nsl.persistencia.dtos.requests;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MenuRequestDTO {

  private Long id;
  @NotBlank(message = "El nombre del menu no puede estar vacío.")
  @Size(min = 3, max = 100, message = "El nombre del menu debe tener entre 3 y 100 caracteres.")
  private String nombre;

  @Size(max = 100, message = "El estado no puede exceder los 100 caracteres.")
  private String estado;

  private Long menuPadreId; // Solo recibe el Long Id

  @Size(max = 30, message = "El icono puede ser nulo. Maximo 30 caracteres")
  private String icono; // Ejemplo: "fas fa-clipboard"

  private Boolean abstracto;

}
