package ar.gob.lapampa.nsl.persistencia.dtos;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.querydsl.core.annotations.QueryProjection;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO;
import ar.gob.lapampa.nsl.datatransfer.PreEscalafonesDTO;
import ar.gob.lapampa.nsl.datatransfer.RrhhAccionesDTO;
import ar.gob.lapampa.nsl.datatransfer.RrhhEmpresasDTO;
import ar.gob.lapampa.nsl.datatransfer.RrhhMenuDTO;
import ar.gob.lapampa.nsl.datatransfer.RrhhRolesDTO;
import ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO;
import ar.gob.lapampa.nsl.utils.constantes.EstadoUsuario;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RrhhUsuarioDTO extends BaseRRHHDTO implements Serializable {

  private static final long serialVersionUID = 6825100916191992407L;
  private String login;
  private String password;
  private EstadoUsuario estado;
  private String token;
  private BduPersonasFisicasDTO prsId;
  private List<RrhhRolesDTO> roles;
  private List<PreEscalafonesDTO> convenios;
  private Map<Long, String[]> menuAutorizados;
  private Map<Long, String[]> estadosAutorizados;
  private Map<Long, String[]> empresasAutorizadas;
  private Map<Long, String[]> urlsAutorizadas;
  private String[] conveniosAutorizados;
  private char demo;
  private Date fechaBaja;
  private Boolean isAdmin = false;
  private String remoteHost;
  private Long rolActivo;

  public RrhhUsuarioDTO(String login) {
    super();
    this.login = login;
  }

  @QueryProjection
  public RrhhUsuarioDTO(Long id) {
    super();
    this.id = id;
  }

  public RrhhUsuarioDTO(EstadoUsuario estado) {
    super();
    this.estado = estado;
  }

  public RrhhUsuarioDTO(String login, EstadoUsuario estado) {
    super();
    this.login = login;
    this.estado = estado;
  }

  @Override
  public String getBeanName() {
    return RrhhUsuariosDTO.class.getName();
  }

  public void loadUrlsAutorizadas() {
    final Map<Long, String[]> mapa = new HashMap<>();
    final List<String> urlAutorizadas = new ArrayList<>();
    if (getRoles() != null && !getRoles().isEmpty()) {
      for (final RrhhRolesDTO rol : getRoles()) {
        if (rol.getAcciones() != null && !rol.getAcciones().isEmpty()) {
          for (final RrhhAccionesDTO accion : rol.getAcciones()) {
            urlAutorizadas.add(accion.getEndpoint());
          }
        }
        mapa.put(rol.getId(), urlAutorizadas.toArray(new String[0]));
        urlAutorizadas.clear();
      }
    }
    setUrlsAutorizadas(mapa);
  }

  public void loadMenuAutorizados() {
    final Map<Long, String[]> mapa = new HashMap<>();
    final List<String> menuAut = new ArrayList<>();
    if (getRoles() != null && !getRoles().isEmpty()) {
      for (final RrhhRolesDTO rol : getRoles()) {
        if (rol.getMenues() != null && !rol.getMenues().isEmpty()) {
          for (final RrhhMenuDTO menu : rol.getMenues()) {
            menuAut.add(menu.getNombre());
          }
        }
        mapa.put(rol.getId(), menuAut.toArray(new String[0]));
        menuAut.clear();
      }
    }
    setMenuAutorizados(mapa);
  }

  public void loadEstadosAutorizados() {
    final Map<Long, String[]> mapa = new HashMap<>();
    final List<String> estadosAut = new ArrayList<>();
    if (getRoles() != null && !getRoles().isEmpty()) {
      for (final RrhhRolesDTO rol : getRoles()) {
        if (rol.getMenues() != null && !rol.getMenues().isEmpty()) {
          for (final RrhhMenuDTO menu : rol.getMenues()) {
            estadosAut.add(menu.getEstado());
          }
        }
        mapa.put(rol.getId(), estadosAut.toArray(new String[0]));
        estadosAut.clear();
      }
    }
    setEstadosAutorizados(mapa);
  }

  public void loadEmpresasAutorizadas() {
    final Map<Long, String[]> mapa = new HashMap<>();
    final List<String> empresasAut = new ArrayList<>();
    if (getRoles() != null && !getRoles().isEmpty()) {
      for (final RrhhRolesDTO rol : getRoles()) {
        if (rol.getEmpresas() != null && !rol.getEmpresas().isEmpty()) {
          for (final RrhhEmpresasDTO empresa : rol.getEmpresas()) {
            empresasAut.add(empresa.getId().toString());
          }
        }
        mapa.put(rol.getId(), empresasAut.toArray(new String[0]));
        empresasAut.clear();
      }
    }
    setEmpresasAutorizadas(mapa);
  }

  public void loadIsAdmin() {
    for (final RrhhRolesDTO rol : roles) {
      if (rol.getId() == 1) {
        setIsAdmin(true);
        break;
      }
    }
  }

  public Long loadRolActivo() {
    setRolActivo(getRoles().size() > 0 ? getRoles().get(0).getId() : null);
    return getRolActivo();
  }

  public String[] loadUrlsAutorizadasRol(Long indice) {
    return indice != null ? getUrlsAutorizadas().get(indice) : null;
  }

  public String[] loadEmpresasAutorizadasRol(Long indice) {
    return indice != null ? getEmpresasAutorizadas().get(indice) : null;
  }

  public void loadConveniosAutorizados() {
    final List<String> conveniosAut = new ArrayList<>();
    if (getConvenios() != null && !getConvenios().isEmpty()) {
      for (final PreEscalafonesDTO convenio : getConvenios()) {
        conveniosAut.add(convenio.getId().toString());
      }
    }
    setConveniosAutorizados(conveniosAut.toArray(new String[0]));
  }

}

