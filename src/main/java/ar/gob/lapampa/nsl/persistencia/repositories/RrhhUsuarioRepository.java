package ar.gob.lapampa.nsl.persistencia.repositories;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import ar.gob.lapampa.nsl.persistencia.models.UsuarioModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhUsuario;

public interface RrhhUsuarioRepository
    extends JpaRepository<RrhhUsuario, Long>, QuerydslPredicateExecutor<RrhhUsuario> {

  @Query(value = """
          SELECT
          	ru.ID,
              ru.LOGIN,
              CASE\s
                  WHEN ru.Demo = 'N' THEN 'No'\s
                  ELSE 'Si' \s
              END AS DEMO,
              bpf.NOMBRES,
              bpf.APELLIDO,    \s
              CASE\s
                  WHEN nu.ID IS NULL THEN 'No'\s
                  ELSE 'Si' \s
              END AS EN_NNSL
          FROM LAPAMPA.RRHH_USUARIOS ru
          JOIN LAPAMPA.BDU_PERSONAS_FISICAS bpf ON ru.PRS_ID = bpf.ID
          LEFT JOIN LAPAMPA.NSL_USUARIOS nu ON nu.USERNAME = ru.LOGIN
      """, nativeQuery = true)
  List<UsuarioModel> encontrarRapido();

  @Procedure(procedureName = "LAPAMPA.SYNC_RRHH_USUARIOS_ROLES")
  String syncUsuariosRoles(@Param("p_rrhh_rol_id") Long rrhhRolId,
      @Param("p_usuario_list") String usuariosList);
}

