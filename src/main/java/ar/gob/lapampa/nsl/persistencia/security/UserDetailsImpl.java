package ar.gob.lapampa.nsl.persistencia.security;

import java.io.IOException;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class UserDetailsImpl implements UserDetails {
  private static final long serialVersionUID = 1L;

  private Long id;

  private String username;

  private String email;

  @JsonIgnore
  private String password;

  private Collection<? extends GrantedAuthority> authorities;


  public static UserDetailsImpl build(String user) {
    UserDetailsImpl userDetails = new UserDetailsImpl();
    ObjectMapper mapper = new ObjectMapper();
    JsonFactory factory = mapper.getFactory();
    JsonParser jsonParser = null;
    try {
      jsonParser = factory.createParser(user);
      JsonNode node = mapper.readTree(jsonParser);
      List<JsonNode> lista = node.findValues("authority");
      Collection<SimpleGrantedAuthority> listaauth = new HashSet<>();
      lista.forEach(n -> listaauth.add(new SimpleGrantedAuthority(n.asText())));
      userDetails.setAuthorities(listaauth);
      userDetails.setEmail(node.findValue("email").asText());
      userDetails.setId(node.findValue("id").asLong());
      userDetails.setUsername(node.findValue("username").asText());
    } catch (IOException e) {
      e.printStackTrace();
    }

    return userDetails;
  }

  @Override
  public Collection<? extends GrantedAuthority> getAuthorities() {
    return authorities;
  }

  @Override
  public boolean isAccountNonExpired() {
    return true;
  }

  @Override
  public boolean isAccountNonLocked() {
    return true;
  }

  @Override
  public boolean isCredentialsNonExpired() {
    return true;
  }

  @Override
  public boolean isEnabled() {
    return true;
  }

}
