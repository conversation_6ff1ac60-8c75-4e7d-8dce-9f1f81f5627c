package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_OTROS_MEDIOS_PRE")
@NamedQueries(value = {
    @NamedQuery(name = "BduOtrosMediosPre.findAll", query = "SELECT b FROM BduOtrosMediosPre b")})
public class BduOtrosMediosPre implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "VALOR", length = 10)
  private String valor;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ORP_ID")
  private long orpId;
  @JoinColumn(name = "OMT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduOtrosMediosTipos omtId;

  public BduOtrosMediosPre(Long id) {
    this.id = id;
  }

  public BduOtrosMediosPre(Long id, String valor, long orpId) {
    this.id = id;
    this.valor = valor;
    this.orpId = orpId;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduOtrosMediosPre[ id=" + id + " ]";
  }

}
