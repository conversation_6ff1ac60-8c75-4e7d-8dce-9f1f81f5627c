package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ORGANISMOS_ORG")
@NamedQueries(value = {
    @NamedQuery(name = "BduOrganismosOrg.findAll", query = "SELECT b FROM BduOrganismosOrg b")})
public class BduOrganismosOrg implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "CODIGO")
  private Short codigo;
  @Column(name = "NORMATIVA")
  private Long normativa;
  @Column(name = "DMC_ID")
  private Long dmcId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDesde;
  @Column(name = "ORO_ID_BASE")
  private Long oroIdBase;
  @OneToMany(mappedBy = "oroId", fetch = FetchType.LAZY)
  private Set<BduOrganismosMapeos> bduOrganismosMapeosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "oroId", fetch = FetchType.LAZY)
  private Set<BduOrgOroTelefonos> bduOrgOroTelefonosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "oroId", fetch = FetchType.LAZY)
  private Set<BduOtrosMediosOro> bduOtrosMediosOroSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduOrganismosOrg", fetch = FetchType.LAZY)
  private Set<BduOrgOroVinRet> bduOrgOroVinRetSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduOrganismosOrg1", fetch = FetchType.LAZY)
  private Set<BduOrgOroVinRet> bduOrgOroVinRetSet1;
  @JoinColumn(name = "NVL_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduNiveles nvlId;
  @OneToMany(mappedBy = "oroId", fetch = FetchType.LAZY)
  private Set<BduOrganismosOrg> bduOrganismosOrgSet;
  @JoinColumn(name = "ORO_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduOrganismosOrg oroId;
  @JoinColumn(name = "ODT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduOrigenesDatos odtId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "oroId", fetch = FetchType.LAZY)
  private Set<BduOrgOroMails> bduOrgOroMailsSet;

  public BduOrganismosOrg(Long id) {
    this.id = id;
  }

  public BduOrganismosOrg(Long id, Character activo, Date fechaDesde) {
    this.id = id;
    this.activo = activo;
    this.fechaDesde = fechaDesde;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduOrganismosOrg[ id=" + id + " ]";
  }

}
