package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_PRESENT_DDJJ572")
@SequenceGenerator(name = "GAN_PRESENT_DDJJ572_SQ", sequenceName = "GAN_PRESENT_DDJJ572_SQ",
    allocationSize = 1)
public class GanPresentDdjj572 implements Serializable {

  private static final long serialVersionUID = 6842565333876479163L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_PRESENT_DDJJ572_SQ")
  private Long id;

  @JoinColumn(name = "EMPLEADO", referencedColumnName = "ID")
  @OneToOne(optional = false, fetch = FetchType.EAGER, cascade = CascadeType.ALL)
  private GanEmpleadoDdjj572 empleado;

  @JoinColumn(name = "AGENTERETENCION", referencedColumnName = "ID")
  @OneToOne(optional = true, fetch = FetchType.EAGER, cascade = CascadeType.ALL)
  private GanEmpleadorDdjj572 agenteRetencion;

  @Temporal(TemporalType.DATE)
  private Date fechapresentacion;

  @JoinColumn(name = "LOTE", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER, cascade = CascadeType.ALL)
  private GanLoteDdjj572 lote;

  @Column(name = "NROPRESENTACION")
  private String nropresentacion;

  @Column(name = "PERIODO")
  private String periodo;

  @Column(name = "XML_NOMBRE")
  private String xmlNombre;

  @Column(name = "XML_CONTENIDO")

  private String xmlContenido;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "presentacion")
  private List<GanCargfamDdjj572> cargasFamilia;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "presentacion")
  private List<GanPluriempDdjj572> ganLiqOtrosEmpEnt;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "presentacion")
  private List<GanDeduccionDdjj572> deducciones;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "presentacion")
  private List<GanRetperpagosDdjj572> retPerPagos;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "presentacion")
  private List<GanAjustesDdjj572> ajustes;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "presentacion")
  private List<GanDtosadicDdjj572> datosAdicionales;

  public GanPresentDdjj572(Long id) {
    this.id = id;
  }

}
