package ar.gob.lapampa.nsl.persistencia.dtos.requests;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import ar.gob.lapampa.nsl.persistencia.models.UsuarioModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SyncUsuariosRequestDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 4023764305514935532L;
  private List<UsuarioModel> usuarios;

}
