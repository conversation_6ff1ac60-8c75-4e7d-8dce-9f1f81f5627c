package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_PROCESOS")
@SequenceGenerator(name = "LIQ_PRC_SEQ", sequenceName = "LIQ_PRC_SEQ", allocationSize = 1)
public class LiqProcesos implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_PRC_SEQ")
  private Long id;

  @Column(name = "FECHA_INICIO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaInicio;

  @Column(name = "FECHA_FIN")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaFin;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "MES")
  private Short mes;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANIO")
  private Short anio;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_CIERRE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaCierre;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "TIPO_PROCESO")
  private Character tipoProceso;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_CIERRE_ANTERIOR")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaCierreAnterior;

  @Basic(optional = false)
  @Column(name = "SELECTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean selectivo;

  @Basic(optional = false)
  @Column(name = "OFICIAL")
  @Convert(converter = YesNoConverter.class)
  private Boolean oficial;

  @Basic(optional = false)
  @Column(name = "OMNIBUS")
  @Convert(converter = YesNoConverter.class)
  private Boolean omnibus;

  @Column(name = "ESTADO")
  private Character estado;

  @Size(max = 3)
  @Column(name = "TIPO_LIQUIDACION_SELECTIVA")
  private String tipoLiquidacionSelectiva;

  @Size(max = 4000)
  @Column(name = "VALORES_LIQUIDACION_SELECTIVA")
  private String valoresLiquidacionSelectiva;

  @Size(max = 3)
  @Column(name = "TIPO_LIQ_SELECTIVA_ORIGINAL")
  private String tipoLiqSelectivaOriginal;

  @Size(max = 4000)
  @Column(name = "VALORES_LIQ_SELECTIVA_ORIGINAL")
  private String valoresLiqSelectivaOriginal;

  @OneToMany(mappedBy = "prcId", fetch = FetchType.LAZY)
  private Set<LiqOcuSitRev> liqOcuSitRevSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "prcId", fetch = FetchType.LAZY)
  private Set<LiqPersonasAReliq> liqPersonasAReliqSet;

  @OneToMany(mappedBy = "prcId", fetch = FetchType.LAZY)
  private Set<LiqOcupacionesRecibos> liqOcupacionesRecibosSet;

  @OneToMany(mappedBy = "prcId", fetch = FetchType.LAZY)
  private Set<LiqTotalesMesPersona> liqTotalesMesPersonaSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "prcId", fetch = FetchType.LAZY)
  private Set<LiqTotalesResumenProg> liqTotalesResumenProgSet;

  @OneToMany(mappedBy = "prcId", fetch = FetchType.LAZY)
  private Set<LiqComplementarias> liqComplementariasSet;

  @OneToMany(mappedBy = "prcId", fetch = FetchType.LAZY)
  private Set<LiqIncompCodPersona> liqIncompCodPersonaSet;

  @OneToMany(mappedBy = "prcId", fetch = FetchType.LAZY)
  private Set<LiqTotalesMesOcupaciones> liqTotalesMesOcupacionesSet;

  @OneToMany(mappedBy = "prcId", fetch = FetchType.LAZY)
  private Set<LiqProcesosPasos> liqProcesosPasosSet;

  @JoinColumn(name = "LIT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private LiqLiquidacionesTipos litId;

  @OneToMany(mappedBy = "prcId", fetch = FetchType.LAZY)
  private Set<LiqProcesos> liqProcesosSet;

  @JoinColumn(name = "PRC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqProcesos prcId;

  @OneToMany(mappedBy = "prcViejoId", fetch = FetchType.LAZY)
  private Set<LiqRecalculosProcesos> liqRecalculosProcesosSet;

  @OneToMany(mappedBy = "prcNuevoId", fetch = FetchType.LAZY)
  private Set<LiqRecalculosProcesos> liqRecalculosProcesosSet1;

  @OneToMany(mappedBy = "prcPadreId", fetch = FetchType.LAZY)
  private Set<LiqRecalculosDetalle> liqRecalculosDetalleSet;

  @OneToMany(mappedBy = "prcViejoId", fetch = FetchType.LAZY)
  private Set<LiqRecalculosDetalle> liqRecalculosDetalleSet1;

  @OneToMany(mappedBy = "prcNuevoId", fetch = FetchType.LAZY)
  private Set<LiqRecalculosDetalle> liqRecalculosDetalleSet2;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "prcId", fetch = FetchType.LAZY)
  private Set<LiqArbolPresup> liqArbolPresupSet;

  @OneToMany(mappedBy = "prcId", fetch = FetchType.LAZY)
  private List<LiqProcesosSetCodigos> liqProcesosSetCodigosSet;

  @Column(name = "FECHA_ACREDITACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAcreditacion;

  public LiqProcesos(Long id) {
    this.id = id;
  }

  public LiqProcesos(Long id, Date fechaCierreAnterior) {
    this.id = id;
    this.fechaCierreAnterior = fechaCierreAnterior;
  }

  public LiqProcesos(Long id, Short mes, Short anio, Date fechaCierre, Character tipoProceso,
      Date fechaCierreAnterior) {
    this.id = id;
    this.mes = mes;
    this.anio = anio;
    this.fechaCierre = fechaCierre;
    this.tipoProceso = tipoProceso;
    this.fechaCierreAnterior = fechaCierreAnterior;
  }

}
