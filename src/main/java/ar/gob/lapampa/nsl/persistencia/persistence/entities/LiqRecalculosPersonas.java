package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_RECALCULOS_PERSONAS")
@NamedQueries(value = {@NamedQuery(name = "LiqRecalculosPersonas.findAll",
    query = "SELECT l FROM LiqRecalculosPersonas l")})
public class LiqRecalculosPersonas implements Serializable {
  private static final long serialVersionUID = 1L;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private BigDecimal id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRS_ID")
  private BigInteger prsId;
  @Column(name = "MES")
  private BigInteger mes;
  @Column(name = "ANIO")
  private BigInteger anio;
  @Size(max = 80)
  @Column(name = "TABLA")
  private String tabla;
  @Column(name = "TABLA_ID")
  private BigInteger tablaId;
  @Column(name = "FECHA_VIG_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigDesde;
  @Column(name = "FECHA_VIG_HASTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigHasta;
  @Column(name = "ACTIVO")
  private Character activo;

  public LiqRecalculosPersonas(BigDecimal id) {
    this.id = id;
  }

  public LiqRecalculosPersonas(BigDecimal id, BigInteger prsId) {
    this.id = id;
    this.prsId = prsId;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqRecalculosPersonas[ id=" + id + " ]";
  }

}
