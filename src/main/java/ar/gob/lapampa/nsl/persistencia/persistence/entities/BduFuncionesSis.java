package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_FUNCIONES_SIS")
@NamedQueries(value = {
    @NamedQuery(name = "BduFuncionesSis.findAll", query = "SELECT b FROM BduFuncionesSis b")})
public class BduFuncionesSis implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "NIVEL")
  private Short nivel;
  @Size(max = 200)
  @Column(name = "OUTCOME")
  private String outcome;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PUBLICO")
  private Character publico;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "fsiId", fetch = FetchType.LAZY)
  private Set<BduUsuarioswsFsi> bduUsuarioswsFsiSet;
  @OneToMany(mappedBy = "fsiId", fetch = FetchType.LAZY)
  private Set<BduFuncionesMenu> bduFuncionesMenuSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduFuncionesSis", fetch = FetchType.LAZY)
  private Set<BduFuncionesPerfiles> bduFuncionesPerfilesSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduFuncionesSis", fetch = FetchType.LAZY)
  private Set<BduFuncAccPerfiles> bduFuncAccPerfilesSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "fsiVueltaId", fetch = FetchType.LAZY)
  private Set<BduFuncAccionesSis> bduFuncAccionesSisSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "fsiId", fetch = FetchType.LAZY)
  private Set<BduFuncAccionesSis> bduFuncAccionesSisSet1;
  @JoinColumn(name = "SIS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduSistemas sisId;

  public BduFuncionesSis(Long id) {
    this.id = id;
  }

  public BduFuncionesSis(Long id, Character publico) {
    this.id = id;
    this.publico = publico;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduFuncionesSis[ id=" + id + " ]";
  }

}
