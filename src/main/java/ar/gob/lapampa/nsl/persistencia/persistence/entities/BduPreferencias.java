package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PREFERENCIAS")
@NamedQueries(value = {
    @NamedQuery(name = "BduPreferencias.findAll", query = "SELECT b FROM BduPreferencias b")})
public class BduPreferencias implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 20)
  @Column(name = "VALOR")
  private String valor;

  public BduPreferencias(Long id) {
    this.id = id;
  }

  public BduPreferencias(Long id, String descripcion, String valor) {
    this.id = id;
    this.descripcion = descripcion;
    this.valor = valor;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduPreferencias[ id=" + id + " ]";
  }

}
