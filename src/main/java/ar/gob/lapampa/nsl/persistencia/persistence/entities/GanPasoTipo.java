package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_PASOS_TIPOS")
@SequenceGenerator(name = "GAN_PASOS_TIPOS_SQ", sequenceName = "GAN_PASOS_TIPOS_SQ",
    allocationSize = 1)
public class GanPasoTipo implements Serializable {

  private static final long serialVersionUID = 2565917648990866089L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_LOTE_DDJJ572_SQ")
  private Long id;

  @Column(name = "CODIGO")
  private String codigo;

  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Column(name = "MODALIDAD")
  private Long modalidad;

  @Column(name = "ACUMULADOR")
  private Long acumulador;

  @Column(name = "FUNCION")
  private String funcion;

}

