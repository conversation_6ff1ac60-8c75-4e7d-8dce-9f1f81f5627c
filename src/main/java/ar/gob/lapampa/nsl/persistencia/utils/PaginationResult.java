package ar.gob.lapampa.nsl.persistencia.utils;

import java.util.Collection;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Clase utilizada para pasar listados paginados entre capas
 *
 * @param <T>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PaginationResult<T> {

  private Collection<T> listOfResult;
  private int size;

}
