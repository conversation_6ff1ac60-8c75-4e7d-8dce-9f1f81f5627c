package ar.gob.lapampa.nsl.persistencia.persistence.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import ar.gob.lapampa.nsl.persistencia.dtos.RrhhAccionDTO;
import ar.gob.lapampa.nsl.persistencia.models.AccionModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhAcciones;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RrhhAccionMapper {

  RrhhAccionMapper INSTANCE = Mappers.getMapper(RrhhAccionMapper.class);

  RrhhAccionDTO rrhhAccionToRrhhAccionesDto(RrhhAcciones accion);

  static AccionModel rrhhAccionToRrhhAccionModel(RrhhAcciones accion) {
    AccionModel model = new AccionModel(accion.getId());
    model.setNombre(accion.getNombre());
    model.setEndpoint(accion.getEndpoint());
    model.setNota(accion.getNota());
    return model;
  }

}
