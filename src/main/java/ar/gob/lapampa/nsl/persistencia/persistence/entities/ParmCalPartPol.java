package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PARM_CAL_PART_POL")
public class ParmCalPartPol implements Serializable {

  private static final long serialVersionUID = -7848703850760915406L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Column(name = "ACTIVO")
  private String activo;

  @Column(name = "DESCRIPCION")
  private String descripcion;

  // @OneToMany(cascade = CascadeType.ALL, mappedBy = "calId", fetch = FetchType.LAZY)
  // private Set<LiqOcupacionesPartPol> liqOcupacionesPartPol

}
