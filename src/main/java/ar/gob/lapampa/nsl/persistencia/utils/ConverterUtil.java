package ar.gob.lapampa.nsl.persistencia.utils;

import java.util.List;
import ar.gob.lapampa.nsl.utils.constantes.Constantes;

public final class ConverterUtil {

  private ConverterUtil() {
    throw new IllegalStateException("Utility class");
  }

  public static final String DTO_PACKAGE = "ar.gob.lapampa.nsl.datatransfer";

  public static String convertListIdToString(List<Long> lista) {

    final StringBuffer ids = new StringBuffer();

    String separador = "";
    for (final Long id : lista) {

      ids.append(separador).append(id);
      separador = Constantes.SEPARADOR_COMA;

    }

    return ids.toString();
  }


  public static Class<?> mapEntityToClass(String className) {
    Class<?> c = null;
    final StringBuffer canonicalName = new StringBuffer(className);
    try {
      c = Class.forName(canonicalName.toString());
    } catch (final ClassNotFoundException e) {
      e.printStackTrace();
    }

    return c;
  }

  public static Object mapNameToObject(String packageName, String entityName) {
    Class<?> c = null;
    Object result = null;
    final StringBuilder canonicalName = new StringBuilder();

    canonicalName.append(packageName).append('.').append(entityName);
    try {
      c = Class.forName(canonicalName.toString());
      result = c.getDeclaredConstructor().newInstance();
    } catch (final Exception ex) {
      ex.printStackTrace();
    }

    return result;
  }

  public static String stringParaBusquedaSQL(String valor) {
    return "%" + valor.toUpperCase().replace(" ", "%") + "%";
  }

  public static String getStringFromObject(Object objeto) {
    String resultado = "";

    if (objeto != null) {
      resultado = objeto.toString();
    }
    return resultado;
  }

}
