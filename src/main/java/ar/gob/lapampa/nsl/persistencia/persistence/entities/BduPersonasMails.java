package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PERSONAS_MAILS")
@NamedQueries(value = {
    @NamedQuery(name = "BduPersonasMails.findAll", query = "SELECT b FROM BduPersonasMails b")})
public class BduPersonasMails implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected BduPersonasMailsPK bduPersonasMailsPK;
  @JoinColumn(name = "MAI_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduMail bduMail;

  public BduPersonasMails(BduPersonasMailsPK bduPersonasMailsPK) {
    this.bduPersonasMailsPK = bduPersonasMailsPK;
  }

  public BduPersonasMails(long prsId, long maiId) {
    this.bduPersonasMailsPK = new BduPersonasMailsPK(prsId, maiId);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduPersonasMails[ bduPersonasMailsPK="
        + bduPersonasMailsPK + " ]";
  }

}
