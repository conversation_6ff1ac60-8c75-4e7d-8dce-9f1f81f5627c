package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_ANTIGUEDADES_PREGASE")
@NamedQueries(value = {@NamedQuery(name = "LiqAntiguedadesPregase.findAll",
    query = "SELECT l FROM LiqAntiguedadesPregase l")})
public class LiqAntiguedadesPregase implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRS_ID")
  private long prsId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANIO")
  private short anio;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MES")
  private short mes;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANT_ANIOS")
  private short antAnios;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANT_MESES")
  private short antMeses;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANT_DIAS")
  private short antDias;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "TIPO")
  private short tipo;

  public LiqAntiguedadesPregase(Long id) {
    this.id = id;
  }

  public LiqAntiguedadesPregase(Long id, long prsId, short anio, short mes, short antAnios,
      short antMeses, short antDias, short tipo) {
    this.id = id;
    this.prsId = prsId;
    this.anio = anio;
    this.mes = mes;
    this.antAnios = antAnios;
    this.antMeses = antMeses;
    this.antDias = antDias;
    this.tipo = tipo;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqAntiguedadesPregase[ id=" + id + " ]";
  }

}
