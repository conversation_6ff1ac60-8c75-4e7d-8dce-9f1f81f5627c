package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * The persistent class for the PRE_COMBINACION_PRESUPUESTARIA database table.
 * 
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_CARGOS_PRESUPUESTARIOS")
@SequenceGenerator(name = "PRE_CARGOS_PRESUPUESTARIOS_SEQ",
    sequenceName = "PRE_CARGOS_PRESUPUESTARIOS_SEQ", allocationSize = 1)

public class PreCargosPresupuestarios implements Serializable {

  private static final long serialVersionUID = 4405560716596638602L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PRE_CARGOS_PRESUPUESTARIOS_SEQ")
  private Long id;

  @JoinColumn(name = "UNICAT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private PreUnidadOrgCat unicatId; //

  @Temporal(TemporalType.DATE)
  @Column(name = "FECHA_VIG_DESDE")
  private Date fechaVigDesde;

  @Temporal(TemporalType.DATE)
  @Column(name = "FECHA_VIG_HASTA")
  private Date fechaVigHasta;
  private String activo;
  private Long cantidad;

}
