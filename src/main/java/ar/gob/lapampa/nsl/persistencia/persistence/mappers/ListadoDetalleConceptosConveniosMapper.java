package ar.gob.lapampa.nsl.persistencia.persistence.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import ar.gob.lapampa.nsl.datatransfer.response.ListadoDetalleConceptosConveniosResponseDTO;
import ar.gob.lapampa.nsl.persistencia.models.ListadoDetalleConceptosConveniosModel;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ListadoDetalleConceptosConveniosMapper {

    ListadoDetalleConceptosConveniosMapper INSTANCE = Mappers.getMapper(ListadoDetalleConceptosConveniosMapper.class);

    static ListadoDetalleConceptosConveniosModel listadoDetalleToModel(ListadoDetalleConceptosConveniosResponseDTO detalle) {
        ListadoDetalleConceptosConveniosModel model = new ListadoDetalleConceptosConveniosModel();
        model.setCodigoNro(detalle.getCodNro());
        model.setHaberes(detalle.getHaberes());
        model.setDescuentos(detalle.getDescuentos());
        model.setConcepto(detalle.getConcepto());
        model.setConvenioNombre(detalle.getConvenioNombre());
        model.setConvenioNro(detalle.getConvenioNro());
        model.setCodigoEmpresa(detalle.getCodigoEmpresa());
        model.setDescripcionEmpresa(detalle.getDescripcionEmpresa());
        model.setApellido(detalle.getApellido());
        model.setAfiliado(detalle.getAfiliado());
        model.setNombre(detalle.getNombre());
        model.setOcuId(detalle.getOcuId());
        model.setAnioRetroactivo(detalle.getAnioRetroactivo());
        model.setMesRetroactivo(detalle.getMesRetroactivo());
        return model;
    }
}
