package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_PLURIEMP_DDJJ572")
@SequenceGenerator(name = "GAN_PLURIEMP_DDJJ572_SQ", sequenceName = "GAN_PLURIEMP_DDJJ572_SQ",
    allocationSize = 1)
public class GanPluriempDdjj572 implements Serializable {

  private static final long serialVersionUID = 7811685935428434824L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_PLURIEMP_DDJJ572_SQ")
  private Long id;

  @JoinColumn(name = "EMPLEADOR", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER, cascade = CascadeType.ALL)
  private GanEmpleadorDdjj572 empleador;

  @Column(name = "GANBRUT")
  private BigDecimal ganbrut;

  @Column(name = "HORASEXTGR")
  private BigDecimal horasextgr;

  @Column(name = "MES")
  private Long mes;

  @Column(name = "OBRASOC")
  private BigDecimal obrasoc;

  @JoinColumn(name = "PRESENTACION", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER, cascade = CascadeType.ALL)
  private GanPresentDdjj572 presentacion;

  @Column(name = "AJUSTE")
  private BigDecimal ajuste;

  @Column(name = "RETGAN")
  private BigDecimal retgan;

  @Column(name = "RETRIBNOHAB")
  private BigDecimal retribnohab;

  @Column(name = "SAC")
  private BigDecimal sac;

  @Column(name = "SEGSOC")
  private BigDecimal segsoc;

  @Column(name = "SIND")
  private BigDecimal sind;

  @Column(name = "MATDID")
  private BigDecimal matdid;

  @Column(name = "EXENOALC")
  private BigDecimal exenoalc;

}
