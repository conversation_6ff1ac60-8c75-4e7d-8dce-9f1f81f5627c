package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class LiqCodigosNormasPK implements Serializable {

  private static final long serialVersionUID = -2419742477631623991L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "COD_ID")
  private long codId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "NOR_ID")
  private long norId;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqCodigosNormasPK[ codId=" + codId + ", norId="
        + norId + " ]";
  }

}
