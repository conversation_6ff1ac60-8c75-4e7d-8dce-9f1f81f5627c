package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_MIGRACION_SAGE_ERRORES")
@NamedQueries(value = {@NamedQuery(name = "LiqMigracionSageErrores.findAll",
    query = "SELECT l FROM LiqMigracionSageErrores l")})
public class LiqMigracionSageErrores implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 100)
  @Column(name = "ETAPA")
  private String etapa;
  @Size(max = 100)
  @Column(name = "TABLA")
  private String tabla;
  @Column(name = "TABLA_ID")
  private Long tablaId;
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;
  @Size(max = 50)
  @Column(name = "TIPO_LOG")
  private String tipoLog;
  @Size(max = 4000)
  @Column(name = "ERROR_MENSAJE")
  private String errorMensaje;

  public LiqMigracionSageErrores(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqMigracionSageErrores[ id=" + id + " ]";
  }

}
