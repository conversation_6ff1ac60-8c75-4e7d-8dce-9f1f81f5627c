package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_CUOTAS_ALIMENTARIAS")
@SequenceGenerator(name = "LIQ_CUA_SEQ", sequenceName = "LIQ_CUA_SEQ", allocationSize = 1)
public class LiqCuotasAlimentarias implements Serializable {

  private static final long serialVersionUID = 6276189113838425402L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_CUA_SEQ")
  private Long id;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduPersonasFisicas prsId;

  @JoinColumn(name = "PRS_BEN_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduPersonasFisicas prsBenId;

  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;

  @Column(name = "FECHA_INICIO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaInicio;

  @Column(name = "FECHA_FIN")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaFin;

  @Column(name = "MONTO_FIJO")
  private Double montoFijo;

  @Column(name = "PORC_MENSUAL")
  private Double porcMensual;

  @Size(max = 4000)
  @Column(name = "OBSERVACION")
  private String observacion;

  @Size(max = 200)
  @Column(name = "NOM_BENEF")
  private String nomBenef;

  @Size(max = 200)
  @Column(name = "APE_BENEF")
  private String apeBenef;

  @Column(name = "NRO_OFICIO")
  private String nroOficio;

  @Column(name = "PORC_SAC")
  private Double porcSac;

  @Column(name = "MONTO_SAC")
  private Double montoSac;

  @Column(name = "MONTO_MINIMO")
  private Double montoMinimo;

  @Column(name = "INCLUYE_AF")
  @Convert(converter = YesNoConverter.class)
  private Boolean incluyeAf;

  @Column(name = "INCLUYE_SAC")
  @Convert(converter = YesNoConverter.class)
  private Boolean incluyeSac;

  @Size(max = 1)
  @Column(name = "ESTADO")
  private String estado;

  @JoinColumn(name = "EMP_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhEmpresa empId;

  @JoinColumn(name = "COD_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqCodigos codId;

  @Column(name = "CARATULA")
  private String caratula;

  @Column(name = "EXPEDIENTE")
  private String expediente;

  @Column(name = "JUEZ")
  private String juez;

  @Column(name = "SECRETARIA")
  private String secretaria;

  @Column(name = "CUENTA_BANCO")
  private String cuentaBanco;

  @Column(name = "CUENTA_BANCO_DENOMINACION")
  private String cuentaBancoDenominacion;

  @Column(name = "DESCRIPCION_HAB_REC")
  private String descripcionHabRec;

  @Column(name = "CBU_DESTINATARIO")
  private String cbuDestinatario;

  @Basic(optional = false)
  @Column(name = "FECHA_NOTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaNotaOficio;

  @JoinTable(name = "LIQ_CUOTAS_ALIM_VINCULOS",
      joinColumns = {@JoinColumn(name = "CUA_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "VNC_ID", referencedColumnName = "ID")})
  @ManyToMany(fetch = FetchType.LAZY)
  private List<BduVinculos> liqCuotasAlimVinculosList;

  @JoinColumn(name = "TCC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqTipoCalculoCuotasAlimentarias tccId;

  @JoinColumn(name = "CIR_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqCircunscripciones cirId;

  @JoinColumn(name = "JUD_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqJuzgadosDefensorias judId;

  @JoinColumn(name = "BAS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqBancosSucursales basId;

  @JoinColumn(name = "FOP_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqFormasPago fopId;

  public LiqCuotasAlimentarias(Long id) {
    this.id = id;
  }

}
