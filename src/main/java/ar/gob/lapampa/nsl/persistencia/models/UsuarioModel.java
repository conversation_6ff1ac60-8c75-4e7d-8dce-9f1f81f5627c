package ar.gob.lapampa.nsl.persistencia.models;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import org.hibernate.annotations.Formula;
import com.querydsl.core.annotations.QueryProjection;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import ar.gob.lapampa.nsl.persistencia.exceptions.EdadException;
import ar.gob.lapampa.nsl.utils.constantes.EstadoUsuario;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UsuarioModel extends BaseRRHHDTO implements Serializable {

  private static final long serialVersionUID = 6825100916191992407L;
  private String login;
  private String nombres;
  private String apellidos;
  private EstadoUsuario estado;
  private Long prsId;
  private String demo;
  private Date fechaBaja;
  private Date fechaNacimiento;
  private String enNsl;
  private List<RolModel> roles;

  @Formula("timestampdiff('year', fechaNacimiento, now())")
  private Integer edad;

  public UsuarioModel(String login) {
    super();
    this.login = login;
  }

  @QueryProjection
  public UsuarioModel(Long id) {
    super();
    this.id = id;
  }

  @QueryProjection
  public UsuarioModel(BigDecimal id, String login, String demo, String nombres, String apellidos, String enNsl) {
    super();
    this.id = id.longValue();
    this.login = login;
    this.demo = demo;
    this.nombres = nombres;
    this.apellidos = apellidos;
    this.enNsl = enNsl;
  }

  public UsuarioModel(EstadoUsuario estado) {
    super();
    this.estado = estado;
  }

  public UsuarioModel(final String login, final EstadoUsuario estado) {
    super();
    this.login = login;
    this.estado = estado;
  }

  public void setFechaNacimiento(final Date fnac) {
    if (fnac != null) {
      this.fechaNacimiento = fnac;
      final LocalDate hoy = LocalDate.now();
      final LocalDate nacimiento = fnac.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
      this.edad = (int) ChronoUnit.YEARS.between(nacimiento, hoy);
    }
  }

  public void setEdad(Integer edad) throws EdadException {
    throw new EdadException("No permitido. Cambie fecha Nacimiento");
  }

  public EstadoUsuario getEstado() {
    if (this.fechaBaja != null) {
      this.estado = EstadoUsuario.BAJA;
    }
    return this.estado;
  }

  /**
   * Obtiene Nombre de class name solo
   * 
   * @return String
   */
  @Override
  public String getBeanName() {
    return UsuarioModel.class.getSimpleName();
  }

  /**
   * Obtiene Nombre de package + class name
   * 
   * @return String
   */
  @Override
  public String getCualifiedName() {
    return UsuarioModel.class.getCanonicalName();
  }

}
