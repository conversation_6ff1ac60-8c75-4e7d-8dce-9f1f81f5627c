package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "SIAF_CUENTAS_PRESUPUESTARIAS")
@NamedQueries(value = {@NamedQuery(name = "SiafCuentasPresupuestarias.findAll",
    query = "SELECT s FROM SiafCuentasPresupuestarias s")})
public class SiafCuentasPresupuestarias implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 500)
  @Column(name = "CUENTA")
  private String cuenta;
  @Size(max = 500)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @OneToMany(mappedBy = "cupId", fetch = FetchType.LAZY)
  private Set<SiafCuentasEscalafon> siafCuentasEscalafonSet;
  @OneToMany(mappedBy = "cupIdTmp", fetch = FetchType.LAZY)
  private Set<LiqCodigos> liqCodigosSet;
  @OneToMany(mappedBy = "cupId", fetch = FetchType.LAZY)
  private Set<LiqCodigos> liqCodigosSet1;

  public SiafCuentasPresupuestarias(Long id) {
    this.id = id;
  }

}
