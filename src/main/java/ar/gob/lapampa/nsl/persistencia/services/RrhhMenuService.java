package ar.gob.lapampa.nsl.persistencia.services;

import java.util.List;

import ar.gob.lapampa.nsl.persistencia.dtos.requests.MenuRequestDTO;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.models.MenuModel;

@Service
public interface RrhhMenuService {

  GenericResponseDTO listarMenusLegacy();

  GenericResponseDTO actualizarMenusByRol(List<MenuModel> request, Long rolId);

  GenericResponseDTO getById(Long id);

  GenericResponseDTO updateById(MenuRequestDTO request, Long id);

  GenericResponseDTO create(MenuRequestDTO request);

  GenericResponseDTO deleteById(Long id);
}
