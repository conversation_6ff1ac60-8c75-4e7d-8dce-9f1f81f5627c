package ar.gob.lapampa.nsl.persistencia.security;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.databind.ObjectMapper;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.RrhhUsuarioLogDTO;
import ar.gob.lapampa.nsl.persistencia.services.RrhhUsuarioLogService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

/*
 * NO USAR ESTE COMPONENTE COMO BASE PARA OTROS MICROSERVICIOS Este microservicio de persistencia ya
 * implementa ABM de UserLogs utilizado por este componente
 */
@Slf4j
@Component("cs")
public class CustomSecurityEvaluator {

  private static final String PREFIX_PERMISSION = "NNSL_";
  private static final String PREFIX_ROLE = "ROLE_";

  @Autowired
  private RrhhUsuarioLogService usuarioLogService;

  @Autowired
  private HttpServletRequest request;


  /**
   * Sobrecarga de tieneAcceso para que por defecto sea AND en roles y OR en permisos
   *
   * @param roles
   * @param permisos
   * @return boolean
   */
  public boolean hasAccess(String roles, String permisos) {
    return hasAccess(roles, permisos, "OR", "AND");
  }

  /*
   * Sobrecarga para cuando utilizamos "IS" en logica de Roles (permisos es ignorado)
   */
  public boolean hasAccess(String roles, String permisos, String logicRoles) {
    return hasAccess(roles, permisos, logicRoles, "AND");
  }

  /**
   * Verifica roles y permisos Si hay un error de acceso lo persiste en el log de usuarios
   *
   * @param roles
   * @param permisos
   * @param logicRoles AND u OR
   * @param logicPermisos AND u OR
   * @return boolean
   */
  public boolean hasAccess(String roles, String permisos, String logicRoles, String logicPermisos) {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    String endpoint = request.getRequestURI();

    // Convertir string de roles y permisos a Set
    Set<String> rolesSet = roles.contains(",") ? Set.of(roles.split("\\s*,\\s*")) : Set.of(roles);

    Set<String> permisosSet =
        permisos.contains(",") ? Set.of(permisos.split("\\s*,\\s*")) : Set.of(permisos);

    // Verificar permisos - esto lanzará AccessDeniedException con mensaje detallado si falla
    verificarPermisos(endpoint, authentication, rolesSet, permisosSet, logicRoles, logicPermisos);

    return true;
  }

  /**
   * Verifica roles y permisos Si hay un error de acceso lo persiste en el log de usuarios
   *
   * @param endpoint Endpoint al que intentó acceder
   * @param authentication Objeto de contexto
   * @param permisosRequeridos permisos
   * @param rolesRequeridos roles
   * @param logicPermisos AND u OR
   * @param logicRoles AND u OR
   * 
   */
  public void verificarPermisos(String endpoint, Authentication authentication,
      Set<String> rolesRequeridos, Set<String> permisosRequeridos, String logicRoles,
      String logicPermisos) {

    // Obtener las autoridades del usuario actual (roles y permisos)
    Set<String> authorities = authentication.getAuthorities().stream()
        .map(GrantedAuthority::getAuthority).collect(Collectors.toSet());

    // Aplicamos lógica de Roles
    boolean usarAnd = logicRoles.equalsIgnoreCase("AND");
    boolean esRolUnico = logicRoles.equalsIgnoreCase("IS");
    boolean esSoloPermisos = logicPermisos.equalsIgnoreCase("IS");
    boolean cumpleRoles = false;
    boolean cumplePermisos = false;
    String username = authentication.getName();
    String rolActual = "";
    Optional<? extends GrantedAuthority> optRolActual = authentication.getAuthorities().stream()
        .filter(f -> f.getAuthority().startsWith(PREFIX_ROLE)).findFirst();
    if (optRolActual.isPresent()) {
      rolActual = optRolActual.get().getAuthority().substring(PREFIX_ROLE.length());
    }
    Set<String> permisosFaltantes = new HashSet<>();
    Set<String> rolesFaltantes = new HashSet<>();

    // Verificar roles y permisos solo si esRolUnico es falso
    if (esRolUnico) {
      cumpleRoles = rolesRequeridos.size() == 1
          && rolesRequeridos.stream().allMatch(role -> authorities.contains(PREFIX_ROLE + role));
      cumplePermisos = true; // damos por valido los permisos (ignoramos)
    } else {
      if (esSoloPermisos) {
        cumpleRoles = true; // damos por valido los roles (ingnoramos)
        usarAnd = true; // deben cumplirse todos los permisos requeridos
      } else {
        cumpleRoles = usarAnd
            ? rolesRequeridos.stream().allMatch(role -> authorities.contains(PREFIX_ROLE + role))
            : rolesRequeridos.stream().anyMatch(role -> authorities.contains(PREFIX_ROLE + role));

        // aplicamos lógica de Permisos
        usarAnd = logicPermisos.equalsIgnoreCase("AND");
      }

      // Verificar permisos
      cumplePermisos = usarAnd
          ? permisosRequeridos.stream()
              .allMatch(permiso -> authorities.contains(PREFIX_PERMISSION + permiso)) // Lógica AND
          : permisosRequeridos.stream()
              .anyMatch(permiso -> authorities.contains(PREFIX_PERMISSION + permiso)); // Lógica OR
    }

    // Evaluar si cumple ambos criterios según la lógica seleccionada
    if (!cumplePermisos || !cumpleRoles) {

      if (!cumplePermisos) {
        // Verificar permisos faltantes
        permisosFaltantes = permisosRequeridos.stream()
            .filter(permiso -> !authorities.contains(PREFIX_PERMISSION + permiso))
            .collect(Collectors.toSet());
      }
      if (!cumpleRoles) {
        // Verificar roles faltantes
        rolesFaltantes = rolesRequeridos.stream()
            .filter(role -> !authorities.contains(PREFIX_ROLE + role)).collect(Collectors.toSet());
      }

      // Lanzar excepción si faltan permisos o roles
      if (!permisosFaltantes.isEmpty() || !rolesFaltantes.isEmpty()) {
        StringBuilder mensaje =
            buildMessage(endpoint, username, permisosFaltantes, rolesFaltantes, esRolUnico);

        // Registrar el error en el log de usuarios antes de lanzar la excepción
        registrarErrorAcceso(username, rolActual, endpoint, permisosFaltantes, rolesFaltantes);

        throw new AccessDeniedException(mensaje.toString());
      }
    }
  }

  /**
   * Registra un error de acceso en el log de usuarios
   *
   * @param username Usuario que intentó acceder
   * @param endpoint Endpoint al que intentó acceder
   * @param permisosFaltantes permisos de error detallado
   * @param rolesFaltantes roles de error detallado
   */
  private void registrarErrorAcceso(String username, String rolActual, String endpoint,
      Set<String> permisosFaltantes, Set<String> rolesFaltantes) {
    try {
      // Crear un mapa para el log
      RrhhUsuarioLogDTO logMap = new RrhhUsuarioLogDTO();
      logMap.setLogin(username);
      logMap.setRole(rolActual);
      logMap.setTipo("FORBIDDEN");
      logMap.setEstado("403");
      logMap.setCreado(new Date());

      // Crear un objeto que contenga la información del resultado
      Map<String, Object> resultadoMap = new HashMap<>();
      resultadoMap.put("error", "Acceso denegado");
      resultadoMap.put("endpoint", endpoint);
      resultadoMap.put("mensaje", "Faltan permisos.");
      resultadoMap.put("permisosFaltantes", permisosFaltantes);
      resultadoMap.put("rolesFaltantes", rolesFaltantes);
      resultadoMap.put("user", Long.valueOf(username));

      // Convertir el mapa de resultado a JSON
      ObjectMapper mapper = new ObjectMapper();
      String resultadoJson = mapper.writeValueAsString(resultadoMap);

      // Agregar el resultado JSON al mapa principal
      logMap.setResultado(resultadoJson);

      // Llamar al servicio para registrar el log en una nueva transacción
      GenericResponseDTO response = usuarioLogService.registrar(logMap);

      // Verificar la respuesta
      if (response != null) {
        log.debug("Respuesta del servicio de log: {}",
            response.getCodigoEstado() == 0 ? "Éxito" : "Error: " + response.getMensaje());
      } else {
        log.error("ERROR: La respuesta del servicio de log es NULL");
      }

    } catch (Exception e) {
      log.error("ERROR al registrar log de acceso denegado: {}", e.getMessage());
    }
  }

  /**
   * Construye un mensaje de error detallado para la excepción de acceso denegado.
   *
   * @param endpoint Endpoint al que se intentó acceder
   * @param username Nombre de usuario que intentó el acceso
   * @param permisosFaltantes Conjunto de permisos que faltan
   * @param rolesFaltantes Conjunto de roles que faltan
   * @return StringBuilder con el mensaje formateado en formato JSON
   */
  private StringBuilder buildMessage(String endpoint, String username,
      Set<String> permisosFaltantes, Set<String> rolesFaltantes, boolean esRolUnico) {

    StringBuilder mensaje = new StringBuilder();
    StringBuilder descripcion = new StringBuilder();

    // Construir la parte descriptiva del mensaje segun esRolUnico
    if (!rolesFaltantes.isEmpty()) {
      if (esRolUnico) {
        descripcion.append("Sobran roles");
      } else {
        descripcion.append("Faltan roles");
      }
    }

    if (!permisosFaltantes.isEmpty() && !esRolUnico) {
      if (!descripcion.isEmpty()) {
        descripcion.append(" y permisos");
      } else {
        descripcion.append("Faltan permisos");
      }
    }

    // Agregar punto final y comillas
    if (!descripcion.isEmpty()) {
      descripcion.append(".");
    }

    // Iniciar el JSON con la descripción
    mensaje.append("{\n  \"mensaje\": \"").append(descripcion).append("\",\n");

    // Agregar roles faltantes
    mensaje.append("  \"roles\": ");
    if (rolesFaltantes.isEmpty()) {
      mensaje.append("null");
    } else {
      mensaje.append("[\"").append(String.join("\", \"", rolesFaltantes)).append("\"]");
    }
    mensaje.append(",\n");

    // Agregar permisos faltantes
    mensaje.append("  \"permisos\": ");
    if (permisosFaltantes.isEmpty()) {
      mensaje.append("null");
    } else {
      mensaje.append("[\"").append(String.join("\", \"", permisosFaltantes)).append("\"]");
    }
    mensaje.append(",\n");

    // Agregar información del usuario y endpoint
    mensaje.append("  \"user\": \"").append(username).append("\",\n");
    mensaje.append("  \"endPoint\": \"").append(endpoint).append("\"\n}");

    return mensaje;
  }
}
