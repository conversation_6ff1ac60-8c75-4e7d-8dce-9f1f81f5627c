package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_COD_PROV_DA")
@NamedQueries(
    value = {@NamedQuery(name = "LiqCodProvDa.findAll", query = "SELECT l FROM LiqCodProvDa l")})
public class LiqCodProvDa implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected LiqCodProvDaPK liqCodProvDaPK;

  public LiqCodProvDa(LiqCodProvDaPK liqCodProvDaPK) {
    this.liqCodProvDaPK = liqCodProvDaPK;
  }

  public LiqCodProvDa(long codId, long provId, long dadId) {
    this.liqCodProvDaPK = new LiqCodProvDaPK(codId, provId, dadId);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqCodProvDa[ liqCodProvDaPK=" + liqCodProvDaPK
        + " ]";
  }

}
