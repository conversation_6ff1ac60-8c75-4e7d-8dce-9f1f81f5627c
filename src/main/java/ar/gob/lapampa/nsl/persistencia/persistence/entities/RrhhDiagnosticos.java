package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_DIAGNOSTICOS")
@NamedQueries(value = {
    @NamedQuery(name = "RrhhDiagnosticos.findAll", query = "SELECT r FROM RrhhDiagnosticos r")})
public class RrhhDiagnosticos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 500)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @JoinTable(name = "RRHH_REC_MED_DIAGNOSTICOS",
      joinColumns = {@JoinColumn(name = "DIA_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "RME_ID", referencedColumnName = "ID")})
  @ManyToMany(fetch = FetchType.LAZY)
  private Set<RrhhReconocimientosMedicos> rrhhReconocimientosMedicosSet;

  public RrhhDiagnosticos(Long id) {
    this.id = id;
  }

  public RrhhDiagnosticos(Long id, String descripcion) {
    this.id = id;
    this.descripcion = descripcion;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhDiagnosticos[ id=" + id + " ]";
  }

}
