package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class LiqCuotasAlimVinculosPK implements Serializable {

  private static final long serialVersionUID = 1387799243861676482L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "CUA_ID")
  private long cuaId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "VNC_ID")
  private long vncId;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqCuotasAlimVinculosPK[ cuaId=" + cuaId
        + ", vncId=" + vncId + " ]";
  }

}
