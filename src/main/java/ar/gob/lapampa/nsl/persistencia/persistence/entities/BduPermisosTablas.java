package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PERMISOS_TABLAS")
@NamedQueries(value = {
    @NamedQuery(name = "BduPermisosTablas.findAll", query = "SELECT b FROM BduPermisosTablas b")})
public class BduPermisosTablas implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;
  @Column(name = "PERMISO_INSERT")
  private Character permisoInsert;
  @Column(name = "PERMISO_UPDATE")
  private Character permisoUpdate;
  @Column(name = "PERMISO_SELECT")
  private Character permisoSelect;
  @Column(name = "PERMISO_DELETE")
  private Character permisoDelete;
  @Column(name = "PERMISO_EXECUTE")
  private Character permisoExecute;
  @JoinColumn(name = "PRM_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduPermisos prmId;
  @JoinColumn(name = "TBL_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduTablas tblId;

  public BduPermisosTablas(Long id) {
    this.id = id;
  }

  public BduPermisosTablas(Long id, Date fecha) {
    this.id = id;
    this.fecha = fecha;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduPermisosTablas[ id=" + id + " ]";
  }

}
