package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_PARAM_DTOSADIC_DDJJ572")
public class GanParamDtosadicDdjj572 implements Serializable {

  private static final long serialVersionUID = -7313499050079962295L;

  @Id
  @Column(name = "CODIGO")
  private String codigo;

  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Column(name = "MES_DDE_HASTA")
  private String mesDdeHasta;

  @Column(name = "VALOR")
  private String valor;

}
