package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_TAREAS")
@SequenceGenerator(name = "RRHH_TAR_SEQ", sequenceName = "RRHH_TAR_SEQ", allocationSize = 1)
public class RrhhTareas implements Serializable {

  private static final long serialVersionUID = -6010992691561393469L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_TAR_SEQ")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Column(name = "NRO")
  private Long nro;

  @Basic(optional = true)
  @Column(name = "INGRESABLE")
  @Convert(converter = YesNoConverter.class)
  private Boolean ingresable;

  @JoinColumn(name = "ESC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private PreEscalafones escId;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "tarId", fetch = FetchType.LAZY)
  private Set<RrhhOcupacionesTareas> rrhhOcupacionesTareasSet;

  public RrhhTareas(Long id) {
    this.id = id;
  }

  public RrhhTareas(Long id, String descripcion, Boolean activo) {
    this.id = id;
    this.descripcion = descripcion;
    this.activo = activo;
  }

}
