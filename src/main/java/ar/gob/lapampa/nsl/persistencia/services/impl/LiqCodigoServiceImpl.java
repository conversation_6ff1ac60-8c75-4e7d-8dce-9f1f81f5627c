package ar.gob.lapampa.nsl.persistencia.services.impl;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.ComparableExpressionBase;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.jpa.impl.JPAQuery;
import ar.gob.lapampa.nsl.datatransfer.request.LiqCodigoRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.request.LiqCodigoTipoRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.request.ListadoConceptoConvenioRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.datatransfer.response.ListadoDetalleConceptosConveniosResponseDTO;
import ar.gob.lapampa.nsl.datatransfer.response.ListadoResponseDTO;
import ar.gob.lapampa.nsl.datatransfer.response.ListadoTotalesConceptosConveniosResponseDTO;
import ar.gob.lapampa.nsl.persistencia.models.CodigoModel;
import ar.gob.lapampa.nsl.persistencia.models.CodigoTipoModel;
import ar.gob.lapampa.nsl.persistencia.models.ListadoDetalleConceptosConveniosModel;
import ar.gob.lapampa.nsl.persistencia.models.ListadoTotalesConceptosConveniosModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.LiqCodigos;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.LiqCodigosTipos;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QBduPersonasFisicas;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QLiqCodigos;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QLiqCodigosTipos;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QLiqOcupacionesRecibos;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QLiqRecibos;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QLiqRecibosDetalle;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QPreEscalafones;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QRrhhEmpresa;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QRrhhOcupaciones;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.LiqCodigoMapper;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.LiqCodigoTipoMapper;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.ListadoDetalleConceptosConveniosMapper;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.ListadoTotalesConceptosConveniosMapper;
import ar.gob.lapampa.nsl.persistencia.repositories.LiqCodigoRepository;
import ar.gob.lapampa.nsl.persistencia.repositories.LiqCodigoTipoRepository;
import ar.gob.lapampa.nsl.persistencia.services.LiqCodigoService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

@Service
public class LiqCodigoServiceImpl implements LiqCodigoService {

  @PersistenceContext
  private EntityManager entityManager;

  @Autowired
  private LiqCodigoTipoRepository liqCodigoTipoRepository;

  @Autowired
  private LiqCodigoRepository liqCodigoRepository;

  Sort sort = Sort.unsorted();

  private static final HashMap<String, Path> ordenamiento;
  static {
    ordenamiento = new HashMap<>();
    ordenamiento.put("apellido", QLiqRecibos.liqRecibos.apellido);
    ordenamiento.put("nombre", QLiqRecibos.liqRecibos.nombre);
    ordenamiento.put("nro", QLiqCodigos.liqCodigos.nro);
    ordenamiento.put("codigoNro", QLiqRecibosDetalle.liqRecibosDetalle.codNro);
    ordenamiento.put("ocuId", QRrhhOcupaciones.rrhhOcupaciones.id);
    ordenamiento.put("codigoEmpresa", QRrhhEmpresa.rrhhEmpresa.codigo);
    ordenamiento.put("convenioNombre", QPreEscalafones.preEscalafones.descripcion);
    ordenamiento.put("afiliado", QLiqRecibos.liqRecibos.afiliado);
    ordenamiento.put("haberes", QLiqRecibosDetalle.liqRecibosDetalle.haberes);
    ordenamiento.put("descuentos", QLiqRecibosDetalle.liqRecibosDetalle.descuentos);
    ordenamiento.put("haberesSuma", QLiqRecibosDetalle.liqRecibosDetalle.haberes);
    ordenamiento.put("cuentaHaberes", QLiqRecibosDetalle.liqRecibosDetalle.haberes);
    ordenamiento.put("descuentosSuma", QLiqRecibosDetalle.liqRecibosDetalle.descuentos);
    ordenamiento.put("cuentaDescuentos", QLiqRecibosDetalle.liqRecibosDetalle.descuentos);
    ordenamiento.put("convenioNro", QPreEscalafones.preEscalafones.nro);
  }

  @Override
  public GenericResponseDTO listarTiposdeCodigo(LiqCodigoTipoRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();
    BooleanBuilder booleanBuilder = new BooleanBuilder();

    if (!"".equals(request.getSortBy())) {
      if (Boolean.TRUE.equals(request.getAsc())) {
        sort = Sort.by(Sort.Direction.ASC, request.getSortBy());
      } else {
        sort = Sort.by(Sort.Direction.DESC, request.getSortBy());
      }
    } else {
      sort = Sort.unsorted();
    }

    if (Boolean.TRUE.equals(request.getActivo()))
      booleanBuilder.and(QLiqCodigosTipos.liqCodigosTipos.activo.isTrue());
    else
      booleanBuilder.and(QLiqCodigosTipos.liqCodigosTipos.activo.isFalse());

    if (request.getCotPadreId() != null)
      booleanBuilder
          .and(QLiqCodigosTipos.liqCodigosTipos.cotPadreId.id.eq(request.getCotPadreId()));
    else
      booleanBuilder.and(QLiqCodigosTipos.liqCodigosTipos.cotPadreId.id.isNull());

    List<LiqCodigosTipos> tipoCodigo =
        (List<LiqCodigosTipos>) liqCodigoTipoRepository.findAll(booleanBuilder.getValue(), sort);
    List<CodigoTipoModel> respuesta =
        tipoCodigo.stream().map(LiqCodigoTipoMapper::liqCodigoTipoToCodigoTipoModel).toList();
    response.setEstadoExito(respuesta);
    return response;
  }

  @Override
  public GenericResponseDTO listar(LiqCodigoRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();
    BooleanBuilder booleanBuilder = new BooleanBuilder();
    JPAQuery<Object> query = new JPAQuery<>(entityManager);

    if (Boolean.TRUE.equals(request.getActivo()))
      booleanBuilder.and(QLiqCodigos.liqCodigos.activo.isTrue());
    else
      booleanBuilder.and(QLiqCodigos.liqCodigos.activo.isFalse());

    QLiqCodigos liqCodigos = QLiqCodigos.liqCodigos;
    QLiqCodigosTipos liqCodigosTipos = QLiqCodigosTipos.liqCodigosTipos;

    if (!"".equals(request.getSortBy())) {
      Path fieldPath = ordenamiento.get(request.getSortBy());
      ComparableExpressionBase<?> orden = (ComparableExpressionBase<?>) fieldPath;
      if (Boolean.TRUE.equals(request.getAsc())) {
        query.orderBy(orden.asc());
      } else {
        query.orderBy(orden.desc());
      }
    }

    if (request.getEscId() != null && request.getEscId() != 0) {
      BooleanExpression escId = liqCodigos.escId.id.eq(request.getEscId());
      booleanBuilder.and(escId);
    }

    if (request.getCotId() != null) {
      BooleanExpression cotId = liqCodigos.cotId.id.eq(request.getCotId());
      BooleanExpression cotPadreId = liqCodigosTipos.cotPadreId.id.eq(request.getCotId())
          .and(liqCodigos.cotId.id.eq(liqCodigosTipos.id));
      BooleanExpression codigoTipo = cotId.or(cotPadreId);
      booleanBuilder.and(codigoTipo);
    }

    query.where(booleanBuilder);

    List<LiqCodigos> codigos =
        query.select(liqCodigos).distinct().from(liqCodigos, liqCodigosTipos).fetch();
    List<CodigoModel> respuesta =
        codigos.stream().map(LiqCodigoMapper::liqCodigoToCodigoModel).collect(Collectors.toList());
    response.setEstadoExito(respuesta);
    return response;
  }

  @Override
  public GenericResponseDTO listarConceptosConvenio(ListadoConceptoConvenioRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();
    JPAQuery<Object> query = new JPAQuery<>(entityManager);

    QLiqRecibosDetalle liqRecibosDetalle = QLiqRecibosDetalle.liqRecibosDetalle;
    QLiqRecibos liqRecibos = QLiqRecibos.liqRecibos;
    QLiqOcupacionesRecibos liqOcupacionesRecibos = QLiqOcupacionesRecibos.liqOcupacionesRecibos;
    QRrhhOcupaciones rrhhOcupaciones = QRrhhOcupaciones.rrhhOcupaciones;
    QPreEscalafones preEscalafones = QPreEscalafones.preEscalafones;
    QRrhhEmpresa rrhhEmpresa = QRrhhEmpresa.rrhhEmpresa;
    QBduPersonasFisicas bduPersonasFisicas = QBduPersonasFisicas.bduPersonasFisicas;
    QLiqCodigos liqCodigos = QLiqCodigos.liqCodigos;
    QLiqCodigosTipos liqCodigosTipos = QLiqCodigosTipos.liqCodigosTipos;

    BooleanExpression condicion = null;

    if (request.getProcesoId() != null)
      condicion = liqOcupacionesRecibos.prcId.id.eq(request.getProcesoId());

    if (request.getEmpresaId() != null) {
      BooleanExpression condicionEmpresa = rrhhEmpresa.id.eq(request.getEmpresaId());
      condicion = (condicion != null) ? condicion.and(condicionEmpresa) : condicionEmpresa;
    }

    if (request.getCodigoId() != null && request.getCodigoId() != 0) {
      BooleanExpression condicionCodigo = liqRecibosDetalle.codId.id.eq(request.getCodigoId());
      condicion = (condicion != null) ? condicion.and(condicionCodigo) : condicionCodigo;
    }

    if (request.getConvenio() != null) {
      BooleanExpression condicionConvenio = liqCodigos.escId.id.eq(request.getConvenio());
      BooleanExpression condicionConvenioOcu = rrhhOcupaciones.escId.id.eq(request.getConvenio());
      condicion = (condicion != null) ? condicion.and(condicionConvenioOcu) : condicionConvenioOcu;

      BooleanExpression condicionConvenio99 = liqCodigos.escId.id.eq(99L);
      BooleanExpression condicionfinal = condicionConvenio.or(condicionConvenio99);
      condicion = (condicion != null) ? condicion.and(condicionfinal) : condicionfinal;
    }

    if (request.getSubTipoCodigoId() != null && request.getTipoCodigoId() != null) {
      BooleanExpression condicionTipo = liqCodigos.cotId.id.eq(request.getSubTipoCodigoId());
      condicion = (condicion != null) ? condicion.and(condicionTipo) : condicionTipo;
    }

    if (request.getSubTipoCodigoId() == null && request.getTipoCodigoId() != null) {
      BooleanExpression cotId = liqCodigos.cotId.id.eq(request.getTipoCodigoId());
      BooleanExpression cotPadreId = liqCodigosTipos.cotPadreId.id.eq(request.getTipoCodigoId());
      BooleanExpression codigoTipo = cotId.or(cotPadreId);
      condicion = (condicion != null) ? condicion.and(codigoTipo) : codigoTipo;

    }

    if (request.getPage() != null && request.getSize() != null) {
      query.offset((request.getPage() - 1L) * request.getSize()).limit(request.getSize());
    }

    if (condicion != null)
      query.where(condicion);

    if (request.getSortBy() != null) {
      Path fieldPath = ordenamiento.get(request.getSortBy());
      ComparableExpressionBase<?> orden = (ComparableExpressionBase<?>) fieldPath;


      switch (request.getSortBy()) {
        case "haberesSuma", "descuentosSuma":
          NumberExpression<?> numericField = (NumberExpression<?>) orden;
          query.where(numericField.notIn(0));
          if (Boolean.TRUE.equals(request.getAsc())) {
            query.orderBy(numericField.sum().asc());
          } else {
            query.orderBy(numericField.sum().desc());
          }
          break;
        case "cuentaDescuentos", "cuentaHaberes":
          NumberExpression<?> numericField2 = (NumberExpression<?>) orden;
          query.where(numericField2.notIn(0));
          if (Boolean.TRUE.equals(request.getAsc())) {
            query.orderBy(numericField2.count().asc());
          } else {
            query.orderBy(numericField2.count().desc());
          }
          break;
        default:
          if (Boolean.TRUE.equals(request.getAsc())) {
            query.orderBy(orden.asc());
          } else {
            query.orderBy(orden.desc());
          }
          break;

      }
    } else
      query.orderBy(QPreEscalafones.preEscalafones.nro.asc(),
          QLiqRecibosDetalle.liqRecibosDetalle.codNro.asc());

    Long totalRegistros = null;
    List<ListadoTotalesConceptosConveniosResponseDTO> recibosTotales;
    if (request.getTipoListado().equals("totales")) {
      if (request.getObtenerCantRegistros().equals(Boolean.TRUE)) {
        totalRegistros = (long) query
            .select(Projections.constructor(ListadoTotalesConceptosConveniosResponseDTO.class,
                liqRecibosDetalle.codNro, liqRecibosDetalle.haberes.sum(),
                liqRecibosDetalle.descuentos.sum(), preEscalafones.nro, liqRecibosDetalle.concepto,
                preEscalafones.descripcion, rrhhEmpresa.codigo, rrhhEmpresa.descripcion,
                liqRecibosDetalle.descuentos.count().as("cantDescuentos"),
                liqRecibosDetalle.haberes.count().as("cantHaberes")))
            .from(liqRecibosDetalle).join(liqRecibos)
            .on(liqRecibosDetalle.recId.id.eq(liqRecibos.id)).join(liqOcupacionesRecibos)
            .on(liqOcupacionesRecibos.recId.id.eq(liqRecibos.id)).join(rrhhOcupaciones)
            .on(liqOcupacionesRecibos.ocuId.id.eq(rrhhOcupaciones.id)).join(preEscalafones)
            .on(preEscalafones.id.eq(rrhhOcupaciones.escId.id)).join(rrhhEmpresa)
            .on(rrhhOcupaciones.empId.id.eq(rrhhEmpresa.id)).join(bduPersonasFisicas)
            .on(bduPersonasFisicas.id.eq(rrhhOcupaciones.prsId.id)).join(liqCodigos)
            .on(liqRecibosDetalle.codId.id.eq(liqCodigos.id)).join(liqCodigosTipos)
            .on(liqCodigosTipos.id.eq(liqCodigos.cotId.id))
            .groupBy(preEscalafones.nro, liqRecibosDetalle.codNro, preEscalafones.descripcion,
                liqRecibosDetalle.concepto, rrhhEmpresa.codigo, rrhhEmpresa.descripcion)
            .fetch().size();

        response.setEstadoExito(totalRegistros);
      }
      recibosTotales = query
          .select(Projections.constructor(ListadoTotalesConceptosConveniosResponseDTO.class,
              liqRecibosDetalle.codNro, liqRecibosDetalle.haberes.sum().coalesce(0d),
              liqRecibosDetalle.descuentos.sum().coalesce(0d), preEscalafones.nro,
              liqRecibosDetalle.concepto, preEscalafones.descripcion, rrhhEmpresa.codigo,
              rrhhEmpresa.descripcion,
              liqRecibosDetalle.descuentos.count().coalesce(0L).as("cantDescuentos"),
              liqRecibosDetalle.haberes.count().coalesce(0L).as("cantHaberes")))
          .from(liqRecibosDetalle).join(liqRecibos).on(liqRecibosDetalle.recId.id.eq(liqRecibos.id))
          .join(liqOcupacionesRecibos).on(liqOcupacionesRecibos.recId.id.eq(liqRecibos.id))
          .join(rrhhOcupaciones).on(liqOcupacionesRecibos.ocuId.id.eq(rrhhOcupaciones.id))
          .join(preEscalafones).on(preEscalafones.id.eq(rrhhOcupaciones.escId.id)).join(rrhhEmpresa)
          .on(rrhhOcupaciones.empId.id.eq(rrhhEmpresa.id)).join(bduPersonasFisicas)
          .on(bduPersonasFisicas.id.eq(rrhhOcupaciones.prsId.id)).join(liqCodigos)
          .on(liqRecibosDetalle.codId.id.eq(liqCodigos.id)).join(liqCodigosTipos)
          .on(liqCodigosTipos.id.eq(liqCodigos.cotId.id))
          .groupBy(preEscalafones.nro, liqRecibosDetalle.codNro, preEscalafones.descripcion,
              liqRecibosDetalle.concepto, rrhhEmpresa.codigo, rrhhEmpresa.descripcion)
          .fetch();
      List<ListadoTotalesConceptosConveniosModel> totalesModel = recibosTotales.stream()
          .map(ListadoTotalesConceptosConveniosMapper::listadoToModel).toList();
      ListadoResponseDTO respuesta = new ListadoResponseDTO(totalRegistros, totalesModel);
      response.setEstadoExito(respuesta);

    }

    List<ListadoDetalleConceptosConveniosResponseDTO> recibosDetalle;
    if (request.getTipoListado().equals("detalle")) {
      if (request.getObtenerCantRegistros().equals(Boolean.TRUE)) {
        totalRegistros = (long) query
            .select(Projections.constructor(ListadoDetalleConceptosConveniosResponseDTO.class,
                liqRecibosDetalle.codNro, liqRecibosDetalle.haberes, liqRecibosDetalle.descuentos,
                preEscalafones.nro, liqRecibosDetalle.concepto, preEscalafones.descripcion,
                rrhhEmpresa.codigo, rrhhEmpresa.descripcion, liqRecibos.afiliado,
                rrhhOcupaciones.id.as("ocuId"), liqRecibos.apellido, liqRecibos.nombre,
                liqRecibosDetalle.mesRetroactivo, liqRecibosDetalle.anioRetroactivo))
            .from(liqRecibosDetalle).join(liqRecibos)
            .on(liqRecibosDetalle.recId.id.eq(liqRecibos.id)).join(liqOcupacionesRecibos)
            .on(liqOcupacionesRecibos.recId.id.eq(liqRecibos.id)).join(rrhhOcupaciones)
            .on(liqOcupacionesRecibos.ocuId.id.eq(rrhhOcupaciones.id)).join(preEscalafones)
            .on(preEscalafones.id.eq(rrhhOcupaciones.escId.id)).join(rrhhEmpresa)
            .on(rrhhOcupaciones.empId.id.eq(rrhhEmpresa.id)).join(bduPersonasFisicas)
            .on(bduPersonasFisicas.id.eq(rrhhOcupaciones.prsId.id)).join(liqCodigos)
            .on(liqRecibosDetalle.codId.id.eq(liqCodigos.id)).join(liqCodigosTipos)
            .on(liqCodigosTipos.id.eq(liqCodigos.cotId.id)).fetch().size();
        response.setEstadoExito(totalRegistros);
      }
      recibosDetalle = query
          .select(Projections.constructor(ListadoDetalleConceptosConveniosResponseDTO.class,
              liqRecibosDetalle.codNro, liqRecibosDetalle.haberes, liqRecibosDetalle.descuentos,
              preEscalafones.nro, liqRecibosDetalle.concepto, preEscalafones.descripcion,
              rrhhEmpresa.codigo, rrhhEmpresa.descripcion, liqRecibos.afiliado,
              rrhhOcupaciones.id.as("ocuId"), liqRecibos.apellido, liqRecibos.nombre,
              liqRecibosDetalle.mesRetroactivo, liqRecibosDetalle.anioRetroactivo))
          .from(liqRecibosDetalle).join(liqRecibos).on(liqRecibosDetalle.recId.id.eq(liqRecibos.id))
          .join(liqOcupacionesRecibos).on(liqOcupacionesRecibos.recId.id.eq(liqRecibos.id))
          .join(rrhhOcupaciones).on(liqOcupacionesRecibos.ocuId.id.eq(rrhhOcupaciones.id))
          .join(preEscalafones).on(preEscalafones.id.eq(rrhhOcupaciones.escId.id)).join(rrhhEmpresa)
          .on(rrhhOcupaciones.empId.id.eq(rrhhEmpresa.id)).join(bduPersonasFisicas)
          .on(bduPersonasFisicas.id.eq(rrhhOcupaciones.prsId.id)).join(liqCodigos)
          .on(liqRecibosDetalle.codId.id.eq(liqCodigos.id)).join(liqCodigosTipos)
          .on(liqCodigosTipos.id.eq(liqCodigos.cotId.id)).fetch();
      List<ListadoDetalleConceptosConveniosModel> recibosModel = recibosDetalle.stream()
          .map(ListadoDetalleConceptosConveniosMapper::listadoDetalleToModel).toList();
      ListadoResponseDTO respuesta = new ListadoResponseDTO(totalRegistros, recibosModel);
      response.setEstadoExito(respuesta);
    }
    return response;
  }
}
