package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * The persistent class for the PRE_PARTIDAS_PRESUPUESTARIA database table.
 * 
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_PARTIDAS_PRESUPUESTARIA")
@SequenceGenerator(name = "PRE_PART_SEQ", sequenceName = "PRE_PART_SEQ", allocationSize = 1)
public class PrePartidasPresupuestaria implements Serializable {

  private static final long serialVersionUID = -8177527879310283387L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PRE_PART_SEQ")
  private Long id;

  private Long anio;

  @JoinColumn(name = "COMBPRES_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private PreCombinacionPresupuestaria combpresId;

}
