package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_FUNC_ACC_PERFILES")
@NamedQueries(value = {
    @NamedQuery(name = "BduFuncAccPerfiles.findAll", query = "SELECT b FROM BduFuncAccPerfiles b")})
public class BduFuncAccPerfiles implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected BduFuncAccPerfilesPK bduFuncAccPerfilesPK;
  @Column(name = "NIVEL")
  private Short nivel;
  @JoinColumn(name = "ACC_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduAcciones bduAcciones;
  @JoinColumn(name = "FSI_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduFuncionesSis bduFuncionesSis;
  @JoinColumn(name = "PFL_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduPerfiles bduPerfiles;

  public BduFuncAccPerfiles(BduFuncAccPerfilesPK bduFuncAccPerfilesPK) {
    this.bduFuncAccPerfilesPK = bduFuncAccPerfilesPK;
  }

  public BduFuncAccPerfiles(long pflId, long fsiId, long accId) {
    this.bduFuncAccPerfilesPK = new BduFuncAccPerfilesPK(pflId, fsiId, accId);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduFuncAccPerfiles[ bduFuncAccPerfilesPK="
        + bduFuncAccPerfilesPK + " ]";
  }

}
