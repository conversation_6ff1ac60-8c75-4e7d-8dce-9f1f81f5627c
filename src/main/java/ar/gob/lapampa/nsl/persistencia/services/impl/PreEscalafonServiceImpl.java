package ar.gob.lapampa.nsl.persistencia.services.impl;

import java.util.List;
import org.springframework.stereotype.Service;
import com.querydsl.jpa.impl.JPAQuery;
import ar.gob.lapampa.nsl.datatransfer.request.PreEscalafonRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.models.CodigoModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.LiqCodigos;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QLiqCodigos;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.LiqCodigoMapper;
import ar.gob.lapampa.nsl.persistencia.services.PreEscalafonService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

@Service
public class PreEscalafonServiceImpl implements PreEscalafonService {

  @PersistenceContext
  private EntityManager entityManager;

  @Override
  public GenericResponseDTO listarCodigosporConvenio(PreEscalafonRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();
    QLiqCodigos codigo = QLiqCodigos.liqCodigos;

    JPAQuery<Object> query = new JPAQuery<>(entityManager);

    List<LiqCodigos> codigos =
        query.select(codigo).from(codigo).where(codigo.escId.id.eq(request.getId()))
            .where(codigo.activo.isTrue()).orderBy(codigo.nro.asc()).fetch();

    List<CodigoModel> respuesta =
        codigos.stream().map(LiqCodigoMapper::liqCodigoToCodigoModel).toList();
    response.setEstadoExito(respuesta);
    return response;
  }
}
