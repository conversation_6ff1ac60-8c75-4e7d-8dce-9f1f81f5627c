package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_CGE_RECIBOS_DETALLE")
@NamedQueries(value = {@NamedQuery(name = "LiqCgeRecibosDetalle.findAll",
    query = "SELECT l FROM LiqCgeRecibosDetalle l")})
public class LiqCgeRecibosDetalle implements Serializable {
  private static final long serialVersionUID = 1L;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private BigDecimal id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "REC_ID")
  private BigInteger recId;
  @Column(name = "ORDEN")
  private BigInteger orden;
  @Size(max = 100)
  @Column(name = "COD_NRO")
  private String codNro;
  @Column(name = "CANTIDAD")
  private BigInteger cantidad;
  @Size(max = 100)
  @Column(name = "CONCEPTO")
  private String concepto;
  @Column(name = "HABERES")
  private BigInteger haberes;
  @Column(name = "DESCUENTOS")
  private BigInteger descuentos;
  @Column(name = "COD_ID")
  private BigInteger codId;

  public LiqCgeRecibosDetalle(BigDecimal id) {
    this.id = id;
  }

  public LiqCgeRecibosDetalle(BigDecimal id, BigInteger recId) {
    this.id = id;
    this.recId = recId;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqCgeRecibosDetalle[ id=" + id + " ]";
  }

}
