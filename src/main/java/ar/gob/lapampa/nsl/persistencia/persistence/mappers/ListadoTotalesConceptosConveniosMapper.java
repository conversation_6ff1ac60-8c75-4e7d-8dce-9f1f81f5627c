package ar.gob.lapampa.nsl.persistencia.persistence.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import ar.gob.lapampa.nsl.datatransfer.response.ListadoTotalesConceptosConveniosResponseDTO;
import ar.gob.lapampa.nsl.persistencia.models.ListadoTotalesConceptosConveniosModel;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ListadoTotalesConceptosConveniosMapper {

    ListadoTotalesConceptosConveniosMapper INSTANCE = Mappers.getMapper(ListadoTotalesConceptosConveniosMapper.class);

    static ListadoTotalesConceptosConveniosModel listadoToModel(ListadoTotalesConceptosConveniosResponseDTO totales) {
        ListadoTotalesConceptosConveniosModel model = new ListadoTotalesConceptosConveniosModel();
        model.setCodigoNro(totales.getCodNro());
        model.setHaberes(totales.getHaberes());
        model.setDescuentos(totales.getDescuentos());
        model.setConcepto(totales.getConcepto());
        model.setConvenioNombre(totales.getConvenioNombre());
        model.setConvenioNro(totales.getConvenioNro());
        model.setCodigoEmpresa(totales.getCodigoEmpresa());
        model.setDescripcionEmpresa(totales.getDescripcionEmpresa());
        model.setCantDescuentos(totales.getCantDescuentos());
        model.setCantHaberes(totales.getCantHaberes());
        return model;
    }
}
