package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_PLANTA_PRESUPUESTARIA")
public class PrePlantaPresupuestaria implements Serializable {

  private static final long serialVersionUID = -4546593923804713563L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @JoinColumn(name = "PRE_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private PrePresupuestos preId;

  @JoinColumn(name = "ACT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduOrganismosPre actId;

  @JoinColumn(name = "CAT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private PreCategorias catId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "CDAD_PRESUPUESTADA")
  private Long cdadPresupuestada;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "CDAD_RESERVADOS")
  private Long cdadReservados;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "CDAD_OCU_EFECTIVOS")
  private Long cdadOcuEfectivos;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRESUPUESTADA")
  private Character presupuestada;

  @OneToMany(mappedBy = "pprId", fetch = FetchType.LAZY)
  private Set<RrhhOcupacionesMov> rrhhOcupacionesMovSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "prePlantaPresupuestaria",
      fetch = FetchType.LAZY)
  private Set<PrePlantaPresCambios> prePlantaPresCambiosSet;

  public PrePlantaPresupuestaria(Long id) {
    this.id = id;
  }

}
