package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_TOTALES_MES_PERSONA")
@NamedQueries(value = {@NamedQuery(name = "LiqTotalesMesPersona.findAll",
    query = "SELECT l FROM LiqTotalesMesPersona l")})
public class LiqTotalesMesPersona implements Serializable {
  private static final long serialVersionUID = 1L;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private BigDecimal id;
  @Column(name = "MES")
  private BigInteger mes;
  @Column(name = "ANIO")
  private BigInteger anio;
  @Column(name = "TOTAL_BASICO")
  private BigInteger totalBasico;
  @Column(name = "TOTAL_HABERES")
  private BigInteger totalHaberes;
  @Column(name = "TOTAL_DESCUENTOS")
  private BigInteger totalDescuentos;
  @Column(name = "TOTAL_ASIG_FILIARES")
  private BigInteger totalAsigFiliares;
  @Column(name = "TOTAL_NOMINAL")
  private BigInteger totalNominal;
  @Column(name = "TOTAL_HABER_REM")
  private BigInteger totalHaberRem;
  @Column(name = "TOTAL_HABER_NO_REM")
  private BigInteger totalHaberNoRem;
  @Column(name = "TOTAL_HABER_REM_P_MENS")
  private BigInteger totalHaberRemPMens;
  @Column(name = "TOTAL_HABER_NO_REM_P_MENS")
  private BigInteger totalHaberNoRemPMens;
  @Column(name = "TOTAL_HABER_MIN")
  private BigInteger totalHaberMin;
  @Column(name = "TOTAL_DESC_HABER_REM")
  private BigInteger totalDescHaberRem;
  @Column(name = "TOTAL_DESC_HABER_NO_REM")
  private BigInteger totalDescHaberNoRem;
  @Column(name = "TOTAL_DESC_LEY")
  private BigInteger totalDescLey;
  @Column(name = "TOTAL_NETO")
  private BigInteger totalNeto;
  @Column(name = "TOTAL_GANANCIA")
  private BigInteger totalGanancia;
  @Column(name = "TOTAL_SEG_VIDA")
  private BigInteger totalSegVida;
  @Column(name = "TOTAL_SAC_REM")
  private BigInteger totalSacRem;
  @Column(name = "TOTAL_SAC_NO_REM")
  private BigInteger totalSacNoRem;
  @Column(name = "TOTAL_BRUTO")
  private BigInteger totalBruto;
  @Column(name = "TOTAL_BANCO")
  private BigInteger totalBanco;
  @Column(name = "TOTAL_APORTE_PATRONAL")
  private BigInteger totalAportePatronal;
  @Column(name = "TOTAL_AJUSTE_REM")
  private BigInteger totalAjusteRem;
  @Column(name = "TOTAL_MIN_REM")
  private BigInteger totalMinRem;
  @Column(name = "TOTAL_MIN_NO_REM")
  private BigInteger totalMinNoRem;
  @Column(name = "PRS_ID")
  private BigInteger prsId;
  @Column(name = "SELECTIVO")
  private Character selectivo;
  @Column(name = "TIPO_PROCESO")
  private Character tipoProceso;
  @Column(name = "TOTAL_HABERES_REM_MES")
  private BigInteger totalHaberesRemMes;
  @Column(name = "TOTAL_HABERES_NO_REM_MES")
  private BigInteger totalHaberesNoRemMes;
  @Column(name = "MONTO_SAC_REM")
  private BigDecimal montoSacRem;
  @Column(name = "MONTO_SAC_NO_REM")
  private BigDecimal montoSacNoRem;
  @Column(name = "TOTAL_AJUSTE_NO_REM")
  private BigDecimal totalAjusteNoRem;
  @Column(name = "TOTAL_RETROACTIVOS_REM")
  private BigDecimal totalRetroactivosRem;
  @Column(name = "TOTAL_RETROACTIVOS_NO_REM")
  private BigDecimal totalRetroactivosNoRem;
  @Column(name = "TOTAL_HABER_REM_P_ESC1")
  private BigInteger totalHaberRemPEsc1;
  @Column(name = "TOTAL_HABER_NO_REM_P_ESC1")
  private BigInteger totalHaberNoRemPEsc1;
  @JoinColumn(name = "PRC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqProcesos prcId;
  @JoinColumn(name = "PPE_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqProcPasosEsc ppeId;

  public LiqTotalesMesPersona(BigDecimal id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqTotalesMesPersona[ id=" + id + " ]";
  }

}
