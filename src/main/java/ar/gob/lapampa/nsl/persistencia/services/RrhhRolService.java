package ar.gob.lapampa.nsl.persistencia.services;

import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.RegistrarRolesAUsuarioRequestDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.RolRequestDTO;

@Service
public interface RrhhRolService {

  GenericResponseDTO registrarRolesAUsuario(RegistrarRolesAUsuarioRequestDTO request);

  GenericResponseDTO listarRolesDisponibles(RolRequestDTO request);

  GenericResponseDTO listarRolesPorLogin(String login);

  GenericResponseDTO crearRol(RolRequestDTO request);

  GenericResponseDTO borrarRol(Long rolId);

  GenericResponseDTO modificarRol(RolRequestDTO request, Long rolId);

  GenericResponseDTO rolPorId(Long id);

}
