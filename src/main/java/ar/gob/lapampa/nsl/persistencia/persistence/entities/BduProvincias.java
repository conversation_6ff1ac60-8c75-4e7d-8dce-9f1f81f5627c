package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PROVINCIAS")
@NamedQueries(
    value = {@NamedQuery(name = "BduProvincias.findAll", query = "SELECT b FROM BduProvincias b")})
public class BduProvincias implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PAI_ID")
  private long paiId;
  @Size(max = 200)
  @Column(name = "NOMBRE")
  private String nombre;
  @Size(max = 10)
  @Column(name = "CODIGO")
  private String codigo;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ODT_ID")
  private long odtId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "prvId", fetch = FetchType.LAZY)
  private Set<BduDepartamentos> bduDepartamentosSet;

  public BduProvincias(Long id) {
    this.id = id;
  }

  public BduProvincias(Long id, long paiId, long odtId) {
    this.id = id;
    this.paiId = paiId;
    this.odtId = odtId;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduProvincias[ id=" + id + " ]";
  }

}
