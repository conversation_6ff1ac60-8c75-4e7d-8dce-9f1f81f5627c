package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ORIGENES")
@NamedQueries(
    value = {@NamedQuery(name = "BduOrigenes.findAll", query = "SELECT b FROM BduOrigenes b")})
public class BduOrigenes implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @OneToMany(mappedBy = "orgId", fetch = FetchType.LAZY)
  private Set<BduEntidades> bduEntidadesSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "orgId", fetch = FetchType.LAZY)
  private Set<BduOrigenesDatos> bduOrigenesDatosSet;

  public BduOrigenes(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduOrigenes[ id=" + id + " ]";
  }

}
