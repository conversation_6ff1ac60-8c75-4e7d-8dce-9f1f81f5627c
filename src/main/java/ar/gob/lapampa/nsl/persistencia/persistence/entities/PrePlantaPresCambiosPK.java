package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class PrePlantaPresCambiosPK implements Serializable {

  private static final long serialVersionUID = -8335828693114542216L;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PPR_ID")
  private long pprId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ITEM")
  private long item;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.PrePlantaPresCambiosPK[ pprId=" + pprId
        + ", item=" + item + " ]";
  }

}
