package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ACT_ENTID_PERF")
@NamedQueries(value = {
    @NamedQuery(name = "BduActEntidPerf.findAll", query = "SELECT b FROM BduActEntidPerf b")})
public class BduActEntidPerf implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Column(name = "NIVEL")
  private Short nivel;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "aceId", fetch = FetchType.LAZY)
  private Set<BduActEntidPerfEve> bduActEntidPerfEveSet;
  @JoinColumn(name = "ACT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduActores actId;
  @JoinColumn(name = "ENT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduEntidades entId;
  @JoinColumn(name = "TEV_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduEventosTipos tevId;
  @JoinColumn(name = "PFL_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduPerfiles pflId;

  public BduActEntidPerf(Long id) {
    this.id = id;
  }

  public BduActEntidPerf(Long id, Character activo) {
    this.id = id;
    this.activo = activo;
  }

}
