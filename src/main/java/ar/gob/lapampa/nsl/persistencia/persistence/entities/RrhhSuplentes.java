package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_SUPLENTES")
public class RrhhSuplentes implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRS_ID")
  private long prsId;
  @Column(name = "EST_ID")
  private Long estId;
  @Column(name = "PRT_ID")
  private Long prtId;
  @JoinColumn(name = "ESC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private PreEscalafones escId;

  public RrhhSuplentes(Long id) {

    this.id = id;
  }

  public RrhhSuplentes(Long id, long prsId) {

    this.id = id;
    this.prsId = prsId;
  }

}
