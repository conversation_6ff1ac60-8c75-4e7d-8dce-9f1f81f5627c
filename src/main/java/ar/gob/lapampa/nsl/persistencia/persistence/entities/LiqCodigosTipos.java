package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.List;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_CODIGOS_TIPOS")
public class LiqCodigosTipos implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "OCULTAR_EN_RECIBO")
  @Convert(converter = YesNoConverter.class)
  private Boolean ocultarEnRecibo;

  @OneToMany(mappedBy = "cotPadreId", fetch = FetchType.LAZY)
  private List<LiqCodigosTipos> liqCodigosTiposSet;

  @JoinColumn(name = "COT_PADRE_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqCodigosTipos cotPadreId;

  @OneToMany(mappedBy = "cotId", fetch = FetchType.LAZY)
  private List<LiqCodigos> liqCodigosSet;

  public LiqCodigosTipos(Long id) {
    this.id = id;
  }

}
