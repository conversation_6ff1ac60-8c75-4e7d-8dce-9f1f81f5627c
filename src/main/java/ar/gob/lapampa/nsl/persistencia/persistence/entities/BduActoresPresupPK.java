package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Embeddable
public class BduActoresPresupPK implements Serializable {

  private static final long serialVersionUID = 1L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "SIS_ID")
  private long sisId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACT_ID")
  private long actId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ORP_ID")
  private long orpId;

  public BduActoresPresupPK(long sisId, long actId, long orpId) {
    this.sisId = sisId;
    this.actId = actId;
    this.orpId = orpId;
  }

}
