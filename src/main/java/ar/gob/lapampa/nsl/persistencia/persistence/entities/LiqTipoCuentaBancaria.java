package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_TIPO_CTA_BANCARIA")
public class LiqTipoCuentaBancaria implements Serializable {

  private static final long serialVersionUID = -6504317356031833153L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Basic(optional = false)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Basic(optional = false)
  @Column(name = "CODIGO")
  private Integer codigo;

  public LiqTipoCuentaBancaria(Long id) {
    this.id = id;
  }

}
