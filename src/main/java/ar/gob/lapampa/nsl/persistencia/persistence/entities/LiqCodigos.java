package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_CODIGOS")
@SequenceGenerator(name = "LIQ_COD_SEQ", sequenceName = "LIQ_COD_SEQ", allocationSize = 1)
public class LiqCodigos implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_COD_SEQ")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 12)
  @Column(name = "NRO")
  private String nro;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "INGRESABLE_CONCEPTO_MENSUAL")
  @Convert(converter = YesNoConverter.class)
  private Boolean ingresableConceptoMensual;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 100)
  @Column(name = "NOMBRE")
  private String nombre;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 20)
  @Column(name = "NOMBRE_CORTO")
  private String nombreCorto;

  @Size(max = 4000)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Column(name = "ORDEN")
  private Long orden;

  @Column(name = "CODSUE")
  private Long codsue;

  @Column(name = "FUENTE")
  private Integer fuente;

  @Column(name = "SUBFUENTE")
  private Integer subfuente;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "IMPUTA_ACT_AFECTADA")
  @Convert(converter = YesNoConverter.class)
  private Boolean imputaActAfectada;

  @Column(name = "RECALCULABLE")
  private Character recalculable;

  @Column(name = "ORDEN_REC")
  private Long ordenRec;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "codId", fetch = FetchType.LAZY)
  private Set<RrhhAfiliaciones> rrhhAfiliacionesSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "codId", fetch = FetchType.LAZY)
  private Set<LiqFirmasDetalle> liqFirmasDetalleSet;

  @OneToMany(mappedBy = "codId", fetch = FetchType.LAZY)
  private Set<LiqEmbargos> liqEmbargosSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "codId", fetch = FetchType.LAZY)
  private Set<LiqDescuentosInformados> liqDescuentosInformadosSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "liqCodigos", fetch = FetchType.LAZY)
  private Set<LiqCodigosNormas> liqCodigosNormasSet;

  // @OneToMany(mappedBy = "codId", fetch = FetchType.LAZY)
  // private Set<LiqCuotasAlimentarias> liqCuotasAlimentariasSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "codId", fetch = FetchType.LAZY)
  private Set<RrhhOcupacionesConceptos> rrhhOcupacionesConceptosSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "codId", fetch = FetchType.LAZY)
  private Set<LiqDescuentos> liqDescuentosSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "codId", fetch = FetchType.LAZY)
  private Set<LiqCodigosVersiones> liqCodigosVersionesSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "codId", fetch = FetchType.LAZY)
  private Set<SiafCuentasEscalafon> siafCuentasEscalafonSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "codId", fetch = FetchType.LAZY)
  private Set<LiqRecalculosDetalle> liqRecalculosDetalleSet;

  @OneToMany(mappedBy = "codPadreId", fetch = FetchType.LAZY)
  private Set<LiqCodigos> liqCodigosSet;

  @JoinColumn(name = "COD_PADRE_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqCodigos codPadreId;

  @JoinColumn(name = "COT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private LiqCodigosTipos cotId;

  @JoinColumn(name = "DIT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqDtosInformadosTipos ditId;

  @JoinColumn(name = "MOD_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqModalidades modId;

  @JoinColumn(name = "ESC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private PreEscalafones escId;

  @JoinColumn(name = "CUP_ID_TMP", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private SiafCuentasPresupuestarias cupIdTmp;

  @JoinColumn(name = "CUP_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private SiafCuentasPresupuestarias cupId;

  @JoinColumn(name = "PRO_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private SiafProveedores proId;

  public LiqCodigos(Long id) {
    this.id = id;
  }

}
