package ar.gob.lapampa.nsl.persistencia.controllers;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import jakarta.servlet.Filter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringBootVersion;
import org.springframework.http.ResponseEntity;
import org.springframework.security.web.FilterChainProxy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Main Application Controller Defines default check end points
 * 
 * @version 1.0.0
 */
@RestController
@RequestMapping
@NoArgsConstructor
@Slf4j
public class AplicacionController {

  /**
   * Filter Chain
   */
  @Autowired
  @Qualifier("springSecurityFilterChain")
  private Filter filterChain;

  /**
   * application in bootstrap.yml
   */
  @Value("${application}")
  private String application;

  /**
   * Version in pom.xml
   */
  @Value("${project.version}")
  private String version;

  /**
   * Current {@link spring.datasource.username} user defined in pom.xml
   */
  @Value("${spring.datasource.username}")
  private String dbUser;

  /**
   * Current {@link spring.profiles.active} active profile defined in app yml
   */
  @Value("${spring.profiles.active}")
  private String activeProfile;

  /**
   * Current {@link spring.datasource.hikari.pool-name} pool name defined in app yml
   */
  @Value("${spring.datasource.hikari.pool-name}")
  private String poolName;

  /**
   * Current {@link config-origin} configurations origin
   */
  @Value("${config-origin}")
  private String configOrigin;

  /**
   * environment in boostarp.yml
   */
  @Value("${environment}")
  private String runtimeEnv;

  /**
   * Check de estado
   * 
   * @return String
   */
  @Operation(summary = "Estado del servicio", description = "Devuelve estado del servicio",
      tags = {"Status"}, hidden = false, deprecated = false)
  @GetMapping("/estado")
  public ResponseEntity<String> healthCheck() {
    final StringBuilder msj = new StringBuilder(255);
    final String javaVersion = System.getProperty("java.version");
    final String springVersion = SpringBootVersion.getVersion();
    final Map<Integer, Map<Integer, String>> filters = getSecurityFilterChainProxy();

    msj.append(application + ": Ok\nProject version: ").append(version).append("\nJava version: ")
        .append(javaVersion).append("\nDB User: ").append(dbUser).append("\nProfile activo: ")
        .append(activeProfile).append("\nConfigs desde: ").append(configOrigin)
        .append("\nPool Name: ").append(poolName).append("\nSpring Boot version: ")
        .append(springVersion).append("\nRuntime Env.: ").append(runtimeEnv).append("\nFilters:");

    filters.get(1).forEach((i, s) -> msj.append("\n" + i + ": " + s));
    return ResponseEntity.ok(msj.toString());
  }

  /**
   * Check logging levels
   * 
   * @return String
   */
  @Operation(summary = "Check de logs con lombok",
      description = "Dispara un log de cada tipo para verificar", tags = {"Status"}, hidden = false,
      deprecated = false)
  @GetMapping("/logsTest")
  public String logsCheck() {
    log.trace("Logs check: TRACE Message");
    log.debug("Logs check: DEBUG Message");
    log.info("Logs check: INFO Message");
    log.warn("Logs check: WARN Message");
    log.error("Logs check: ERROR Message");
    return "Ok! Check salida de Logs...";
  }

  private Map<Integer, Map<Integer, String>> getSecurityFilterChainProxy() {
    final Map<Integer, Map<Integer, String>> filterChains = new ConcurrentHashMap<>();
    int index = 1;
    for (final SecurityFilterChain secfc : this.getFilters()) {
      // filters.put(i++, secfc.getClass().getName())
      final Map<Integer, String> filters = new ConcurrentHashMap<>();
      int subindex = 1;
      for (final Filter filter : secfc.getFilters()) {
        filters.put(subindex++, filter.getClass().getName());
      }
      filterChains.put(index++, filters);
    }
    return filterChains;
  }

  private List<SecurityFilterChain> getFilters() {
    final FilterChainProxy filterChainProxy = (FilterChainProxy) filterChain;
    return filterChainProxy.getFilterChains();
  }
}
