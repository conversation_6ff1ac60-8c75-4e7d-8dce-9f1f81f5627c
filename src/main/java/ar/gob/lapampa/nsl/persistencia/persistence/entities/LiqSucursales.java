package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_SUCURSALES")
public class LiqSucursales implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 400)
  @Column(name = "NOMBRE")
  private String nombre;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 20)
  @Column(name = "CODIGO_POSTAL")
  private String codigoPostal;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "NRO")
  private Long nro;

  public LiqSucursales(Long id) {
    this.id = id;
  }

}
