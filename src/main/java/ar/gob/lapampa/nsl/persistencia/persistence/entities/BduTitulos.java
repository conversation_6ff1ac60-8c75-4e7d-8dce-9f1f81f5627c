package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_TITULOS")
public class BduTitulos implements Serializable {

  private static final long serialVersionUID = 362325862417629182L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Basic(optional = false)
  @Column(name = "OFICIAL")
  private String oficial;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Column(name = "CANT_ANIOS")
  private Short cantAnios;

  @Column(name = "AMBITO")
  private String ambito;

  @Column(name = "CODIGO")
  private String codigo;

  @ManyToMany(mappedBy = "bduTitulosSet", fetch = FetchType.LAZY)
  private Set<BduIncumbencias> bduIncumbenciasSet;

  @JoinColumn(name = "TLT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduTitulosTipos tltId;

  public BduTitulos(Long id) {
    this.id = id;
  }

}
