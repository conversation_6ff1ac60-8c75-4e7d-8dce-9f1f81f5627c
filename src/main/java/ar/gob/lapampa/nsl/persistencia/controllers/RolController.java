package ar.gob.lapampa.nsl.persistencia.controllers;

import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.RegistrarRolesAUsuarioRequestDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.RolRequestDTO;
import ar.gob.lapampa.nsl.persistencia.security.CustomSecurityEvaluator;
import ar.gob.lapampa.nsl.persistencia.services.RrhhRolService;

/**
 * RRHHUsuario Controller
 * 
 * <AUTHOR> NSL
 *
 */
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/v1/app/roles")
public class RolController {

  private final CustomSecurityEvaluator securityEvaluator;

  public RolController(CustomSecurityEvaluator securityEvaluator) {
    this.securityEvaluator = securityEvaluator;
  }

  @Autowired
  private RrhhRolService rolService;

  @PostMapping("/registrarRolesAUsuario")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','USUARIO_R, ROL_R, ROLUSUARIO_W','','IS')")
  public @ResponseBody GenericResponseDTO registrarRolesAUsuario(
      @RequestBody RegistrarRolesAUsuarioRequestDTO request) {

    return rolService.registrarRolesAUsuario(request);
  }

  @PostMapping("/listar-roles-disponibles")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','ROL_R','','IS')")
  public GenericResponseDTO listarRolesDisponibles(@RequestBody RolRequestDTO request) {
    return rolService.listarRolesDisponibles(request);
  }

  @GetMapping("/rol-por-id/{id}")
  @PreAuthorize("@cs.hasAccess('','ROL_R','','IS')")
  @Transactional
  public GenericResponseDTO obtenerRolPorId(@PathVariable Long id) {
    return rolService.rolPorId(id);
  }

  @GetMapping("/listar-roles-por-login/{login}")
  @PreAuthorize("@cs.hasAccess('','ROL_R','','IS')")
  @Transactional
  public GenericResponseDTO listarRolesPorLogin(@PathVariable String login) {
    return rolService.listarRolesPorLogin(login);
  }

  @PostMapping("/crear-rol")
  @Transactional
  // @PreAuthorize("hasAuthority('USER_WRITE') and
  // hasRole('ADMINISTRADOR')")
  public @ResponseBody GenericResponseDTO crearRol(@RequestBody RolRequestDTO request) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("ROL_W");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/roles/crear-rol", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return rolService.crearRol(request);
  }

  @DeleteMapping("/eliminar-rol/{rolId}")
  @Transactional
  // @PreAuthorize("hasAuthority('USER_WRITE') and
  // hasRole('ADMINISTRADOR')")
  public @ResponseBody GenericResponseDTO borrarRol(@PathVariable Long rolId) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("ROL_R", "ROL_W");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/roles/eliminar-rol", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return rolService.borrarRol(rolId);
  }

  @PutMapping("/modificar-rol/{rolId}")
  @Transactional
  // @PreAuthorize("hasAuthority('USER_WRITE') and
  // hasRole('ADMINISTRADOR')")
  public @ResponseBody GenericResponseDTO modificarRol(@RequestBody RolRequestDTO request,
      @PathVariable Long rolId) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("ROL_R", "ROL_W");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/roles/modificar-rol", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return rolService.modificarRol(request, rolId);
  }
}
