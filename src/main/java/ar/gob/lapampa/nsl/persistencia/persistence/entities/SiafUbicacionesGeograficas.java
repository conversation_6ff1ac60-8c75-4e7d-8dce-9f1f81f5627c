package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "SIAF_UBICACIONES_GEOGRAFICAS")
@NamedQueries(value = {@NamedQuery(name = "SiafUbicacionesGeograficas.findAll",
    query = "SELECT s FROM SiafUbicacionesGeograficas s")})
public class SiafUbicacionesGeograficas implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "NRO")
  private String nro;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "DEP_ID")
  private long depId;

  public SiafUbicacionesGeograficas(Long id) {
    this.id = id;
  }

  public SiafUbicacionesGeograficas(Long id, long depId) {
    this.id = id;
    this.depId = depId;
  }

}
