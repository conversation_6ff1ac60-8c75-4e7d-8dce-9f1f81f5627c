package ar.gob.lapampa.nsl.persistencia.services.impl;

import static org.apache.commons.lang.WordUtils.capitalizeFully;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.MenuRequestDTO;
import ar.gob.lapampa.nsl.persistencia.models.MenuModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QRrhhMenu;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhMenu;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhRol;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.RrhhMenuMapper;
import ar.gob.lapampa.nsl.persistencia.repositories.RrhhMenuRepository;
import ar.gob.lapampa.nsl.persistencia.repositories.RrhhRolRepository;
import ar.gob.lapampa.nsl.persistencia.services.RrhhMenuService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class RrhhMenuServiceImpl implements RrhhMenuService {

  @Autowired
  private RrhhMenuRepository rrhhMenuRepository;

  @Autowired
  private RrhhRolRepository rrhhRolRepository;

  @PersistenceContext
  private EntityManager entityManager;

  @Override
  public GenericResponseDTO listarMenusLegacy() {
    final GenericResponseDTO response = new GenericResponseDTO();
    List<RrhhMenu> listaMenus = rrhhMenuRepository.findAll();
    List<MenuModel> listaMenusModel = new ArrayList<>();

    for (RrhhMenu menu : listaMenus) {
      MenuModel menuModel = RrhhMenuMapper.rrhhMenuToRrhhMenuModel(menu);
      listaMenusModel.add(menuModel);
    }
    listaMenusModel.sort(Comparator.comparing(MenuModel::getNombre));
    // Establecer la respuesta exitosa con la lista de menus
    response.setEstadoExito(listaMenusModel);
    return response;
  }

  public GenericResponseDTO actualizarMenusByRol(List<MenuModel> request, Long rolId) {
    final GenericResponseDTO response = new GenericResponseDTO();

    try {
      Optional<RrhhRol> rolOptional = rrhhRolRepository.findById(rolId);
      if (rolOptional.isPresent()) {
        // aseguramos valores unicos
        Set<Long> idsNuevosMenus =
            request.stream().map(MenuModel::getId).collect(Collectors.toSet());
        // Convertir el set de longs a una lista de strings separados por comas
        String menusListStr =
            idsNuevosMenus.stream().map(String::valueOf).collect(Collectors.joining(","));

        // Sincronizamos con la DB
        String result = rrhhMenuRepository.syncMenusRoles(rolId, menusListStr);

        // Verificar si el resultado es nulo antes de usarlo
        if (result == null) {
          response.setEstadoError("La operación en base de datos no devolvió un resultado.");
          log.warn("El stored procedure para rol ID {} devolvió null.", rolId);
          return response;
        } else if (result.toUpperCase().contains("ERROR")) {
          response.setEstadoError(result);
          log.warn("Error devuelto por el stored procedure para rol ID {}: {}", rolId, result);
          return response;
        } else {
          response.setEstadoExito("Ok");
          response.setMensaje(result);
          log.info("Permisos actualizados mediante stored procedure. Resultado: {}", result);
          return response;
        }
      } else {
        response.setEstadoError("Rol con ID " + rolId + " no encontrado.");
        log.warn("Intento de actualizar permisos de rol no existente: ID={}", rolId);
      }
    } catch (Exception e) {
      log.error("Error al actualizar los menus para el rol: {}", rolId, e);
      response.setEstadoError("Error interno al actualizar los menus: " + e.getMessage());
    }
    return response;
  }

  @Override
  public GenericResponseDTO getById(Long menuId) {
    final GenericResponseDTO response = new GenericResponseDTO();
    try {
      Optional<RrhhMenu> optionalMenu = rrhhMenuRepository.findById(menuId);
      if (optionalMenu.isPresent()) {
        response.setEstadoExito(optionalMenu.get());
      } else {
        response.setEstadoError("Menu no hallado para el Id: " + menuId);
      }
    } catch (Exception e) {
      log.error("Error al obtener menu por id: {}", menuId, e);
      response.setEstadoError("Error interno al obtener menu: " + e.getMessage());
    }
    return response;
  }

  @Override
  public GenericResponseDTO updateById(MenuRequestDTO request, Long id) {
    final GenericResponseDTO response = new GenericResponseDTO();
    try {
      // Validate required fields
      if (request.getNombre() == null || request.getNombre().trim().isEmpty()) {
        response.setEstadoError("El nombre del menu es requerido");
        return response;
      }

      Optional<RrhhMenu> optionalMenu = rrhhMenuRepository.findById(id);
      if (optionalMenu.isPresent()) {
        RrhhMenu menu = optionalMenu.get();
        menu.setNombre(request.getNombre().trim());
        menu.setEstado(request.getEstado());
        menu.setIcono(request.getIcono());
        menu.setAbstracto(request.getAbstracto());

        // Handle menu parent relationship if provided
        if (request.getMenuPadreId() != null) {
          // Validate that parent menu exists and is not the same as current menu
          if (request.getMenuPadreId().equals(id)) {
            response.setEstadoError("Un menu no puede ser padre de si mismo");
            return response;
          }

          Optional<RrhhMenu> parentMenu = rrhhMenuRepository.findById(request.getMenuPadreId());
          if (parentMenu.isPresent()) {
            menu.setMenuPadreId(parentMenu.get());
          } else {
            response.setEstadoError("Menu padre no encontrado con ID: " + request.getMenuPadreId());
            return response;
          }
        } else {
          menu.setMenuPadreId(null);
        }

        RrhhMenu menuGuardado = rrhhMenuRepository.save(menu);
        response.setEstadoExito(menuGuardado);
        log.info("Menu actualizado exitosamente con ID: {} y nombre: {}", menuGuardado.getId(),
            menuGuardado.getNombre());
      } else {
        response.setEstadoError("Menu no hallado para el Id: " + id);
      }
    } catch (Exception e) {
      log.error("Error al actualizar menu por id: {}", id, e);
      response.setEstadoError("Error interno al actualizar menu: " + e.getMessage());
    }

    return response;
  }

  @Override
  public GenericResponseDTO create(MenuRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();
    try {
      // Validaciones
      if (request.getNombre() == null || request.getNombre().trim().isEmpty()) {
        response.setEstadoError("El nombre del menu es requerido");
        return response;
      }

      Optional<RrhhMenu> existeNombre =
          rrhhMenuRepository.findOne(QRrhhMenu.rrhhMenu.nombre.eq(request.getNombre()));
      if (existeNombre.isPresent()) {
        response.setEstadoError("Ya existe un menu con el nombre interno: " + request.getNombre()
            + " (derivado de '" + request.getNombre() + "')");
        log.warn("Intento de crear menu duplicado: {}", request.getNombre());
        return response;
      }

      Optional<RrhhMenu> existeEstado =
          rrhhMenuRepository.findOne(QRrhhMenu.rrhhMenu.estado.eq(request.getEstado()));
      if (existeEstado.isPresent()) {
        response.setEstadoError("Ya existe un menu con el estado interno: " + request.getEstado()
            + " (derivado de '" + request.getEstado() + "')");
        log.warn("Intento de crear menu con estado duplicado: {}", request.getEstado());
        return response;
      }


      log.info("Creando menu con nombre: {}", request.getNombre());
      log.info("Request ID recibido: {}", request.getId());

      // Crear una nueva instancia completamente limpia
      RrhhMenu menu = new RrhhMenu();
      menu.setId(null);
      log.info("Menu creado con método factory, ID inicial: {}", menu.getId());

      menu.setNombre(capitalizeFully(request.getNombre().toLowerCase().trim()));
      menu.setEstado(request.getEstado());
      menu.setIcono(request.getIcono());
      menu.setAbstracto(request.getAbstracto());

      // Verificamos si el menu padre existe
      if (request.getMenuPadreId() != null) {
        Optional<RrhhMenu> parentMenu = rrhhMenuRepository.findById(request.getMenuPadreId());
        if (parentMenu.isPresent()) {
          menu.setMenuPadreId(parentMenu.get());
          log.info("Menu padre establecido con ID: {}", parentMenu.get().getId());
        } else {
          response.setEstadoError("Menu padre no encontrado con ID: " + request.getMenuPadreId());
          return response;
        }
      }

      log.info("Antes de persist(), ID del menu: {}", menu.getId());

      // Verificación final: asegurar que el ID esté null
      if (menu.getId() != null) {
        log.warn("ADVERTENCIA: El ID no es null antes de persist(): {}", menu.getId());
        menu.setId(null);
        log.info("ID forzado a null nuevamente");
      }

      // Usar EntityManager.persist() para garantizar que se use la secuencia en lugar de
      // Repository.save(menu)
      entityManager.persist(menu);
      entityManager.flush(); // Forzar la persistencia inmediata

      log.info("Después de persist() y flush(), ID del menu: {}", menu.getId());

      // Mapear a MenuModel para la respuesta
      MenuModel menuModel = RrhhMenuMapper.rrhhMenuToRrhhMenuModel(menu);
      response.setEstadoExito(menuModel);
      response.setMensaje("Menu '" + request.getNombre() + "' creado exitosamente.");
      log.info("Menu creado: ID={}, NombreInterno={}", menu.getId(), menu.getNombre());

    } catch (Exception e) {
      log.error("Error al crear menu: {}", request.getNombre(), e);
      response.setEstadoError("Error interno al crear menu: " + e.getMessage());
    }
    return response;
  }

  @Override
  public GenericResponseDTO deleteById(Long id) {
    final GenericResponseDTO response = new GenericResponseDTO();
    try {
      Optional<RrhhMenu> optionalMenu = rrhhMenuRepository.findById(id);
      if (optionalMenu.isPresent()) {
        rrhhMenuRepository.deleteById(id);
        response.setEstadoExito(id);
      } else {
        response.setEstadoError("Menu no hallado para el Id: " + id);
      }
    } catch (Exception e) {
      log.error("Error al borrar menu por id: {}", id, e);
      response.setEstadoError("Error interno al borrar menu: " + e.getMessage());
    }
    return response;
  }

}
