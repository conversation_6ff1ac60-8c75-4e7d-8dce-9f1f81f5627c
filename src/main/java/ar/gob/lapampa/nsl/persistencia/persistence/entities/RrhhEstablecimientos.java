package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_ESTABLECIMIENTOS")
@SequenceGenerator(name = "RRHH_EST_SEQ", sequenceName = "RRHH_EST_SEQ", allocationSize = 1)
public class RrhhEstablecimientos implements Serializable {

  private static final long serialVersionUID = 4306449889801231567L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_EST_SEQ")
  private Long id;

  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Column(name = "NRO")
  private Long nro;

  @Column(name = "CUE")
  private Long cue;

  @Column(name = "TELEFONO")
  private String telefono;

  @Column(name = "DOMICILIO")
  private String domicilio;

  public RrhhEstablecimientos(Long id) {
    this.id = id;
  }

}
