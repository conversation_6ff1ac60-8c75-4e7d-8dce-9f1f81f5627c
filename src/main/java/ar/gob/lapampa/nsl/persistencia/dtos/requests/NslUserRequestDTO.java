package ar.gob.lapampa.nsl.persistencia.dtos.requests;

import jakarta.validation.constraints.NotNull;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class NslUserRequestDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 8023764303714935532L;
  private String username;
  private String email;
  private String apellidos;
  private String nombres;

  @NotNull
  private Integer size;
  @NotNull
  private Integer page;
  @NotNull
  private String sortBy;
  private Boolean asc;
}
