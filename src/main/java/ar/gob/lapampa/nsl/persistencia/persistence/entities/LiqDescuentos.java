package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> <PERSON>SL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_DESCUENTOS")
@NamedQueries(
    value = {@NamedQuery(name = "LiqDescuentos.findAll", query = "SELECT l FROM LiqDescuentos l")})
public class LiqDescuentos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Column(name = "PRS_ID")
  private Long prsId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MES")
  private short mes;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANIO")
  private short anio;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Column(name = "MONTO_A_DESCONTAR")
  private BigDecimal montoADescontar;
  @Column(name = "PORC_MENSUAL")
  private BigDecimal porcMensual;
  @Column(name = "MONTO_DESCONTADO")
  private BigDecimal montoDescontado;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ARCHIVO_ID")
  private long archivoId;
  @Column(name = "COD_NRO")
  private Short codNro;
  @Column(name = "SECUENCIAL")
  private Integer secuencial;
  @Column(name = "DNI")
  private Integer dni;
  @Column(name = "COD_NRO2")
  private Short codNro2;
  @Column(name = "CUOTA")
  private Short cuota;
  @Column(name = "SUC_OTROS")
  private Long sucOtros;
  @Column(name = "NRO_CUENTA")
  private Long nroCuenta;
  @Column(name = "CUIL")
  private Long cuil;
  @Column(name = "SEPARADOR")
  private Short separador;
  @Column(name = "OCU_ID")
  private Long ocuId;
  @Size(max = 2000)
  @Column(name = "COMENTARIO")
  private String comentario;
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "liqDescuentos", fetch = FetchType.LAZY)
  private Set<LiqDescuentosDetalle> liqDescuentosDetalleSet;
  @JoinColumn(name = "COD_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqCodigos codId;
  @JoinColumn(name = "LDI_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqDescuentosInformados ldiId;

  public LiqDescuentos(Long id) {
    this.id = id;
  }

  public LiqDescuentos(Long id, short mes, short anio, long archivoId) {
    this.id = id;
    this.mes = mes;
    this.anio = anio;
    this.archivoId = archivoId;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqDescuentos[ id=" + id + " ]";
  }

}
