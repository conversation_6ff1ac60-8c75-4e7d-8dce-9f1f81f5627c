package ar.gob.lapampa.nsl.persistencia.utils;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

/**
 * Clase que define grupos de permisos y roles. Preferir mayúsculas y sin prefijos NNSL_ o ROLE_
 * para los elementos separados por comas.
 */
@Component("gp")
public class Groups {
  public static String combined(String... permisos) {

    // Dividir cada string por comas, eliminar espacios en blanco, y recopilar todos sin repetir
    Set<String> uniquePermisos = Arrays.stream(permisos)
        .flatMap(p -> Arrays.stream(p.split("\\s*,\\s*"))).collect(Collectors.toSet());

    // Unir los únicos con comas
    return String.join(", ", uniquePermisos);
  }

  // Aca definimos grupos de roles y permisos.
  // Importante sufijos ROLES y PERMS (o distinto ROLES) para distinguir
  // Ejemplo hacemos un grupo de roles para LIQ_PROCESOS
  public static final String LIQ_PROCESOS_ROLES = "ADMINISTRADOR, LIQUIDADOR";
  // Y otro grupo de permisos para LIQ_PROCESOS
  public static final String LIQ_PROCESOS_PERMS =
      "USER_READ, USER_WRITE, PROCESO_READ, PROCESO_WRITE";
  // Y otro grupo de roles para AUDITOR observar el sufijo ROLES y que además repetimos el rol
  // LIQUIDADOR
  // para que el combined() lo elimine por ser repetido
  public static final String LIQ_PROCESOS_AUDIT_ROLES = "AUDITOR, LIQUIDADOR";


}

