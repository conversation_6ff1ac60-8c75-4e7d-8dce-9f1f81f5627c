package ar.gob.lapampa.nsl.persistencia.models;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import com.querydsl.core.annotations.QueryProjection;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RolFullModel extends BaseRRHHDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 8825100916191992407L;
  private String nombre;
  private String descripcion;
  private List<AccionModel> acciones;
  private List<MenuModel> menues;
  private List<EmpresaModel> empresas;
  private List<UsuarioModel> usuarios;

  public RolFullModel(String nombre) {
    super();
    this.nombre = nombre;
  }

  @QueryProjection
  public RolFullModel(Long id) {
    super();
    this.id = id;
  }

  /**
   * Obtiene Nombre de class name solo
   * 
   * @return String
   */
  @Override
  public String getBeanName() {
    return RolFullModel.class.getSimpleName();
  }

  /**
   * Obtiene Nombre de package + class name
   * 
   * @return String
   */
  @Override
  public String getCualifiedName() {
    return RolFullModel.class.getCanonicalName();
  }
}
