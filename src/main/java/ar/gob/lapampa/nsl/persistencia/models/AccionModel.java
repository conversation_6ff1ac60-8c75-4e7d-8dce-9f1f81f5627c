package ar.gob.lapampa.nsl.persistencia.models;

import java.io.Serial;
import java.io.Serializable;
import com.querydsl.core.annotations.QueryProjection;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AccionModel extends BaseRRHHDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 8725100916191992407L;
  private String nombre;
  private String endpoint;
  private String nota;

  public AccionModel(String nombre) {
    super();
    this.nombre = nombre;
  }

  @QueryProjection
  public AccionModel(Long id) {
    super();
    this.id = id;
  }

  /**
   * Obtiene Nombre de class name solo
   * 
   * @return String
   */
  @Override
  public String getBeanName() {
    return AccionModel.class.getSimpleName();
  }

  /**
   * Obtiene Nombre de package + class name
   * 
   * @return String
   */
  @Override
  public String getCualifiedName() {
    return AccionModel.class.getCanonicalName();
  }
}
