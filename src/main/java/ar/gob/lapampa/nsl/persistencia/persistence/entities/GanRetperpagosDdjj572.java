package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_RETPERPAGOS_DDJJ572")
@SequenceGenerator(name = "GAN_RETPERPAGOS_DDJJ572_SQ", sequenceName = "GAN_RETPERPAGOS_DDJJ572_SQ",
    allocationSize = 1)
public class GanRetperpagosDdjj572 implements Serializable {

  private static final long serialVersionUID = 3106134227576527420L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_RETPERPAGOS_DDJJ572_SQ")
  private Long id;

  @Column(name = "DESCBASICA")
  private String descbasica;

  @Column(name = "DESCADICIONAL")
  private String descadicional;

  @Column(name = "MONTOTOTAL")
  private BigDecimal montototal;

  @JoinColumn(name = "PRESENTACION", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER, cascade = CascadeType.ALL)
  private GanPresentDdjj572 presentacion;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "retPerPagos", fetch = FetchType.LAZY)
  private List<GanRetperpagosDetDdjj572> detalles;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "retPerPagos", fetch = FetchType.LAZY)
  private List<GanRetperpagosPerDdjj572> periodos;

  @Column(name = "TIPO")
  private Long tipo;

}
