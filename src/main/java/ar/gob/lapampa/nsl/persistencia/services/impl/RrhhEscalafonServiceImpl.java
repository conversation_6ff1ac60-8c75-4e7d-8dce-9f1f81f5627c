package ar.gob.lapampa.nsl.persistencia.services.impl;

import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.RegistrarEscalafonesAUsuarioRequestDTO;
import ar.gob.lapampa.nsl.persistencia.services.RrhhEscalafonService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class RrhhEscalafonServiceImpl implements RrhhEscalafonService {


  @Override
  public GenericResponseDTO registrarEscalafonesAUsuario(
      RegistrarEscalafonesAUsuarioRequestDTO request) {
    // TODO Auto-generated method stub
    return null;
  }

}
