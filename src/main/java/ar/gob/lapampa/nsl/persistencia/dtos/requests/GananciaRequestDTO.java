package ar.gob.lapampa.nsl.persistencia.dtos.requests;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import jakarta.validation.constraints.NotNull;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class GananciaRequestDTO extends PageRequest {

  protected GananciaRequestDTO(int page, int size, Sort sort) {
    super(page, size, Sort.unsorted());
  }

  private static final long serialVersionUID = 7515740547167713113L;

  @NotNull
  private Long corrida;

  private Long agente;

}
