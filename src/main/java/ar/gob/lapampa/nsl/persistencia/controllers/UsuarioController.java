package ar.gob.lapampa.nsl.persistencia.controllers;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.RrhhUsuarioDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.NslUserRequestDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.SyncUsuariosRequestDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.UsuarioRequestDTO;
import ar.gob.lapampa.nsl.persistencia.security.CustomSecurityEvaluator;
import ar.gob.lapampa.nsl.persistencia.services.RrhhUsuarioService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

/**
 * RRHHUsuario Controller
 * 
 * <AUTHOR> NSL
 *
 */
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/v1/app/usuarios")
public class UsuarioController {

  private final CustomSecurityEvaluator securityEvaluator;

  public UsuarioController(CustomSecurityEvaluator securityEvaluator) {
    this.securityEvaluator = securityEvaluator;
  }

  @Autowired
  private RrhhUsuarioService usuarioService;

  @Operation(summary = "Listado paginado de usuarios RRHH",
      description = "Recibe  parámetros  por body con filtro y paginado", tags = {"Usuarios"},
      hidden = false, deprecated = false)
  @ApiResponses({@ApiResponse(responseCode = "200", description = "Listado de una pagina"),
      @ApiResponse(responseCode = "400", description = "Bad Request")})
  @PostMapping("/listar")
  @Transactional
  // @PreAuthorize("hasAuthority('PERMISSION_USERS_READ') and
  // hasRole('PRESUPUESTO')")
  public @ResponseBody GenericResponseDTO listar(@RequestBody UsuarioRequestDTO request) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("USER_READ");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/usuarios/listar", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso

    return usuarioService.listar(request);
  }

  @PostMapping("/registrar")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','USER_READ, USER_WRITE','','IS')")
  public @ResponseBody GenericResponseDTO registrar(@RequestBody RrhhUsuarioDTO rrhhUsuariosDTO) {

    return usuarioService.registrar(rrhhUsuariosDTO);
  }

  @PostMapping("/modificar")
  @PreAuthorize("@cs.hasAccess('','USER_READ, USER_WRITE','','IS')")
  public @ResponseBody GenericResponseDTO modificar(@RequestBody RrhhUsuarioDTO rrhhUsuariosDTO) {

    return usuarioService.modificar(rrhhUsuariosDTO);
  }

  @PostMapping("/eliminar")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','USER_READ, USER_WRITE','','IS')")
  public @ResponseBody GenericResponseDTO eliminar(@RequestBody RrhhUsuarioDTO rrhhUsuariosDTO) {

    return usuarioService.eliminar(rrhhUsuariosDTO);
  }

  @PostMapping("/deshabilitar")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','USER_READ, USER_WRITE','','IS')")
  public @ResponseBody GenericResponseDTO deshabilitar(
      @RequestBody RrhhUsuarioDTO rrhhUsuariosDTO) {

    return usuarioService.deshabilitar(rrhhUsuariosDTO);
  }

  @PostMapping("/habilitar")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','USER_READ, USER_WRITE','','IS')")
  public @ResponseBody GenericResponseDTO habilitar(@RequestBody RrhhUsuarioDTO rrhhUsuariosDTO) {

    return usuarioService.habilitar(rrhhUsuariosDTO);
  }

  @GetMapping("/auth")
  @PreAuthorize("hasRole('ROLE_ADMINISTRADOR')")
  public Map<String, Object> getPrincipalInfo() {
    Authentication principal = SecurityContextHolder.getContext().getAuthentication();

    Collection<String> authorities =
        principal.getAuthorities().stream().map(GrantedAuthority::getAuthority).toList();

    Map<String, Object> info = new HashMap<>();
    info.put("name: ", principal.getName());
    info.put("authorities: ", authorities);
    info.put("isAutheticated: ", principal.isAuthenticated());

    return info;
  }

  @Operation(summary = "Listado paginado de usuarios RRHH",
      description = "Recibe  parámetros  por body con filtro y paginado", tags = {"Usuarios"},
      hidden = false, deprecated = false)
  @ApiResponses({@ApiResponse(responseCode = "200", description = "Listado de una pagina"),
      @ApiResponse(responseCode = "400", description = "Bad Request")})
  @PostMapping("/listarnsl")
  @Transactional
  // @PreAuthorize("hasAuthority('USER_READ') and
  // hasRole('PRESUPUESTO')")
  public @ResponseBody GenericResponseDTO listarNSL(@RequestBody NslUserRequestDTO request) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("USER_READ");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/usuarios/listarnsl", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return usuarioService.listarNsl(request);
  }

  @Operation(summary = "Listado directo de usuarios RRHH", description = "No recibe  parámetros",
      tags = {"Usuarios"}, hidden = false, deprecated = false)
  @ApiResponses({@ApiResponse(responseCode = "200", description = "Listado completo"),
      @ApiResponse(responseCode = "400", description = "Bad Request")})
  @GetMapping("/listar-rapido")
  @Transactional
  // @PreAuthorize("hasAuthority('USERS_READ') and
  // hasRole('PRESUPUESTO')")
  public @ResponseBody GenericResponseDTO listarRapido() {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("USER_READ", "USER_WRITE");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR", "PRESUPUESTO");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/usuarios/listar-rapido", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso

    return usuarioService.listarRapido();
  }

  @PutMapping("/modificar-por-rolId/{rolId}")
  @Transactional
  // @PreAuthorize("hasAuthority('USER_WRITE') and
  // hasRole('ADMINISTRADOR')")
  public @ResponseBody GenericResponseDTO modificarUsuariosPorRol(
      @RequestBody SyncUsuariosRequestDTO request, @PathVariable Long rolId) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("USER_READ", "USER_WRITE");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/usuarios/modificar-por-rolId", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return usuarioService.actualizarUsuariosByRol(request.getUsuarios(), rolId);
  }
}
