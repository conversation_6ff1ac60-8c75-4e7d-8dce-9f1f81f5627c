package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_REPORTES")
@SequenceGenerator(name = "PRE_REPORTES_SEQ", sequenceName = "PRE_REPORTES_SEQ", allocationSize = 1)
public class PreReportes implements Serializable {

  private static final long serialVersionUID = 7559872471984824194L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PRE_REPORTES_SEQ")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;

  @Column(name = "TIPO_REPORTE")
  private String tipoReporte;

  @JoinColumn(name = "USUARIO_GENERADOR", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhUsuario usuarioGenerador;

  @Column(name = "CONTENIDO_JSON")
  private String contenidoJson;

  @Size(max = 4000)
  @Column(name = "OBSERVACIONES")
  private String observaciones;

  @JoinTable(name = "PRE_REPORTES_PUESTOS",
      joinColumns = {@JoinColumn(name = "REPORTE_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "PUESTO_ID", referencedColumnName = "ID")})
  @ManyToMany(fetch = FetchType.LAZY)
  private Set<PrePuestos> puestos;

  @JoinTable(name = "PRE_REPORTES_PUE_REESTRUCTURA",
      joinColumns = {@JoinColumn(name = "REPORTE_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "PUESTO_ID", referencedColumnName = "ID")})
  @ManyToMany(fetch = FetchType.LAZY)
  private Set<PrePuestos> puestosReestructura;

}
