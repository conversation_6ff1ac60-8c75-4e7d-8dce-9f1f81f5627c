package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_FIRMAS_OCUPACIONES")
@NamedQueries(value = {@NamedQuery(name = "LiqFirmasOcupaciones.findAll",
    query = "SELECT l FROM LiqFirmasOcupaciones l")})
public class LiqFirmasOcupaciones implements Serializable {
  private static final long serialVersionUID = 1L;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private BigDecimal id;
  @Column(name = "OCU_ID")
  private BigInteger ocuId;
  @Column(name = "MES")
  private BigInteger mes;
  @Column(name = "ANIO")
  private BigInteger anio;
  @Column(name = "VIGENTE")
  private Character vigente;
  @Column(name = "MODALIDAD")
  private BigInteger modalidad;
  @JoinColumn(name = "FIR_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqFirmas firId;
  @JoinColumn(name = "PPE_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqProcPasosEsc ppeId;
  @OneToMany(mappedBy = "focId", fetch = FetchType.LAZY)
  private Set<LiqDescuentosDetalle> liqDescuentosDetalleSet;
  @OneToMany(mappedBy = "focId", fetch = FetchType.LAZY)
  private Set<LiqCuotasAlimDetalle> liqCuotasAlimDetalleSet;
  @OneToMany(mappedBy = "focId", fetch = FetchType.LAZY)
  private Set<LiqEmbargosDetalle> liqEmbargosDetalleSet;

  public LiqFirmasOcupaciones(BigDecimal id) {
this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqFirmasOcupaciones[ id=" + id + " ]";
  }

}
