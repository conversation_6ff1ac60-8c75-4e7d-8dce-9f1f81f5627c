package ar.gob.lapampa.nsl.persistencia.controllers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.request.LiqCodigoRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.request.LiqCodigoTipoRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.request.ListadoConceptoConvenioRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.services.LiqCodigoService;
import lombok.NoArgsConstructor;

/**
 * LiqCodigo Controller
 *
 * <AUTHOR> NSL
 *
 */
@RestController
@NoArgsConstructor
@RequestMapping("/api/v1/app/codigos")
public class CodigoController {

  @Autowired
  private LiqCodigoService liqCodigoService;

  @PostMapping("/listar")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','COD_R','','IS')")
  public @ResponseBody GenericResponseDTO listar(@RequestBody LiqCodigoRequestDTO request) {
    return liqCodigoService.listar(request);
  }

  @PostMapping("/listarTiposCodigo")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','TPCOD_R','','IS')")
  public @ResponseBody GenericResponseDTO listarTiposdeCodigo(
      @RequestBody LiqCodigoTipoRequestDTO request) {
    return liqCodigoService.listarTiposdeCodigo(request);
  }

  @PostMapping("/listadoConceptosConvenio")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','LCONC_R','','IS')")
  public @ResponseBody GenericResponseDTO listarConceptosConvenio(
      @RequestBody ListadoConceptoConvenioRequestDTO request) {
    return liqCodigoService.listarConceptosConvenio(request);
  }


}
