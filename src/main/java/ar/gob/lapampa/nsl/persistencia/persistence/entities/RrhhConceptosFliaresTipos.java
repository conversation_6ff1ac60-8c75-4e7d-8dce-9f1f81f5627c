package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.List;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_CONCEPTOS_FLIARES_TIPOS")
public class RrhhConceptosFliaresTipos implements Serializable {

  private static final long serialVersionUID = 655539948460273795L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Basic(optional = false)
  @Size(min = 1, max = 100)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Column(name = "NECESITA_DESTINO")
  @Convert(converter = YesNoConverter.class)
  private Boolean necesitaDestino;

  @Column(name = "PERMITE_AGENTE_DESTINO")
  @Convert(converter = YesNoConverter.class)
  private Boolean permiteDestinoAgente;

  @Column(name = "TIPO_UNICO_ORIGEN")
  @Convert(converter = YesNoConverter.class)
  private Boolean tipoUnicoOrigen;

  @Column(name = "TIPO_UNICO_DESTINO")
  @Convert(converter = YesNoConverter.class)
  private Boolean tipoUnicoDestino;

  @Column(name = "ESTADO_INICIO")
  private String estadoInicio;

  @Column(name = "EDAD_MIN")
  private Long edadMin;

  @Column(name = "EDAD_MAX")
  private Long edadMax;

  @Column(name = "DIF_EDAD")
  private Long difEdad;

  @Basic(optional = false)
  @Nonnull
  @JoinColumn(name = "TVN_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduVinculosTipos tvnId;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "cftId", fetch = FetchType.LAZY)
  private List<RrhhConceptosFliares> rrhhConceptosFliares;

  public RrhhConceptosFliaresTipos(Long id) {
    this.id = id;
  }

  public RrhhConceptosFliaresTipos(Long id, String descripcion) {
    this.id = id;
    this.descripcion = descripcion;
  }

}
