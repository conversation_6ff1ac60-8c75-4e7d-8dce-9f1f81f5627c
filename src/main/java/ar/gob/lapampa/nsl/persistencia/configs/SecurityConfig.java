package ar.gob.lapampa.nsl.persistencia.configs;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import ar.gob.lapampa.nsl.persistencia.security.AuthTokenFilter;
import ar.gob.lapampa.nsl.persistencia.security.UnAuthHandler;

@Configuration
@EnableMethodSecurity
public class SecurityConfig {

  @Bean
  SecurityFilterChain filterChain(HttpSecurity http, AuthTokenFilter authenticationJwtTokenFilter,
      UnAuthHandler unauthorizedHandler) throws Exception {
    http.csrf(csrf -> csrf.disable())
        .exceptionHandling(exception -> exception.authenticationEntryPoint(unauthorizedHandler))
        .sessionManagement(
            session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        .authorizeHttpRequests(auth -> auth.requestMatchers(
            // Public end points (White list)
            AntPathRequestMatcher.antMatcher("/estado"),
            AntPathRequestMatcher.antMatcher("/logsTest"),
            AntPathRequestMatcher.antMatcher("/v3/api-docs/**"),
            AntPathRequestMatcher.antMatcher("/swagger-ui/**"),
            AntPathRequestMatcher.antMatcher("/actuator/**"),
            AntPathRequestMatcher.antMatcher("/logout"), AntPathRequestMatcher.antMatcher("/error"))
            .permitAll()
            // Secured end points (Authenticated list)
            .requestMatchers(AntPathRequestMatcher.antMatcher("/api/test/**"),
                AntPathRequestMatcher.antMatcher("/api/v1/**"))
            // .permitAll().anyRequest().authenticated())
            .authenticated().anyRequest().fullyAuthenticated());

    http.addFilterBefore(authenticationJwtTokenFilter, UsernamePasswordAuthenticationFilter.class);
    return http.build();
  }
}
