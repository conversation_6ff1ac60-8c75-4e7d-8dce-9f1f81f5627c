package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_RECIBOS_DETALLE")
@SequenceGenerator(name = "LIQ_COD_SEQ", sequenceName = "LIQ_COD_SEQ", allocationSize = 1)
public class LiqRecibosDetalle implements Serializable {

  private static final long serialVersionUID = 5823202184917866945L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Column(name = "ORDEN")
  private Long orden;

  @Column(name = "COD_NRO")
  private String codNro;

  @Column(name = "CANTIDAD")
  private Double cantidad;

  @Column(name = "CONCEPTO")
  private String concepto;

  @Column(name = "HABERES")
  private Double haberes;

  @Column(name = "DESCUENTOS")
  private Double descuentos;

  @Column(name = "MES_RETROACTIVO")
  private Integer mesRetroactivo;

  @Column(name = "ANIO_RETROACTIVO")
  private Integer anioRetroactivo;

  @JoinColumn(name = "COD_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqCodigos codId;

  @JoinColumn(name = "REC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqRecibos recId;

  public LiqRecibosDetalle(Long id) {
    this.id = id;
  }

}
