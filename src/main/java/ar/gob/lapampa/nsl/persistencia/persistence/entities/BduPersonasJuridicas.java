package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PERSONAS_JURIDICAS")
@NamedQueries(value = {@NamedQuery(name = "BduPersonasJuridicas.findAll",
    query = "SELECT b FROM BduPersonasJuridicas b")})
public class BduPersonasJuridicas implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PJS_ID")
  private long pjsId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "RAZON_SOCIAL", length = 200)
  private String razonSocial;
  @Column(name = "NOMBRE_FANTASIA", length = 200)
  private String nombreFantasia;
  @Column(name = "FECHA_INICIO_ACTIVIDAD")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaInicioActividad;
  @Column(name = "FECHA_FIN_ACTIVIDAD")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaFinActividad;
  @Column(name = "NRO_SUCURSAL")
  private Long nroSucursal;
  @Column(name = "MATRICULA_MANUAL")
  private BigInteger matriculaManual;
  @Column(name = "FECHA_DISOLUCION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDisolucion;
  @Column(name = "TIENE_CUIT")
  private Character tieneCuit;
  @JoinColumn(name = "ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @OneToOne(optional = false, fetch = FetchType.LAZY)
  private BduPersonas bduPersonas;

  public BduPersonasJuridicas(Long id) {
    this.id = id;
  }

  public BduPersonasJuridicas(Long id, long pjsId, String razonSocial) {
    this.id = id;
    this.pjsId = pjsId;
    this.razonSocial = razonSocial;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduPersonasJuridicas[ id=" + id + " ]";
  }

}
