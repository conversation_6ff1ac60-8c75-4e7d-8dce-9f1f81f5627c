package ar.gob.lapampa.nsl.persistencia.dtos;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RrhhUsuarioRequestDTO extends GenericRequestDTO<RrhhUsuarioDTO>
    implements Serializable {

  private static final long serialVersionUID = 4027844003714935532L;
  private String login;
  private String password;
  private Boolean demo = false;
  private Boolean isAdmin = false;
}
