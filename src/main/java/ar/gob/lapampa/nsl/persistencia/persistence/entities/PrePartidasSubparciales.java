package ar.gob.lapampa.nsl.persistencia.persistence.entities;


import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * The persistent class for the PRE_PARTIDAS_SUBPARCIALES database table.
 * 
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_PARTIDAS_SUBPARCIALES")
public class PrePartidasSubparciales implements Serializable {

  private static final long serialVersionUID = -5080338535913319831L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  private Long codigo;

  private String descripcion;

  @JoinColumn(name = "PPARC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PrePartidasParciales pparcId;

}
