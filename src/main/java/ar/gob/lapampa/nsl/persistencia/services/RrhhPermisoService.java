package ar.gob.lapampa.nsl.persistencia.services;

import java.util.List;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.AccionRequestDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.PermisoRequestDTO;
import ar.gob.lapampa.nsl.persistencia.models.AccionModel;

@Service
public interface RrhhPermisoService {

  GenericResponseDTO listarAccionesDisponibles(AccionRequestDTO request);

  GenericResponseDTO listarAccionesPorRol(Long rolId);

  GenericResponseDTO crearAccion(PermisoRequestDTO request);

  GenericResponseDTO borrarAccion(Long accionId);

  GenericResponseDTO modificarPermiso(PermisoRequestDTO request, Long permisoId);

  GenericResponseDTO listarAccionesLegacy();

  GenericResponseDTO actualizarPermisosByRol(List<AccionModel> request, Long rolId);

  GenericResponseDTO permisoPorId(Long permisoId);
}
