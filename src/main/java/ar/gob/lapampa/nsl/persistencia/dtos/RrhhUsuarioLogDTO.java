package ar.gob.lapampa.nsl.persistencia.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import com.querydsl.core.annotations.QueryProjection;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RrhhUsuarioLogDTO extends BaseRRHHDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 6825200916191992407L;
  private String login;
  private String role;
  private String tipo;
  private String estado;
  private String resultado;
  private Date creado;

  public RrhhUsuarioLogDTO(String login) {
    super();
    this.login = login;
  }

  @QueryProjection
  public RrhhUsuarioLogDTO(Long id) {
    super();
    this.id = id;
  }

  @Override
  public String getBeanName() {
    return RrhhUsuariosDTO.class.getName();
  }

}

