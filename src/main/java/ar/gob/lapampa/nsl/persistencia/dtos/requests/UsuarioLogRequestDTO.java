package ar.gob.lapampa.nsl.persistencia.dtos.requests;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UsuarioLogRequestDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 4023761103714935532L;
  private String login;
  private String role;
  private String tipo;
  private Date desde;
  private Date hasta;
  private String estado;
  private String resultado;
  // Paginado
  @NotNull
  private Integer size;
  @NotNull
  private Integer page;
  @NotNull
  private String sortBy;
  private Boolean asc;
}
