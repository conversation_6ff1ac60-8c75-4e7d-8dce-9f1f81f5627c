package ar.gob.lapampa.nsl.persistencia.controllers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.request.PreEscalafonRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.RegistrarEscalafonesAUsuarioRequestDTO;
import ar.gob.lapampa.nsl.persistencia.services.PreEscalafonService;
import ar.gob.lapampa.nsl.persistencia.services.RrhhEscalafonService;
import lombok.NoArgsConstructor;

/**
 * PreEscalafon Controller
 *
 * <AUTHOR> NSL
 *
 */
@RestController
@NoArgsConstructor
@RequestMapping("/api/v1/app/escalafones")
public class EscalafonController {

  @Autowired
  private PreEscalafonService preEscalafonService;

  @Autowired
  private RrhhEscalafonService escalafonService;

  @PostMapping("/listarCodigosPorConvenio")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','ESC_R','','IS')")
  public @ResponseBody GenericResponseDTO listarCodigosPorConvenio(
      @RequestBody PreEscalafonRequestDTO request) {
    return preEscalafonService.listarCodigosporConvenio(request);
  }

  @PostMapping("/registrarConveniosAUsuario")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','ESC_R, ESC_W','','IS')")
  public @ResponseBody ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO registrarEscalafonesAUsuario(
      @RequestBody RegistrarEscalafonesAUsuarioRequestDTO request) {

    return escalafonService.registrarEscalafonesAUsuario(request);
  }
}
