package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_SISTEMAS")
@NamedQueries(
    value = {@NamedQuery(name = "BduSistemas.findAll", query = "SELECT b FROM BduSistemas b")})
public class BduSistemas implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "EXTERNO")
  private Character externo;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "sisId", fetch = FetchType.LAZY)
  private Set<BduEsquemas> bduEsquemasSet;
  @OneToMany(mappedBy = "sisId", fetch = FetchType.LAZY)
  private Set<BduOrigenesDatos> bduOrigenesDatosSet;
  @OneToMany(mappedBy = "sisId", fetch = FetchType.LAZY)
  private Set<BduSoaEventosTipos> bduSoaEventosTiposSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduSistemas", fetch = FetchType.LAZY)
  private Set<BduActoresPresup> bduActoresPresupSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "sisId", fetch = FetchType.LAZY)
  private Set<BduPerfiles> bduPerfilesSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "sisId", fetch = FetchType.LAZY)
  private Set<BduFuncionesSis> bduFuncionesSisSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "sisId", fetch = FetchType.LAZY)
  private Set<BduUsuariosws> bduUsuarioswsSet;

  public BduSistemas(Long id) {
    this.id = id;
  }

  public BduSistemas(Long id, Character externo, Character activo) {
    this.id = id;
    this.externo = externo;
    this.activo = activo;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduSistemas[ id=" + id + " ]";
  }

}
