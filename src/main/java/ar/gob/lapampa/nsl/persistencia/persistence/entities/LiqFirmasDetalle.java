package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_FIRMAS_DETALLE")
@NamedQueries(value = {
    @NamedQuery(name = "LiqFirmasDetalle.findAll", query = "SELECT l FROM LiqFirmasDetalle l")})
public class LiqFirmasDetalle implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected LiqFirmasDetallePK liqFirmasDetallePK;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MONTO")
  private BigDecimal monto;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 100)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "CDAD")
  private BigDecimal cdad;
  @Size(max = 6)
  @Column(name = "NRO")
  private String nro;
  @JoinColumn(name = "COD_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqCodigos codId;
  @JoinColumn(name = "COV_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqCodigosVersiones covId;
  @JoinColumn(name = "FIR_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqFirmas liqFirmas;

  public LiqFirmasDetalle(LiqFirmasDetallePK liqFirmasDetallePK) {
    this.liqFirmasDetallePK = liqFirmasDetallePK;
  }

  public LiqFirmasDetalle(LiqFirmasDetallePK liqFirmasDetallePK, BigDecimal monto,
      String descripcion) {
    this.liqFirmasDetallePK = liqFirmasDetallePK;
    this.monto = monto;
    this.descripcion = descripcion;
  }

  public LiqFirmasDetalle(long firId, long item) {
    this.liqFirmasDetallePK = new LiqFirmasDetallePK(firId, item);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqFirmasDetalle[ liqFirmasDetallePK="
        + liqFirmasDetallePK + " ]";
  }

}
