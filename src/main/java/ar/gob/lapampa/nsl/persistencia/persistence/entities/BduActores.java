package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ACTORES")
@NamedQueries(
    value = {@NamedQuery(name = "BduActores.findAll", query = "SELECT b FROM BduActores b")})
public class BduActores implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRS_ID")
  private long prsId;
  @Column(name = "NIVEL")
  private Short nivel;
  @Column(name = "ORF_ID")
  private Long orfId;
  @JoinColumn(name = "ALP_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduActLdap alpId;
  @JoinColumn(name = "ENT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduEntidades entId;
  @JoinColumn(name = "TEV_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduEventosTipos tevId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "usuario", fetch = FetchType.LAZY)
  private Set<BduActEntidPerfEve> bduActEntidPerfEveSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduActores", fetch = FetchType.LAZY)
  private Set<BduActoresPresup> bduActoresPresupSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "actId", fetch = FetchType.LAZY)
  private Set<BduActEntidPerf> bduActEntidPerfSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "usuario", fetch = FetchType.LAZY)
  private Set<BduPerfilesEventos> bduPerfilesEventosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "actId", fetch = FetchType.LAZY)
  private Set<BduActoresEventos> bduActoresEventosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "usuario", fetch = FetchType.LAZY)
  private Set<BduActoresEventos> bduActoresEventosSet1;

  public BduActores(Long id) {
    this.id = id;
  }

  public BduActores(Long id, long prsId) {
    this.id = id;
    this.prsId = prsId;
  }

}
