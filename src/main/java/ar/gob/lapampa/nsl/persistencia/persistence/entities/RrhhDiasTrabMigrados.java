package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_DIAS_TRAB_MIGRADOS")
@NamedQueries(value = {@NamedQuery(name = "RrhhDiasTrabMigrados.findAll",
    query = "SELECT r FROM RrhhDiasTrabMigrados r")})
public class RrhhDiasTrabMigrados implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Column(name = "OCU_ID")
  private Long ocuId;
  @Column(name = "DIAS_TRAB")
  private Long diasTrab;
  @Column(name = "MES")
  private Short mes;
  @Column(name = "ANIO")
  private Short anio;

  public RrhhDiasTrabMigrados(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhDiasTrabMigrados[ id=" + id + " ]";
  }

}
