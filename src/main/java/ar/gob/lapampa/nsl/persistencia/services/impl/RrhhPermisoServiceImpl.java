package ar.gob.lapampa.nsl.persistencia.services.impl;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import com.querydsl.core.BooleanBuilder;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.AccionRequestDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.PermisoRequestDTO;
import ar.gob.lapampa.nsl.persistencia.models.AccionModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QRrhhAcciones;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhAcciones;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhRol;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.RrhhAccionMapper;
import ar.gob.lapampa.nsl.persistencia.repositories.NslUsuariosRepository;
import ar.gob.lapampa.nsl.persistencia.repositories.RrhhAccionRepository;
import ar.gob.lapampa.nsl.persistencia.repositories.RrhhRolRepository;
import ar.gob.lapampa.nsl.persistencia.services.RrhhPermisoService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class RrhhPermisoServiceImpl implements RrhhPermisoService {

  @PersistenceContext
  private EntityManager entityManager;

  @Autowired
  private NslUsuariosRepository nslUsuariosRepository;

  @Autowired
  private RrhhRolRepository rrhhRolRepository;

  @Autowired
  private RrhhAccionRepository rrhhAccionRepository;

  Sort sort = Sort.unsorted();

  @Override
  public GenericResponseDTO listarAccionesDisponibles(AccionRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();

    BooleanBuilder booleanBuilder = new BooleanBuilder();

    // Filtrar solo acciones que comienzan con "NNSL_"
    booleanBuilder.and(QRrhhAcciones.rrhhAcciones.nombre.startsWith("NNSL_"));

    // Add filter for nombre if provided
    if (request.getNombre() != null && !request.getNombre().isEmpty()) {
      booleanBuilder.and(QRrhhAcciones.rrhhAcciones.nombre.containsIgnoreCase(request.getNombre()));
    }

    // Add filter for endpoint if provided
    if (request.getEndpoint() != null && !request.getEndpoint().isEmpty()) {
      booleanBuilder
          .and(QRrhhAcciones.rrhhAcciones.endpoint.containsIgnoreCase(request.getEndpoint()));
    }

    // Add filter for nota if provided
    if (request.getNota() != null && !request.getNota().isEmpty()) {
      booleanBuilder.and(QRrhhAcciones.rrhhAcciones.nota.containsIgnoreCase(request.getNota()));
    }


    // Set sorting
    if (request.getSortBy() != null && !request.getSortBy().isEmpty()) {
      if (Boolean.TRUE.equals(request.getAsc())) {
        sort = Sort.by(Sort.Direction.ASC, request.getSortBy());
      } else {
        sort = Sort.by(Sort.Direction.DESC, request.getSortBy());
      }
    } else {
      sort = Sort.unsorted();
    }

    Page<RrhhAcciones> page;
    if (booleanBuilder.getValue() != null) {
      page = rrhhAccionRepository.findAll(booleanBuilder.getValue(),
          PageRequest.of(request.getPage(), request.getSize(), sort));
    } else {
      page =
          rrhhAccionRepository.findAll(PageRequest.of(request.getPage(), request.getSize(), sort));
    }

    Page<AccionModel> pageDto = page.map(accion -> {
      AccionModel accionModel = RrhhAccionMapper.rrhhAccionToRrhhAccionModel(accion);
      if (accionModel.getNombre() != null && accionModel.getNombre().startsWith("NNSL_")) {
        // Eliminar el prefijo "NNSL_" y capitalizar
        String nombreSinPrefijo = accionModel.getNombre().substring(5);
        accionModel.setNombre(nombreSinPrefijo);
      }
      return accionModel;
    });

    // Establecer la respuesta exitosa con la pagina de permisos
    response.setEstadoExito(pageDto);
    return response;
  }

  @Override
  public GenericResponseDTO listarAccionesPorRol(Long rolId) {
    final GenericResponseDTO response = new GenericResponseDTO();

    // Buscar el rol por su ID
    Optional<RrhhRol> rolOptional = rrhhRolRepository.findById(rolId);

    if (rolOptional.isPresent()) {
      RrhhRol rol = rolOptional.get();

      // Obtener las acciones asociadas al rol
      List<RrhhAcciones> acciones = rol.getAcciones();

      // Filtrar solo las acciones que comienzan con "NNSL_", eliminar el prefijo y mapearlas a
      // modelos
      // Eliminar el prefijo "NNSL_" y capitalizar
      List<AccionModel> accionModels = new ArrayList<>();
      for (RrhhAcciones accion : acciones) {
        if (accion.getNombre() != null && accion.getNombre().startsWith("NNSL_")) {
          AccionModel accionModel = RrhhAccionMapper.rrhhAccionToRrhhAccionModel(accion);
          if (accionModel.getNombre() != null) {
            // Eliminar el prefijo "NNSL_" y capitalizar
            String nombreSinPrefijo = accionModel.getNombre().substring(5);
            accionModel.setNombre(nombreSinPrefijo);
          }
          accionModels.add(accionModel);
        }
      }
      accionModels.sort(Comparator.comparing(AccionModel::getNombre));

      // Establecer la respuesta exitosa con la lista de acciones
      response.setEstadoExito(accionModels);
    } else {
      // Si no se encuentra el rol, devolver un mensaje de error
      response.setEstadoError("Rol no encontrado");
    }

    return response;
  }

  @Override
  public GenericResponseDTO crearAccion(PermisoRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();
    try {
      // Normalizar el nombre para almacenamiento interno (prefijo + mayúsculas + guiones bajos)
      // Ejemplo: "Gestionar Usuarios" -> "NNSL_GESTIONAR_USUARIOS"
      String nombreBase =
          request.getNombre().toUpperCase().replace(" ", "_").replaceAll("[^A-Z0-9_]", ""); // Eliminar
      // caracteres
      // no
      // válidos
      String nombreInternoAccion = "NNSL_" + nombreBase;

      Optional<RrhhAcciones> existente =
          rrhhAccionRepository.findOne(QRrhhAcciones.rrhhAcciones.nombre.eq(nombreInternoAccion));
      if (existente.isPresent()) {
        response.setEstadoError("Ya existe una acción con el nombre interno: " + nombreInternoAccion
            + " (derivado de '" + request.getNombre() + "')");
        log.warn("Intento de crear acción duplicada: {}", nombreInternoAccion);
        return response;
      }

      RrhhAcciones nuevaAccion = new RrhhAcciones();
      nuevaAccion.setNombre(nombreInternoAccion);
      nuevaAccion.setEndpoint(request.getEndpoint()); // Puede ser null o vacío
      nuevaAccion.setNota(request.getNota()); // Puede ser null o vacío

      RrhhAcciones accionGuardada = rrhhAccionRepository.save(nuevaAccion);

      // Mapear a AccionModel para la respuesta, similar a como se hace en listarAccionesDisponibles
      AccionModel accionModel = RrhhAccionMapper.rrhhAccionToRrhhAccionModel(accionGuardada);
      if (accionModel.getNombre() != null && accionModel.getNombre().startsWith("NNSL_")) {
        String nombreSinPrefijo = accionModel.getNombre().substring(5);
        // Capitalizar para mostrar un nombre amigable: "GESTIONAR_USUARIOS" -> "Gestionar Usuarios"
        accionModel.setNombre(nombreSinPrefijo.replace("_", " ").toUpperCase());
      }

      response.setEstadoExito(accionModel);
      response.setMensaje("Acción '" + request.getNombre() + "' creada exitosamente.");
      log.info("Acción creada: ID={}, NombreInterno={}", accionGuardada.getId(),
          accionGuardada.getNombre());

    } catch (Exception e) {
      log.error("Error al crear la acción para la solicitud: {}", request.getNombre(), e);
      response.setEstadoError("Error interno al crear la acción: " + e.getMessage());
    }
    return response;
  }

  @Override
  public GenericResponseDTO borrarAccion(Long accionId) {
    final GenericResponseDTO response = new GenericResponseDTO();
    Optional<RrhhAcciones> rolOptional = rrhhAccionRepository.findById(accionId);

    if (rolOptional.isEmpty()) {
      response.setEstadoError("Permiso con ID " + accionId + " no encontrado.");
      log.warn("Intento de borrar permiso no existente: ID={}", accionId);
      return response;
    }

    RrhhAcciones accion = rolOptional.get();
    final List<String> dependenciasActivas = verificarDependenciasAccion(accion);

    if (!dependenciasActivas.isEmpty()) {
      String mensajeError = "El permiso '" + accion.getNombre() + "' (ID: " + accionId
          + ") no puede ser eliminado porque tiene: " + String.join(", ", dependenciasActivas)
          + ". Por favor, desasócielo primero.";
      response.setEstadoError(mensajeError);
      log.warn("Intento de borrar permiso con dependencias: ID={}, Nombre='{}', Dependencias: {}",
          accionId, accion.getNombre(), dependenciasActivas);
      return response;
    }

    // Si no hay dependencias, proceder con el borrado
    try {
      rrhhAccionRepository.deleteById(accionId);
      response.setEstadoExito(accionId);
      response.setMensaje("Permiso eliminado exitosamente.");
      log.info("Permiso eliminado: ID={}", accionId);
    } catch (Exception e) {
      log.error("Error al eliminar el permiso con ID: {}", accionId, e);
      response.setEstadoError("Error interno al eliminar el permiso: " + e.getMessage());
    }
    return response;
  }

  @Override
  public GenericResponseDTO modificarPermiso(PermisoRequestDTO request, Long permisoId) {
    final GenericResponseDTO response = new GenericResponseDTO();
    try {
      // Verificar si el rol existe
      Optional<RrhhAcciones> existente = rrhhAccionRepository.findById(permisoId);
      if (existente.isPresent()) {

        // Normalizar el nombre para almacenamiento interno (prefijo + mayúsculas + guiones bajos)
        // Ejemplo: "Gestionar Usuarios" -> "NNSL_GESTIONAR_USUARIOS"
        String nombreBase =
            request.getNombre().toUpperCase().replace(" ", "_").replaceAll("[^A-Z0-9_]", ""); // Eliminar
        // caracteres
        // no
        // válidos
        String nombreInternoAccion = "NNSL_" + nombreBase;
        RrhhAcciones accion = existente.get();
        accion.setNombre(nombreInternoAccion);
        accion.setEndpoint(request.getEndpoint()); // Puede ser null o vacío
        accion.setNota(request.getNota()); // Puede ser null o vacío
        RrhhAcciones accionGuardada = rrhhAccionRepository.save(accion);

        // Mapear a AccionModel para la respuesta, similar a como se hace en listarRolesDisponibles
        AccionModel accionModel = RrhhAccionMapper.rrhhAccionToRrhhAccionModel(accionGuardada);

        response.setEstadoExito(accionModel);
        response
            .setMensaje("Permiso '" + accionGuardada.getNombre() + "' actualizado exitosamente.");
        log.info("Permiso actualizado: ID={}, NombreInterno={}", accionGuardada.getId(),
            accionGuardada.getNombre());

      } else {
        response.setEstadoError("Permiso con ID " + permisoId + " no encontrado.");
        log.warn("Intento de modificar permiso no existente: ID={}", permisoId);
        return response;
      }

    } catch (Exception e) {
      log.error("Error al actualizar el permiso para la solicitud: {}", request.getNombre(), e);
      response.setEstadoError("Error interno al actualizar el permiso: " + e.getMessage());
    }
    return response;
  }

  private static List<String> verificarDependenciasRol(RrhhRol rol) {
    List<String> dependenciasActivas = new ArrayList<>();

    // Verificar dependencias
    // Gracias a FetchType.LAZY, estas llamadas .isEmpty()
    // resultan en queries eficientes (SELECT COUNT(*)) en lugar de cargar toda la colección.
    if (rol.getUsuarios() != null && !rol.getUsuarios().isEmpty()) {
      dependenciasActivas.add("usuarios asignados (" + rol.getUsuarios().size() + ")");
    }
    if (rol.getMenues() != null && !rol.getMenues().isEmpty()) {
      dependenciasActivas.add("menús asociados (" + rol.getMenues().size() + ")");
    }
    if (rol.getAcciones() != null && !rol.getAcciones().isEmpty()) {
      dependenciasActivas.add("acciones vinculadas (" + rol.getAcciones().size() + ")");
    }
    if (rol.getEmpresas() != null && !rol.getEmpresas().isEmpty()) {
      dependenciasActivas.add("empresas relacionadas (" + rol.getEmpresas().size() + ")");
    }
    return dependenciasActivas;
  }

  private static List<String> verificarDependenciasAccion(RrhhAcciones accion) {
    List<String> dependenciasActivas = new ArrayList<>();

    // Verificar dependencias
    if (accion.getRoles() != null && !accion.getRoles().isEmpty()) {
      dependenciasActivas.add("roles asignados (" + accion.getRoles().size() + ")");
    }

    return dependenciasActivas;
  }

  public String verificarEnNsl(String username) {
    return nslUsuariosRepository.existsByUsername(username) ? "Si" : "No";
  }

  @Override
  public GenericResponseDTO listarAccionesLegacy() {
    final GenericResponseDTO response = new GenericResponseDTO();
    List<RrhhAcciones> acciones = rrhhAccionRepository.findAll();
    List<AccionModel> accionModels = new ArrayList<>();
    for (RrhhAcciones accion : acciones) {
      AccionModel accionModel = RrhhAccionMapper.rrhhAccionToRrhhAccionModel(accion);
      accionModels.add(accionModel);
    }
    accionModels.sort(Comparator.comparing(AccionModel::getNombre));
    // Establecer la respuesta exitosa con la lista de acciones
    response.setEstadoExito(accionModels);
    return response;
  }

  @Override
  public GenericResponseDTO actualizarPermisosByRol(List<AccionModel> request, Long rolId) {
    final GenericResponseDTO response = new GenericResponseDTO();

    try {
      Optional<RrhhRol> rolOptional = rrhhRolRepository.findById(rolId);
      if (rolOptional.isPresent()) {
        // aseguramos valores unicos
        Set<Long> idsNuevosPermisos =
            request.stream().map(AccionModel::getId).collect(Collectors.toSet());
        // Convertir el set de longs a una lista de strings separados por comas
        String accionListStr =
            idsNuevosPermisos.stream().map(String::valueOf).collect(Collectors.joining(","));

        // Sincronizamos con la DB
        String result = rrhhAccionRepository.syncAccionesRoles(rolId, accionListStr);

        // Verificar si el resultado es nulo antes de usarlo
        if (result == null) {
          response.setEstadoError("La operación en base de datos no devolvió un resultado.");
          log.warn("El stored procedure para rol ID {} devolvió null.", rolId);
          return response;
        } else if (result.toUpperCase().contains("ERROR")) {
          response.setEstadoError(result);
          log.warn("Error devuelto por el stored procedure para rol ID {}: {}", rolId, result);
          return response;
        } else {
          response.setEstadoExito("Ok");
          response.setMensaje(result);
          log.info("Permisos actualizados mediante stored procedure. Resultado: {}", result);
          return response;
        }
      } else {
        response.setEstadoError("Rol con ID " + rolId + " no encontrado.");
        log.warn("Intento de actualizar permisos de rol no existente: ID={}", rolId);
      }
    } catch (Exception e) {
      log.error("Error al actualizar los permisos para el rol: {}", rolId, e);
      response.setEstadoError("Error interno al actualizar los permisos: " + e.getMessage());
    }
    return response;
  }

  @Override
  public GenericResponseDTO permisoPorId(Long permisoId) {
    final GenericResponseDTO response = new GenericResponseDTO();
    Optional<RrhhAcciones> permisoOptional = rrhhAccionRepository.findById(permisoId);
    if (permisoOptional.isPresent()) {
      AccionModel accionModel = RrhhAccionMapper.rrhhAccionToRrhhAccionModel(permisoOptional.get());
      response.setEstadoExito(accionModel);
    } else {
      response.setEstadoError("Permiso no encontrado");
    }
    return response;
  }

  // BORRAR CUANDO SE TERMINE DE TESTEAR
  private String llamarFuncionWrapperConEntityManager_TEST(Long rrhhRolId, String accionIds) {
    try {
      // Usar una consulta nativa para llamar a la función PL/SQL
      jakarta.persistence.Query query = entityManager.createNativeQuery(
          "SELECT LAPAMPA.GET_SYNC_ACCIONES_ROLES_RESULT_TEST(:rolId, :accionIds) FROM DUAL");
      query.setParameter("rolId", rrhhRolId);
      query.setParameter("accionIds", accionIds);
      Object result = query.getSingleResult();
      return (result != null) ? result.toString() : null;
    } catch (Exception e) {
      log.error(
          "Error al llamar a GET_SYNC_ACCIONES_ROLES_RESULT_TEST con NativeQuery para rolId {}: {}",
          rrhhRolId, e.getMessage(), e);
      // Devuelve un string que indique el error para que el método llamador lo maneje
      return "ERROR_NATIVE_QUERY: " + e.getMessage();
    }
  }

}
