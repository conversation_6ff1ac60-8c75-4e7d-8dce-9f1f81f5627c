package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_DESCUENTOS_DETALLE")
@NamedQueries(value = {@NamedQuery(name = "LiqDescuentosDetalle.findAll",
    query = "SELECT l FROM LiqDescuentosDetalle l")})
public class LiqDescuentosDetalle implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected LiqDescuentosDetallePK liqDescuentosDetallePK;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Column(name = "MONTO_DESCONTADO")
  private BigDecimal montoDescontado;
  @JoinColumn(name = "DES_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqDescuentos liqDescuentos;
  @JoinColumn(name = "FOC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqFirmasOcupaciones focId;

  public LiqDescuentosDetalle(LiqDescuentosDetallePK liqDescuentosDetallePK) {
    this.liqDescuentosDetallePK = liqDescuentosDetallePK;
  }

  public LiqDescuentosDetalle(long desId, long item) {
    this.liqDescuentosDetallePK = new LiqDescuentosDetallePK(desId, item);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqDescuentosDetalle[ liqDescuentosDetallePK="
        + liqDescuentosDetallePK + " ]";
  }

}
