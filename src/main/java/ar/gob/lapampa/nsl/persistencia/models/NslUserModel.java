package ar.gob.lapampa.nsl.persistencia.models;

import java.io.Serial;
import java.io.Serializable;
import org.apache.commons.lang.WordUtils;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class NslUserModel extends BaseRRHHDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 7825100916191992407L;
    private String username;
    private String email;
    private String password;
    private String apellidos;
    private String nombres;

    public NslUserModel(long id) {
        super();
        this.id = id;
    }

    public NslUserModel(String username) {
        super();
        this.username = username;
    }

    public NslUserModel(final String username, final String email) {
        super();
        this.username = username;
        this.email = email;
    }

    public void setApellidos(String apellidos) {
        this.apellidos = WordUtils.capitalizeFully(apellidos);
    }

    public void setNombres(String nombres) {
        this.nombres = WordUtils.capitalizeFully(nombres);
    }

    /**
     * Obtiene Nombre de class name solo
     *
     * @return String
     */
    @Override
    public String getBeanName() {
        return NslUserModel.class.getSimpleName();
    }
}
