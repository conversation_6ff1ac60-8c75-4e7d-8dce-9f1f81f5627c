package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_LUGARES_RECONOCIMIENTOS")
@NamedQueries(value = {@NamedQuery(name = "RrhhLugaresReconocimientos.findAll",
    query = "SELECT r FROM RrhhLugaresReconocimientos r")})
public class RrhhLugaresReconocimientos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @OneToMany(mappedBy = "lreId", fetch = FetchType.LAZY)
  private Set<RrhhLicencias> rrhhLicenciasSet;

  public RrhhLugaresReconocimientos(Long id) {
    this.id = id;
  }

  public RrhhLugaresReconocimientos(Long id, String descripcion) {
    this.id = id;
    this.descripcion = descripcion;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhLugaresReconocimientos[ id=" + id + " ]";
  }

}
