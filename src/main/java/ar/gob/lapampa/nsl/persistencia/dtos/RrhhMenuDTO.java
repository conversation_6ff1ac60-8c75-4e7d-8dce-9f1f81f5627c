package ar.gob.lapampa.nsl.persistencia.dtos;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import ar.gob.lapampa.nsl.datatransfer.RrhhRolesDTO;
import ar.gob.lapampa.nsl.datatransfer.commons.DTOMultiOrdenable;
import ar.gob.lapampa.nsl.datatransfer.commons.DTOSeleccionable;
import ar.gob.lapampa.nsl.utils.constantes.Constantes;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RrhhMenuDTO extends BaseRRHHDTO
    implements Serializable, DTOSeleccionable, DTOMultiOrdenable {

  @Serial
  private static final long serialVersionUID = -8532525348163975214L;
  private String nombre;
  private String estado;
  private RrhhMenuDTO menuPadreId;
  private String icono;
  private Boolean abstracto;
  private List<RrhhRolesDTO> roles;

  public RrhhMenuDTO(Long id) {
    super();
    this.id = id;
  }

  public RrhhMenuDTO(List<RrhhRolesDTO> roles) {
    super();
    this.roles = roles;
  }

  public RrhhMenuDTO(Long id, List<RrhhRolesDTO> roles) {
    super();
    this.id = id;
    this.roles = roles;
  }

  @Override
  public String getBeanName() {
    return RrhhMenuDTO.class.getName();
  }

  @Override
  public String getTipoDeOrden() {
    return Constantes.ORDEN_ASCENDENTE;
  }

  @Override
  public String getDescripcionCombo() {
    return getNombre();
  }

  @Override
  public List<String> getCamposParaOrdenacion() {
    // return new java.util.ArrayList(Arrays.asList(RrhhMenu_.nombre.getName()))
    return new java.util.ArrayList<>(List.of("nombre"));
  }
}
