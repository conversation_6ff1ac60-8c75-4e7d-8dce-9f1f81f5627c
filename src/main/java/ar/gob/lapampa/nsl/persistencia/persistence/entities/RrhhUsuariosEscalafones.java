package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_USUARIOS_ESCALAFONES")
@SequenceGenerator(name = "RRHH_USR_ESC_SEQ", sequenceName = "RRHH_USR_ESC_SEQ", allocationSize = 1)
public class RrhhUsuariosEscalafones implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_USR_ESC_SEQ")
  private Long id;

  @JoinColumn(name = "RRHH_USR_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhUsuario usuario;

  @JoinColumn(name = "PRE_ESC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private PreEscalafones convenio;

}
