package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_PUESTOS_HIST")
@SequenceGenerator(name = "PRE_PST_HIST_SEQ", sequenceName = "PRE_PST_HIST_SEQ", allocationSize = 1)
public class PrePuestosHist implements Serializable {

  private static final long serialVersionUID = -7252313569405807966L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PRE_PST_HIST_SEQ")
  private Long id;


  // @JoinColumn(name = "NROPUESTO", referencedColumnName = "NROPUESTO")
  // @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private Long nroPuesto;

  @Column(name = "NROHISTORIA")
  private Long nroHistoria;

  @Column(name = "NROLEGAJO")
  private String nroLegajo;

  @Column(name = "ANIODECRETO")
  private Long anioDecreto;

  @Column(name = "ANIORESOLUCION")
  private Long anioResolucion;

  @Column(name = "ANIOEXPEDIENTE")
  private Long anioExpediente;

  @Column(name = "NROLEY")
  private Long nroley;

  @Column(name = "NROEXPEDIENTE")
  private String nroExpediente;

  @Column(name = "NRODECRETO")
  private Long nroDecreto;

  @Column(name = "NRORESOLUCION")
  private String nroResolucion;

  @Column(name = "FECHAPROCESO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaProceso;

  @Column(name = "FECHA_MODIFICACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaModificacion;

  @Column(name = "FECHAVALOR")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaValor;

  @Column(name = "OBSERVACIONES")
  private String observaciones;

  @JoinColumn(name = "OCU_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.LAZY)
  private RrhhOcupaciones ocuId;

  @JoinColumn(name = "ESTADO_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private ParmPuestosEstados estId;

  @JoinColumn(name = "ESTADO2_ID", nullable = true, referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private ParmPuestosEstados est2Id;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduPersonasFisicas prsId;

  @Column(name = "NROREESTRUCTURA")
  private Long nroReestructura;

  @Column(name = "ANIOREESTRUCTURA")
  private Long anioReestructura;

  @Column(name = "FECHAINICIOSUBROGACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaInicioSubrogacion;

  @Column(name = "FECHAFINSUBROGACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaFinSubrogacion;

  @JoinColumn(name = "CAT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private PreCategorias catId;

  @JoinColumn(name = "COMBPRES_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private PreCombinacionPresupuestaria combpresId;

  @OneToMany(mappedBy = "puestoId", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
  private List<PrePuestosEstadosHist> estados;

  @Column(name = "FECHA_ANULACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAnulacion;

  @JoinColumn(name = "FUNCION_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private RrhhFunciones funcionId;

  @JoinColumn(name = "ESTAB_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private RrhhEstablecimientos estabId;

  @Column(name = "FUNCION_EST")
  private String funcionEst;

  public PrePuestosHist(Long id) {

    this.id = id;
  }

  public PrePuestosHist(ParmPuestosEstados estId) {

    this.estId = estId;
  }

}
