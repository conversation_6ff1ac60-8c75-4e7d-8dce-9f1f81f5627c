package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.List;
import java.util.Set;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PERSONAS")
@SequenceGenerator(name = "BDU_PRS_SEQ", sequenceName = "BDU_PRS_SEQ", allocationSize = 1)
public class BduPersonas implements Serializable {

  private static final long serialVersionUID = -2722327588806439423L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BDU_PRS_SEQ")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "TIPO")
  private Character tipo;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "OCURRENCIA")
  private Short ocurrencia;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "NRO_DOCUMENTO")
  private Long nroDocumento;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ODT_ID")
  private Long odtId;

  @Column(name = "PRS_ID_BASE")
  private Long prsIdBase;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @JoinColumn(name = "TDC_ID", referencedColumnName = "ID")
  @OneToOne(optional = false, fetch = FetchType.LAZY)
  private BduDocumentosTipos tdcId;

  @OneToOne(cascade = CascadeType.ALL, mappedBy = "bduPersonas", fetch = FetchType.EAGER)
  private BduPersonasFisicas bduPersonasFisicas;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "prsOrigenId", fetch = FetchType.LAZY)
  private List<BduVinculos> bduVinculosPrsOrigenList;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "prsDestinoId", fetch = FetchType.LAZY)
  private List<BduVinculos> bduVinculosPrsDestinoList;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "prsId", fetch = FetchType.LAZY)
  private Set<BduPersonasDomicilios> bduPersonasDomiciliosSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "prsId", fetch = FetchType.LAZY)
  private Set<BduPersonasDocumentos> bduPersonasDocumentosSet;

  public BduPersonas(Long id) {
    this.id = id;
  }

}
