package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class RrhhOcupacionesObrasPK implements Serializable {

  private static final long serialVersionUID = -2593594009197527584L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "OCU_ID")
  private long ocuId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "OBR_ID")
  private long obrId;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhOcupacionesObrasPK[ ocuId=" + ocuId
        + ", obrId=" + obrId + " ]";
  }

}
