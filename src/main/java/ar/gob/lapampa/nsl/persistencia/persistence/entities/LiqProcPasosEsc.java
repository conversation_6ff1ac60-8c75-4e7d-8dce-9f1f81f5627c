package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_PROC_PASOS_ESC")
@NamedQueries(value = {
    @NamedQuery(name = "LiqProcPasosEsc.findAll", query = "SELECT l FROM LiqProcPasosEsc l")})
public class LiqProcPasosEsc implements Serializable {
  private static final long serialVersionUID = 1L;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private BigDecimal id;
  @Column(name = "OCUP_A_LIQUIDAR")
  private BigInteger ocupALiquidar;
  @Column(name = "OCUP_LIQUIDADAS")
  private BigInteger ocupLiquidadas;
  @Column(name = "OCUP_CON_ERROR")
  private BigInteger ocupConError;
  @OneToMany(mappedBy = "ppeId", fetch = FetchType.LAZY)
  private Set<LiqFirmasOcupaciones> liqFirmasOcupacionesSet;
  @OneToMany(mappedBy = "ppeId", fetch = FetchType.LAZY)
  private Set<LiqTotalesMesPersona> liqTotalesMesPersonaSet;
  @OneToMany(mappedBy = "ppeId", fetch = FetchType.LAZY)
  private Set<LiqTotalesMesOcupaciones> liqTotalesMesOcupacionesSet;
  @JoinColumn(name = "PPA_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqProcesosPasos ppaId;
  @JoinColumn(name = "SVE_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqSetVersiones sveId;
  @JoinColumn(name = "ESC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private PreEscalafones escId;

  public LiqProcPasosEsc(BigDecimal id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqProcPasosEsc[ id=" + id + " ]";
  }

}
