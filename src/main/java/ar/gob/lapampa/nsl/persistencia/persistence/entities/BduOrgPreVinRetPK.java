package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class BduOrgPreVinRetPK implements Serializable {

  private static final long serialVersionUID = -7676035911344471374L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ORP_ID_ORIGEN")
  private long orpIdOrigen;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ORP_ID_DESTINO")
  private long orpIdDestino;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "RET_ID")
  private long retId;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduOrgPreVinRetPK[ orpIdOrigen=" + orpIdOrigen
        + ", orpIdDestino=" + orpIdDestino + ", retId=" + retId + " ]";
  }

}
