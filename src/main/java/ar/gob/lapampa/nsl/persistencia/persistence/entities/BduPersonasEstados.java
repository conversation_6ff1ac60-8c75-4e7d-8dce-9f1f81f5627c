package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PERSONAS_ESTADOS")
@NamedQueries(value = {
    @NamedQuery(name = "BduPersonasEstados.findAll", query = "SELECT b FROM BduPersonasEstados b")})
public class BduPersonasEstados implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRS_ID")
  private long prsId;
  @Column(name = "FCH_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fchDesde;
  @Size(max = 1000)
  @Column(name = "USUARIO_MOV")
  private String usuarioMov;
  @Column(name = "FECHA_MOV")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaMov;
  @JoinColumn(name = "EST_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduEstados estId;
  @JoinColumn(name = "ODT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduOrigenesDatos odtId;

  public BduPersonasEstados(Long id) {
    this.id = id;
  }

  public BduPersonasEstados(Long id, long prsId) {
    this.id = id;
    this.prsId = prsId;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduPersonasEstados[ id=" + id + " ]";
  }

}
