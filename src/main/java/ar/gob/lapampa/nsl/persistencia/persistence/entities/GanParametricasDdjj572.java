package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_PARAMETRICAS_DDJJ572")
@SequenceGenerator(name = "GAN_PARAMETRICAS_DDJJ572_SQ",
    sequenceName = "GAN_PARAMETRICAS_DDJJ572_SQ", allocationSize = 1)
public class GanParametricasDdjj572 implements Serializable {

  private static final long serialVersionUID = -3399680895482240861L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_PARAMETRICAS_DDJJ572_SQ")
  private Long id;

  @Column(name = "CODIGO")
  private Long codigo;

  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Column(name = "PARAMETRICA")
  private String parametrica;

}
