package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Table(name = "NSL_USUARIOS")

public class NslUsuarios implements Serializable {

    @Serial
    private static final long serialVersionUID = -7771813076175707278L;

    @Id
    @Basic(optional = false)
    @Column(name = "ID")
    private Long id;

    @Basic(optional = false)
    @Column(name = "USERNAME")
    private String username;

    @Basic(optional = true)
    @Column(name = "EMAIL")
    private String email;

    @Basic(optional = true)
    @Column(name = "PASSWORD")
    private String password;


    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "USERNAME", referencedColumnName = "CUIL", insertable = false, updatable = false)
    private BduPersonasFisicas persona;
    
    // Constructor con campos básicos
    public NslUsuarios(Long id, String username, String email, String password) {
        this.id = id;
        this.username = username;
        this.email = email;
        this.password = password;
    }
}
