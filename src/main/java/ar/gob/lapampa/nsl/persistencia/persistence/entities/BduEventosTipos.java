package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_EVENTOS_TIPOS")
@NamedQueries(value = {
    @NamedQuery(name = "BduEventosTipos.findAll", query = "SELECT b FROM BduEventosTipos b")})
public class BduEventosTipos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "GENERA_ALTA")
  private Character generaAlta;
  @Column(name = "GENERA_BAJA")
  private Character generaBaja;
  @Column(name = "AUTOMATICO")
  private Character automatico;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVA_LDAP")
  private Character activaLdap;
  @OneToMany(mappedBy = "tevId", fetch = FetchType.LAZY)
  private Set<BduActores> bduActoresSet;
  @JoinColumn(name = "ACC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduAcciones accId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "tevId", fetch = FetchType.LAZY)
  private Set<BduActEntidPerfEve> bduActEntidPerfEveSet;
  @OneToMany(mappedBy = "tevId", fetch = FetchType.LAZY)
  private Set<BduActEntidPerf> bduActEntidPerfSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "tevId", fetch = FetchType.LAZY)
  private Set<BduPerfilesEventos> bduPerfilesEventosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "tevId", fetch = FetchType.LAZY)
  private Set<BduEventosVinculos> bduEventosVinculosSet;
  @OneToMany(mappedBy = "tevPosibleId", fetch = FetchType.LAZY)
  private Set<BduEventosVinculos> bduEventosVinculosSet1;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "tevId", fetch = FetchType.LAZY)
  private Set<BduActoresEventos> bduActoresEventosSet;

  public BduEventosTipos(Long id) {
this.id = id;
  }

  public BduEventosTipos(Long id, String descripcion, Character activaLdap) {
this.id = id;
    this.descripcion = descripcion;
    this.activaLdap = activaLdap;
  }

}
