package ar.gob.lapampa.nsl.persistencia.dtos.requests;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AccionRequestDTO {

  @NotBlank(message = "El nombre del permiso no puede estar vacío.")
  @Size(min = 3, max = 50, message = "El nombre del permiso debe tener entre 3 y 50 caracteres.")
  private String nombre; // Ejemplo: "USER_WRITE", "USER_READ"

  @Size(min = 3, max = 100, message = "El endpoint no puede exceder los 100 caracteres.")
  private String endpoint; // Ejemplo: "/reportes/liquidacionesRecibo", puede ser opcional

  @Size(min = 3, max = 200, message = "La nota no puede exceder los 200 caracteres.")
  private String nota; // Ejemplo: "Es para el permisos de /liquidacionesRecibo", puede ser opcional

  // paginado
  @NotNull
  private Integer size;
  @NotNull
  private Integer page;
  @NotNull
  private String sortBy;
  private Boolean asc;
}
