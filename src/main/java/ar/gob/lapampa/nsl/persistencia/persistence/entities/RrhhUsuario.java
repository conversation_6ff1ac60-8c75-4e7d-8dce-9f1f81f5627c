package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
@Table(name = "RRHH_USUARIOS")
@SequenceGenerator(name = "RRHH_USR_SEQ", sequenceName = "RRHH_USR_SEQ", allocationSize = 1)
public class RrhhUsuario implements Serializable {

  @Serial
  private static final long serialVersionUID = -8869515926033801846L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_USR_SEQ")
  private Long id;

  @Basic(optional = false)
  @Size(min = 1, max = 20)
  @Column(name = "LOGIN")
  private String login;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.EAGER)
  private BduPersonasFisicas prsId;

  // @ManyToMany(cascade = { CascadeType.ALL })
  @ManyToMany(fetch = FetchType.LAZY)
  @JoinTable(name = "RRHH_USUARIOS_ROLES",
      joinColumns = {@JoinColumn(name = "RRHH_USR_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "RRHH_ROL_ID", referencedColumnName = "ID")})
  private List<RrhhRol> roles;

  @ManyToMany(fetch = FetchType.LAZY)
  @JoinTable(name = "RRHH_USUARIOS_ESCALAFONES",
      joinColumns = {@JoinColumn(name = "RRHH_USR_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "PRE_ESC_ID", referencedColumnName = "ID")})
  private List<PreEscalafones> convenios;

  @Basic(optional = false)
  @Size(min = 1, max = 1)
  @Column(name = "DEMO")
  // @Convert(converter = YesNoConverter.class)
  private String demo;

  @Column(name = "FECHA_BAJA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaBaja;

  public RrhhUsuario(@Nonnull Long id) {
    this.id = id;
  }

}
