package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_SOLICITUDWS")
@NamedQueries(value = {
    @NamedQuery(name = "BduSolicitudws.findAll", query = "SELECT b FROM BduSolicitudws b")})
public class BduSolicitudws implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 2000)
  @Column(name = "TOKEN")
  private String token;
  @Column(name = "FECHA_EXPIRA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaExpira;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 1)
  @Column(name = "ACTIVO")
  private String activo;
  @JoinColumn(name = "USUARIOWS_FSI_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduUsuarioswsFsi usuariowsFsiId;

  public BduSolicitudws(Long id) {
    this.id = id;
  }

  public BduSolicitudws(Long id, String token, String activo) {
    this.id = id;
    this.token = token;
    this.activo = activo;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduSolicitudws[ id=" + id + " ]";
  }

}
