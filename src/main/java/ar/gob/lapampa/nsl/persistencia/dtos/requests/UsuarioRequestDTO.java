package ar.gob.lapampa.nsl.persistencia.dtos.requests;

import java.io.Serial;
import java.io.Serializable;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UsuarioRequestDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 4023764303714935532L;
  private String login;
  private String names;
  private String demo;
  private String enNsl;
  @NotNull
  private Boolean active;
  @NotNull
  private Integer size;
  @NotNull
  private Integer page;
  @NotNull
  private String sortBy;
  private Boolean asc;
}
