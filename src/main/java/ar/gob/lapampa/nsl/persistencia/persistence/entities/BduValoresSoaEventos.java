package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_VALORES_SOA_EVENTOS")
@NamedQueries(value = {@NamedQuery(name = "BduValoresSoaEventos.findAll",
    query = "SELECT b FROM BduValoresSoaEventos b")})
public class BduValoresSoaEventos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 400)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @JoinColumn(name = "SET_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduSoaEventosTipos setId;
  @JoinColumn(name = "VLC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduSoaVuelcos vlcId;
  @JoinColumn(name = "MDS_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduValoresSoa mdsId;

  public BduValoresSoaEventos(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduValoresSoaEventos[ id=" + id + " ]";
  }

}
