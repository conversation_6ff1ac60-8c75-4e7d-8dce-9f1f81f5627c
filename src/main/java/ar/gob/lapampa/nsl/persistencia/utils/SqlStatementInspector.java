package ar.gob.lapampa.nsl.persistencia.utils;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.regex.Pattern;
import org.hibernate.resource.jdbc.spi.StatementInspector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import com.zaxxer.hikari.HikariDataSource;
import oracle.jdbc.driver.OracleConnection;

@Component
public class SqlStatementInspector implements StatementInspector {

  /**
   * Current {@link spring.application.name} name defined in pom.xml
   */
  @Value("${spring.application.name}")
  private String appName;

  /**
   * Version in pom.xml
   */
  @Value("${project.version}")
  private String version;

  @Autowired
  private HikariDataSource dataSource;

  private static final long serialVersionUID = -2940646936433443385L;

  private static final Logger LOGGER = LoggerFactory.getLogger(SqlStatementInspector.class);

  private static final Pattern SQL_COMMENT_PATTERN = Pattern.compile("\\/\\*.*?\\*\\/\\s*");


  @Override
  public String inspect(String sql) {


    // obtencion del usuario logueado (cuil)
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    String userName = authentication.getName();
    // WebAuthenticationDetails detalles = (WebAuthenticationDetails) authentication.getDetails();
    // UserDetailsImpl principal = (UserDetailsImpl) authentication.getPrincipal();
    try (Connection conn = dataSource.getConnection()) {
      OracleConnection unwrapped = conn.unwrap(oracle.jdbc.driver.OracleConnection.class);
      CallableStatement cs = unwrapped.prepareCall("{ call DBMS_SESSION.SET_IDENTIFIER(?) }");
      cs.setString(1, userName);
      // cs.setString(2, detalles.getRemoteAddress())
      // cs.setString(3, principal.getId().toString())
      // cs.setString(4, appName)
      // cs.setString(5, version)
      cs.execute();
      // cs.close(); NUNCA CERRAR PORQUE ROMPE EL POOL
      sql += "-- " + userName;
      LOGGER.info("Inspected query: {}", sql);
      LOGGER.info("User: {}", userName);
      // LOGGER.info("RemoteAddress: {}", detalles.getRemoteAddress())
      // LOGGER.info("NSL-UserId: {}", principal.getId())
    } catch (SQLException e) {
      LOGGER.error("Error sql: {}", e.getMessage());
    }

    // Clear comments
    // return SQL_COMMENT_PATTERN.matcher(sql).replaceAll("")

    return sql;

  }
}
