package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class BduFuncAccPerfilesPK implements Serializable {

  private static final long serialVersionUID = 1L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "PFL_ID")
  private long pflId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FSI_ID")
  private long fsiId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACC_ID")
  private long accId;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduFuncAccPerfilesPK[ pflId=" + pflId
        + ", fsiId=" + fsiId + ", accId=" + accId + " ]";
  }

}
