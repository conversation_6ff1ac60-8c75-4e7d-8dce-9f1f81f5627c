package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "SIAF_ACTIVIDADES_PRESUP")
@NamedQueries(value ={@NamedQuery(name = "SiafActividadesPresup.findAll",
    query = "SELECT s FROM SiafActividadesPresup s")})
public class SiafActividadesPresup implements Serializable {

  private static final long serialVersionUID = 6310276681472975605L;

  @EmbeddedId
  protected SiafActividadesPresupPK siafActividadesPresupPK;

  public SiafActividadesPresup(SiafActividadesPresupPK siafActividadesPresupPK) {
    this.siafActividadesPresupPK = siafActividadesPresupPK;
  }

  public SiafActividadesPresup(long actId, long finalidad, long funcion, long departamento,
      long ubicGeografica) {
    siafActividadesPresupPK =
        new SiafActividadesPresupPK(actId, finalidad, funcion, departamento, ubicGeografica);
  }

}
