package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ACTIVIDAD")
@NamedQueries(
    value = {@NamedQuery(name = "BduActividad.findAll", query = "SELECT b FROM BduActividad b")})
public class BduActividad implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 20)
  @Column(name = "CODIGO_ACTIV")
  private String codigoActiv;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Size(max = 400)
  @Column(name = "OBSERVACIONES")
  private String observaciones;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @JoinColumn(name = "SRB_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduSubrubro srbId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "avdId", fetch = FetchType.LAZY)
  private Set<BduProvActividades> bduProvActividadesSet;

  public BduActividad(Long id) {
    this.id = id;
  }

  public BduActividad(Long id, String codigoActiv, String descripcion, Character activo) {
    this.id = id;
    this.codigoActiv = codigoActiv;
    this.descripcion = descripcion;
    this.activo = activo;
  }

}
