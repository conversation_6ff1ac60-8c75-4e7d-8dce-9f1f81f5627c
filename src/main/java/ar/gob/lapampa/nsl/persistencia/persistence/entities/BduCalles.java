package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_CALLES")
@NamedQueries(
    value = {@NamedQuery(name = "BduCalles.findAll", query = "SELECT b FROM BduCalles b")})
public class BduCalles implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "TCL_ID")
  private long tclId;
  @Size(max = 200)
  @Column(name = "NOMBRE")
  private String nombre;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ODT_ID")
  private long odtId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "cllId", fetch = FetchType.LAZY)
  private Set<BduCuadras> bduCuadrasSet;

  public BduCalles(Long id) {
    this.id = id;
  }

  public BduCalles(Long id, long tclId, long odtId, Character activo) {
    this.id = id;
    this.tclId = tclId;
    this.odtId = odtId;
    this.activo = activo;
  }

}
