package ar.gob.lapampa.nsl.persistencia.dtos.requests;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PermisoRequestDTO {

  @NotBlank(message = "El nombre de la acción no puede estar vacío.")
  @Size(min = 3, max = 100, message = "El nombre de la acción debe tener entre 3 y 100 caracteres.")
  private String nombre; // Ejemplo: "Gestionar Reportes", "Ver Usuarios"

  @Size(max = 255, message = "El endpoint no puede exceder los 255 caracteres.")
  private String endpoint; // Ejemplo: "/api/v1/reportes/**", puede ser opcional

  @Size(max = 200, message = "La nota no puede exceder los 200 caracteres.")
  private String nota; // Ejemplo: "Es para endpoint /reportes", puede ser opcional
}
