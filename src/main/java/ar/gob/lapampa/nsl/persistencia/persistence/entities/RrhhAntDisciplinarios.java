package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_ANT_DISCIPLINARIOS")
@NamedQueries(value = {@NamedQuery(name = "RrhhAntDisciplinarios.findAll",
    query = "SELECT r FROM RrhhAntDisciplinarios r")})
public class RrhhAntDisciplinarios implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected RrhhAntDisciplinariosPK rrhhAntDisciplinariosPK;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_VIG_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigDesde;
  @Column(name = "FECHA_VIG_HASTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigHasta;
  @Column(name = "NOR_ID")
  private Long norId;
  @Size(max = 4000)
  @Column(name = "OBSERVACIONES")
  private String observaciones;
  @Column(name = "FECHA_APROBACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAprobacion;
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;
  @Column(name = "ESTADO")
  private Character estado;
  @Size(max = 4000)
  @Column(name = "COMENTARIO")
  private String comentario;
  @Column(name = "FECHA_ANULACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAnulacion;
  @Column(name = "FECHA_CIERRE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaCierre;
  @Column(name = "NOR_NRO")
  private Long norNro;
  @JoinColumn(name = "ADT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhAntDisciplinariosTipos adtId;
  @JoinColumn(name = "SUM_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhSumarios sumId;

  public RrhhAntDisciplinarios(RrhhAntDisciplinariosPK rrhhAntDisciplinariosPK) {
    this.rrhhAntDisciplinariosPK = rrhhAntDisciplinariosPK;
  }

  public RrhhAntDisciplinarios(RrhhAntDisciplinariosPK rrhhAntDisciplinariosPK,
      Date fechaVigDesde) {
    this.rrhhAntDisciplinariosPK = rrhhAntDisciplinariosPK;
    this.fechaVigDesde = fechaVigDesde;
  }

  public RrhhAntDisciplinarios(long ocuId, long item) {
    this.rrhhAntDisciplinariosPK = new RrhhAntDisciplinariosPK(ocuId, item);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhAntDisciplinarios[ rrhhAntDisciplinariosPK="
        + rrhhAntDisciplinariosPK + " ]";
  }

}
