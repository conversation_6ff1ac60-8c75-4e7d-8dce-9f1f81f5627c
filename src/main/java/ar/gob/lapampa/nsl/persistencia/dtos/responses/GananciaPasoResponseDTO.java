package ar.gob.lapampa.nsl.persistencia.dtos.responses;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class GananciaPasoResponseDTO implements Serializable {
  private static final long serialVersionUID = 7518880547167713113L;
  private Long agente;
  private String descripcion;
  private String vlrAlf;
  private Long vlrNum;
  private String fechaMov;
  private Long tipo;

  public GananciaPasoResponseDTO(Long agente, String descripcion, String vlrAlf, Long vlrNum,
      Timestamp fechaMov, Long tipo) {
    this.agente = agente;
    this.descripcion = descripcion;
    this.vlrAlf = vlrAlf;
    this.vlrNum = vlrNum;
    this.fechaMov = fechaMov.toLocalDateTime().toString();
    this.tipo = tipo;

  }
}
