package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * The persistent class for the PRE_FUNCIONES database table.
 * 
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_FUNCIONES")
@SequenceGenerator(name = "PRE_FUN_SEQ", sequenceName = "PRE_FUN_SEQ", allocationSize = 1)
public class PreFunciones implements Serializable {
  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PRE_FUN_SEQ")
  private Long id;

  private Long codigo;

  private String descripcion;

  @JoinColumn(name = "FIN_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.LAZY)
  private PreFinalidades finId;

}
