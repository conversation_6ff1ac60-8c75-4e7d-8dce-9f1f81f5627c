package ar.gob.lapampa.nsl.persistencia.controllers;

import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.MenuRequestDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.SyncMenusRequestDTO;
import ar.gob.lapampa.nsl.persistencia.security.CustomSecurityEvaluator;
import ar.gob.lapampa.nsl.persistencia.services.RrhhMenuService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

/**
 * RRHHUsuario Controller
 * 
 * <AUTHOR> NSL
 *
 */
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/v1/app/menus")
public class MenuController {

  private final CustomSecurityEvaluator securityEvaluator;

  public MenuController(CustomSecurityEvaluator securityEvaluator) {
    this.securityEvaluator = securityEvaluator;
  }

  @Autowired
  private RrhhMenuService menuService;

  @Operation(summary = "Listado directo de menus de usuarios RRHH",
      description = "No recibe  parámetros", tags = {"Usuarios"}, hidden = false,
      deprecated = false)
  @ApiResponses({@ApiResponse(responseCode = "200", description = "Listado completo"),
      @ApiResponse(responseCode = "400", description = "Bad Request")})
  @GetMapping("/listar-menus-legacy")
  @Transactional
  // @PreAuthorize("hasAuthority('PERMISSION_USERS_READ') and
  // hasRole('PRESUPUESTO')")
  public @ResponseBody GenericResponseDTO listarMenuLegacy() {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("USER_READ", "USER_WRITE");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/menus/listar-menus-legacy", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso

    return menuService.listarMenusLegacy();
  }

  @PutMapping("/modificar-por-rolId/{rolId}")
  @Transactional
  // @PreAuthorize("hasAuthority('USER_WRITE') and
  // hasRole('ADMINISTRADOR')")
  public @ResponseBody GenericResponseDTO modificarMenusPorRol(
      @RequestBody SyncMenusRequestDTO request, @PathVariable Long rolId) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("USER_READ", "USER_WRITE");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/menus/modificar-por-rolId", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return menuService.actualizarMenusByRol(request.getMenus(), rolId);
  }

  @Operation(summary = "Obtiene menu por su Id", description = "Recibe  parámetros Long Id",
      tags = {"Menus"}, hidden = false, deprecated = false)
  @ApiResponses({@ApiResponse(responseCode = "200", description = "Listado completo"),
      @ApiResponse(responseCode = "400", description = "Bad Request")})
  @GetMapping("/obtenerPorId/{id}")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','USER_READ','','IS')")
  public @ResponseBody GenericResponseDTO getById(@PathVariable Long id) {
    return menuService.getById(id);
  }

  @Operation(summary = "Actualiza menu por su Id",
      description = "Recibe MenuRequestDTO y un parámetro id", tags = {"Menus"}, hidden = false,
      deprecated = false)
  @ApiResponses({@ApiResponse(responseCode = "200", description = "Objeto actualizado"),
      @ApiResponse(responseCode = "400", description = "Bad Request")})
  @PutMapping("/actualizarPorId/{id}")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','USER_READ, USER_WRITE','','IS')")
  public @ResponseBody GenericResponseDTO updateById(@RequestBody MenuRequestDTO request,
      @PathVariable Long id) {
    return menuService.updateById(request, id);
  }

  @Operation(summary = "Crea nuevo menu", description = "Recibe MenuRequestDTO", tags = {"Menus"},
      hidden = false, deprecated = false)
  @ApiResponses({@ApiResponse(responseCode = "200", description = "Objeto creado"),
      @ApiResponse(responseCode = "400", description = "Bad Request")})
  @PostMapping("/crear")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','USER_READ, USER_WRITE','','IS')")
  public @ResponseBody GenericResponseDTO crearMenu(@RequestBody MenuRequestDTO request) {
    return menuService.create(request);
  }

  @Operation(summary = "Borra menu por su Id", description = "Recibe  parámetro Long Id",
      tags = {"Menus"}, hidden = false, deprecated = false)
  @ApiResponses({@ApiResponse(responseCode = "200", description = "Borrado completo"),
      @ApiResponse(responseCode = "400", description = "Bad Request")})
  @DeleteMapping("/borrarPorId/{id}")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','USER_READ','','IS')")
  public @ResponseBody GenericResponseDTO deleteById(@PathVariable Long id) {
    return menuService.deleteById(id);
  }

}
