package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_CALCULO_COD6")
@NamedQueries(value = {
    @NamedQuery(name = "LiqCalculoCod6.findAll", query = "SELECT l FROM LiqCalculoCod6 l")})
public class LiqCalculoCod6 implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PUNTOS_DESDE")
  private short puntosDesde;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PROL_JOR")
  private Character prolJor;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANT_DESDE")
  private short antDesde;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MONTO")
  private BigDecimal monto;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PUNTOS_HASTA")
  private short puntosHasta;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANT_HASTA")
  private BigInteger antHasta;
  @JoinColumn(name = "COV_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqCodigosVersiones covId;

  public LiqCalculoCod6(Long id) {
    this.id = id;
  }

  public LiqCalculoCod6(Long id, short puntosDesde, Character prolJor, short antDesde,
      BigDecimal monto, short puntosHasta, BigInteger antHasta) {
    this.id = id;
    this.puntosDesde = puntosDesde;
    this.prolJor = prolJor;
    this.antDesde = antDesde;
    this.monto = monto;
    this.puntosHasta = puntosHasta;
    this.antHasta = antHasta;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqCalculoCod6[ id=" + id + " ]";
  }

}
