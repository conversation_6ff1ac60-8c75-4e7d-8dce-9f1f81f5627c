package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_TARIFAS")
@SequenceGenerator(name = "GAN_TARIFAS_SQ", sequenceName = "GAN_TARIFAS_SQ", allocationSize = 1)
public class GanTarifas implements Serializable {

  private static final long serialVersionUID = -110490707598365798L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_TARIFAS_SQ")
  private Long id;

  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Column(name = "CODIGO")
  private String codigo;

  @Column(name = "ANIO")
  private Short anio;

  @Column(name = "FECHA_VIGENCIA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigencia;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Column(name = "RANGOS")
  private Short rangos;

}
