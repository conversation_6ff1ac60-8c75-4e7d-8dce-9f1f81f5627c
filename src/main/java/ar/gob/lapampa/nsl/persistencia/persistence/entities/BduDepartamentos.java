package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_DEPARTAMENTOS")
@NamedQueries(value = {
    @NamedQuery(name = "BduDepartamentos.findAll", query = "SELECT b FROM BduDepartamentos b")})
public class BduDepartamentos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "NOMBRE")
  private String nombre;
  @Size(max = 10)
  @Column(name = "CODIGO")
  private String codigo;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ODT_ID")
  private long odtId;
  @JoinColumn(name = "PRV_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduProvincias prvId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "depId", fetch = FetchType.LAZY)
  private Set<BduLocalidades> bduLocalidadesSet;

  public BduDepartamentos(Long id) {
    this.id = id;
  }

  public BduDepartamentos(Long id, long odtId) {
    this.id = id;
    this.odtId = odtId;
  }

}
