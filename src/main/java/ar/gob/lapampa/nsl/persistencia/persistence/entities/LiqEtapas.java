package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_ETAPAS")
@NamedQueries(
    value = {@NamedQuery(name = "LiqEtapas.findAll", query = "SELECT l FROM LiqEtapas l")})
public class LiqEtapas implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 400)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "ORDEN")
  private Long orden;
  @Column(name = "TIPO_DE_TUPLA")
  private Character tipoDeTupla;

  public LiqEtapas(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqEtapas[ id=" + id + " ]";
  }

}
