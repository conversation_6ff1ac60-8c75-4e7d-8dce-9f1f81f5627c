package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_CAPACITACIONES")
public class RrhhCapacitaciones implements Serializable {

  private static final long serialVersionUID = 8405379175327012151L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Basic(optional = false)
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;

  @Basic(optional = false)
  @Column(name = "COMPLETO")
  private String completo;

  @Column(name = "OBSERVACIONES")
  private String observaciones;

  @Column(name = "PUBLICO")
  private String publico;

  @Column(name = "ANIO_EN_CURSO")
  private Short anioEnCurso;

  @Column(name = "UNIVERSIDAD")
  private String universidad;

  @Column(name = "CANT_MATERIAS_APROBADAS")
  private Short cantMateriasAprobadas;

  @Column(name = "FECHA_APROBACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAprobacion;

  @Column(name = "FECHA_VIG_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigDesde;

  @Column(name = "ESTADO")
  private String estado;

  @Column(name = "COMENTARIO")
  private String comentario;

  @Column(name = "FECHA_ANULACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAnulacion;

  @Column(name = "CARGA_HORARIA")
  private Long cargaHoraria;

  @JoinColumn(name = "TIT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduTitulos titId;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduPersonas prsId;

  @JoinColumn(name = "CRT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhCertificadosTipos crtId;

}
