package ar.gob.lapampa.nsl.persistencia.services.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.JPAExpressions;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.RrhhUsuarioDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.NslUserRequestDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.UsuarioRequestDTO;
import ar.gob.lapampa.nsl.persistencia.models.NslUserModel;
import ar.gob.lapampa.nsl.persistencia.models.RolFullModel;
import ar.gob.lapampa.nsl.persistencia.models.UsuarioModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.NslUsuarios;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QNslUsuarios;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QRrhhUsuario;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhAcciones;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhRol;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhUsuario;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.NslUserMapper;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.RrhhRolMapper;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.RrhhUsuarioMapper;
import ar.gob.lapampa.nsl.persistencia.repositories.NslUsuariosRepository;
import ar.gob.lapampa.nsl.persistencia.repositories.RrhhRolRepository;
import ar.gob.lapampa.nsl.persistencia.repositories.RrhhUsuarioRepository;
import ar.gob.lapampa.nsl.persistencia.services.RrhhUsuarioService;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
public class RrhhUsuarioServiceImpl implements RrhhUsuarioService {

  @Autowired
  private RrhhUsuarioRepository rrhhUsuarioRepository;

  @Autowired
  private NslUsuariosRepository nslUsuariosRepository;

  @Autowired
  private RrhhRolRepository rrhhRolRepository;

  Sort sort = Sort.unsorted();

  @Override
  public GenericResponseDTO listar(UsuarioRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();

    BooleanBuilder booleanBuilder = new BooleanBuilder();

    // Filtro por estado (activo/inactivo)
    if (Boolean.TRUE.equals(request.getActive())) {
      booleanBuilder.and(QRrhhUsuario.rrhhUsuario.fechaBaja.isNull());
    } else if (Boolean.FALSE.equals(request.getActive())) {
      booleanBuilder.and(QRrhhUsuario.rrhhUsuario.fechaBaja.isNotNull());
    }

    // Filtro por demo
    if (request.getDemo() != null && !request.getDemo().isEmpty()) {
      if (request.getDemo().toUpperCase().charAt(0) == 'Y') {
        booleanBuilder.and(QRrhhUsuario.rrhhUsuario.demo.eq("Y"));
      } else if (request.getDemo().toUpperCase().charAt(0) == 'N') {
        booleanBuilder.and(QRrhhUsuario.rrhhUsuario.demo.eq("N"));
      }
    }

    // Filtro por enNsl
    if (request.getEnNsl() != null && !request.getEnNsl().isEmpty()) {
      if (request.getEnNsl().toUpperCase().charAt(0) == 'S') {
        // Subconsulta: usuarios cuyo login existe en NSL_USUARIOS
        booleanBuilder.and(QRrhhUsuario.rrhhUsuario.login.in(JPAExpressions
            .select(QNslUsuarios.nslUsuarios.username).from(QNslUsuarios.nslUsuarios)));
      } else if (request.getEnNsl().toUpperCase().charAt(0) == 'N') {
        // Subconsulta: usuarios cuyo login NO existe en NSL_USUARIOS
        booleanBuilder.and(QRrhhUsuario.rrhhUsuario.login.notIn(JPAExpressions
            .select(QNslUsuarios.nslUsuarios.username).from(QNslUsuarios.nslUsuarios)));
      }
    }

    // Filtro por nombres o apellidos
    if (request.getNames() != null && !request.getNames().isEmpty()) {
      booleanBuilder.andAnyOf(
          QRrhhUsuario.rrhhUsuario.prsId.nombres.containsIgnoreCase(request.getNames()),
          QRrhhUsuario.rrhhUsuario.prsId.apellido.containsIgnoreCase(request.getNames()));
    }

    // Filtro por login
    if (request.getLogin() != null && !request.getLogin().isEmpty()) {
      booleanBuilder.and(QRrhhUsuario.rrhhUsuario.login.containsIgnoreCase(request.getLogin()));
    }

    // Configuración de ordenamiento
    if (request.getSortBy() != null && !request.getSortBy().isEmpty()) {
      if (Boolean.TRUE.equals(request.getAsc())) {
        sort = Sort.by(Sort.Direction.ASC, request.getSortBy());
      } else {
        sort = Sort.by(Sort.Direction.DESC, request.getSortBy());
      }
    } else {
      sort = Sort.unsorted();
    }

    // Agregar logging para depuración
    log.info("Filtros aplicados: active={}, demo={}, enNsl={}, names={}, login={}",
        request.getActive(), request.getDemo(), request.getEnNsl(), request.getNames(),
        request.getLogin());

    // Ejecutar la consulta
    Page<RrhhUsuario> page;
    if (booleanBuilder.getValue() != null) {
      page = rrhhUsuarioRepository.findAll(booleanBuilder.getValue(),
          PageRequest.of(request.getPage(), request.getSize(), sort));
      log.debug("Número de resultados encontrados: {}", page.getTotalElements());
    } else {
      page =
          rrhhUsuarioRepository.findAll(PageRequest.of(request.getPage(), request.getSize(), sort));
      log.debug("Número de resultados sin filtros: {}", page.getTotalElements());
    }

    // Mapear a UsuarioModel y agregar el atributo enNsl
    Page<UsuarioModel> pageDto = page.map(usuario -> {
      UsuarioModel model = RrhhUsuarioMapper.rrhhUsuarioToRrhhUsuarioModel(usuario);
      // Verificar si el login existe en NSL_USUARIOS
      model.setEnNsl(verificarEnNsl(usuario.getLogin()));
      return model;
    });

    response.setEstadoExito(pageDto);
    return response;
  }

  @Override
  public GenericResponseDTO registrar(RrhhUsuarioDTO rrhhUsuariosDTO) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public GenericResponseDTO modificar(RrhhUsuarioDTO rrhhUsuariosDTO) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public GenericResponseDTO eliminar(RrhhUsuarioDTO rrhhUsuariosDTO) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public GenericResponseDTO deshabilitar(RrhhUsuarioDTO rrhhUsuariosDTO) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public GenericResponseDTO habilitar(RrhhUsuarioDTO rrhhUsuariosDTO) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public GenericResponseDTO listarNsl(NslUserRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();

    BooleanBuilder booleanBuilder = new BooleanBuilder();

    // Add filter for username if provided
    if (request.getUsername() != null && !request.getUsername().isEmpty()) {
      booleanBuilder
          .and(QNslUsuarios.nslUsuarios.username.containsIgnoreCase(request.getUsername()));
    }

    // Add filter for email if provided
    if (request.getEmail() != null && !request.getEmail().isEmpty()) {
      booleanBuilder.and(QNslUsuarios.nslUsuarios.email.containsIgnoreCase(request.getEmail()));
    }

    // Add filter for apellidos if provided
    if (request.getApellidos() != null && !request.getApellidos().isEmpty()) {
      booleanBuilder.and(
          QNslUsuarios.nslUsuarios.persona.apellido.containsIgnoreCase(request.getApellidos()));
    }

    // Add filter for nombres if provided
    if (request.getNombres() != null && !request.getNombres().isEmpty()) {
      booleanBuilder
          .and(QNslUsuarios.nslUsuarios.persona.nombres.containsIgnoreCase(request.getNombres()));
    }

    // Set sorting
    if (request.getSortBy() != null && !request.getSortBy().isEmpty()) {
      if (Boolean.TRUE.equals(request.getAsc())) {
        sort = Sort.by(Sort.Direction.ASC, request.getSortBy());
      } else {
        sort = Sort.by(Sort.Direction.DESC, request.getSortBy());
      }
    } else {
      sort = Sort.unsorted();
    }

    Page<NslUsuarios> page;
    if (booleanBuilder.getValue() != null) {
      page = nslUsuariosRepository.findAll(booleanBuilder.getValue(),
          PageRequest.of(request.getPage(), request.getSize(), sort));
    } else {
      page =
          nslUsuariosRepository.findAll(PageRequest.of(request.getPage(), request.getSize(), sort));
    }

    Page<NslUserModel> pageDto = page.map(NslUserMapper::NslUsuariosToNslUserModel);
    response.setEstadoExito(pageDto);
    return response;
  }

  private static List<String> verificarDependenciasRol(RrhhRol rol) {
    List<String> dependenciasActivas = new ArrayList<>();

    // Verificar dependencias
    // Gracias a FetchType.LAZY, estas llamadas .isEmpty()
    // resultan en queries eficientes (SELECT COUNT(*)) en lugar de cargar toda la colección.
    if (rol.getUsuarios() != null && !rol.getUsuarios().isEmpty()) {
      dependenciasActivas.add("usuarios asignados (" + rol.getUsuarios().size() + ")");
    }
    if (rol.getMenues() != null && !rol.getMenues().isEmpty()) {
      dependenciasActivas.add("menús asociados (" + rol.getMenues().size() + ")");
    }
    if (rol.getAcciones() != null && !rol.getAcciones().isEmpty()) {
      dependenciasActivas.add("acciones vinculadas (" + rol.getAcciones().size() + ")");
    }
    if (rol.getEmpresas() != null && !rol.getEmpresas().isEmpty()) {
      dependenciasActivas.add("empresas relacionadas (" + rol.getEmpresas().size() + ")");
    }
    return dependenciasActivas;
  }

  private static List<String> verificarDependenciasAccion(RrhhAcciones accion) {
    List<String> dependenciasActivas = new ArrayList<>();

    // Verificar dependencias
    if (accion.getRoles() != null && !accion.getRoles().isEmpty()) {
      dependenciasActivas.add("roles asignados (" + accion.getRoles().size() + ")");
    }

    return dependenciasActivas;
  }

  public String verificarEnNsl(String username) {
    return nslUsuariosRepository.existsByUsername(username) ? "Si" : "No";
  }

  @Override
  public GenericResponseDTO rolPorId(Long id) {
    final GenericResponseDTO response = new GenericResponseDTO();
    Optional<RrhhRol> role = rrhhRolRepository.findById(id);
    if (role.isPresent()) {
      RolFullModel rolFullModel = RrhhRolMapper.rrhhRolToRrhhRolFullModel(role.get());
      response.setEstadoExito(rolFullModel);
    } else {
      response.setEstadoError("Rol no encontrado");
    }
    return response;
  }

  @Override
  public GenericResponseDTO listarRapido() {
    final GenericResponseDTO response = new GenericResponseDTO();
    List<UsuarioModel> listado = rrhhUsuarioRepository.encontrarRapido();
    response.setEstadoExito(listado);
    return response;
  }

  @Override
  public GenericResponseDTO actualizarUsuariosByRol(List<UsuarioModel> request, Long rolId) {
    final GenericResponseDTO response = new GenericResponseDTO();

    try {
      Optional<RrhhRol> rolOptional = rrhhRolRepository.findById(rolId);
      if (rolOptional.isPresent()) {
        // aseguramos valores unicos
        Set<Long> idsNuevosUsuarios =
            request.stream().map(UsuarioModel::getId).collect(Collectors.toSet());
        // Convertir el set de longs a una lista de strings separados por comas
        String usuariosListStr =
            idsNuevosUsuarios.stream().map(String::valueOf).collect(Collectors.joining(","));

        // Sincronizamos con la DB
        String result = rrhhUsuarioRepository.syncUsuariosRoles(rolId, usuariosListStr);

        // Verificar si el resultado es nulo antes de usarlo
        if (result == null) {
          response.setEstadoError("La operación en base de datos no devolvió un resultado.");
          log.warn("El stored procedure para rol ID {} devolvió null.", rolId);
          return response;
        } else if (result.toUpperCase().contains("ERROR")) {
          response.setEstadoError(result);
          log.warn("Error devuelto por el stored procedure para rol ID {}: {}", rolId, result);
          return response;
        } else {
          response.setEstadoExito("Ok");
          response.setMensaje(result);
          log.info("Usuarios actualizados mediante stored procedure. Resultado: {}", result);
          return response;
        }
      } else {
        response.setEstadoError("Rol con ID " + rolId + " no encontrado.");
        log.warn("Intento de actualizar usuarios de rol no existente: ID={}", rolId);
      }
    } catch (Exception e) {
      log.error("Error al actualizar los usuarios para el rol: {}", rolId, e);
      response.setEstadoError("Error interno al actualizar los usuarios: " + e.getMessage());
    }
    return response;
  }

}
