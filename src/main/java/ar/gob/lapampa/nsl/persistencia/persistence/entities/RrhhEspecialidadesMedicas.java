package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_ESPECIALIDADES_MEDICAS")
@NamedQueries(value = {@NamedQuery(name = "RrhhEspecialidadesMedicas.findAll",
    query = "SELECT r FROM RrhhEspecialidadesMedicas r")})
public class RrhhEspecialidadesMedicas implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Size(max = 4000)
  @Column(name = "EME_PADRE_ID")
  private String emePadreId;

  public RrhhEspecialidadesMedicas(Long id) {
    this.id = id;
  }

  public RrhhEspecialidadesMedicas(Long id, String descripcion) {
    this.id = id;
    this.descripcion = descripcion;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhEspecialidadesMedicas[ id=" + id + " ]";
  }

}
