package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.sql.Timestamp;
import com.querydsl.core.annotations.QueryEntity;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@QueryEntity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_PASOS")
@SequenceGenerator(name = "GAN_PASOS_SQ", sequenceName = "GAN_PASOS_SQ", allocationSize = 1)
public class GanPaso implements Serializable {

  private static final long serialVersionUID = 2565913636990866089L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_LOTE_DDJJ572_SQ")
  private Long id;

  @Column(name = "TIPO")
  private Long tipo;

  @Column(name = "PRC_ID")
  private Long prcId;

  @Column(name = "AGENTE")
  private Long agente;

  @Column(name = "ANIO")
  private Long anio;

  @Column(name = "MES")
  private Long mes;

  @Column(name = "VLR_NUM")
  private Long vlrNum;

  @Column(name = "VLR_ALF")
  private String vlrAlf;

  @Column(name = "FCH_MOV")
  private Timestamp fechaMov;

  @Column(name = "OCU_ID")
  private Long ocuId;

  @Column(name = "EMP_ID")
  private Long empId;

  @Column(name = "COD_GNCIA")
  private Long codGanancia;

  @Column(name = "CORRIDA")
  private Long corrida;
}
