package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ORG_PRE_MAILS")
@NamedQueries(value = {
    @NamedQuery(name = "BduOrgPreMails.findAll", query = "SELECT b FROM BduOrgPreMails b")})
public class BduOrgPreMails implements Serializable {
  private static final long serialVersionUID = 1L;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ORP_ID")
  private long orpId;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @JoinColumn(name = "MAI_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduMail maiId;

  public BduOrgPreMails(Long id) {
    this.id = id;
  }

  public BduOrgPreMails(Long id, long orpId) {
    this.id = id;
    this.orpId = orpId;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduOrgPreMails[ id=" + id + " ]";
  }

}
