package ar.gob.lapampa.nsl.persistencia.models;

import java.io.Serializable;
import com.querydsl.core.annotations.QueryProjection;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CodigoModel extends BaseRRHHDTO implements Serializable{

    private static final long serialVersionUID = 4774068467086902865L;

    private String nombreCorto;
    private String nro;
    private Long cotId;

    @QueryProjection
    public CodigoModel(Long id) {
        super();
        this.id = id;
    }

    /**
     * Obtiene Nombre de class name solo
     *
     * @return String
     */
    @Override
    public String getBeanName() {
        return CodigoModel.class.getSimpleName();
    }

    /**
     * Obtiene Nombre de package + class name
     *
     * @return String
     */
    @Override
    public String getCualifiedName() {
        return CodigoModel.class.getCanonicalName();
    }

}
