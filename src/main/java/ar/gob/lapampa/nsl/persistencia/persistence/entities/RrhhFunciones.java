package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_FUNCIONES")
@SequenceGenerator(name = "RRHH_FUN_SEQ", sequenceName = "RRHH_FUN_SEQ", allocationSize = 1)
public class RrhhFunciones implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_FUN_SEQ")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Column(name = "NRO")
  private Long nro;

  @OneToMany(mappedBy = "funId", fetch = FetchType.LAZY)
  private Set<RrhhOcupacionesMov> rrhhOcupacionesMovSet;

  @JoinColumn(name = "ESC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PreEscalafones escId;

  @Column(name = "FECHA_VIG_DESDE_FUN")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigDesdeFun;

  @Column(name = "FECHA_VIG_HASTA_FUN")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigHastaFun;

  @Column(name = "OBSERVACION_FUN")
  private String observacionFun;

  public RrhhFunciones(Long id) {

    this.id = id;
  }

  public RrhhFunciones(Long id, String descripcion, Boolean activo) {

    this.id = id;
    this.descripcion = descripcion;
    this.activo = activo;
  }

}
