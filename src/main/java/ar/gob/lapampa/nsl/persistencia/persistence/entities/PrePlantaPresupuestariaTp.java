package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_PLANTA_PRESUPUESTARIA_TP")
public class PrePlantaPresupuestariaTp implements Serializable {

  private static final long serialVersionUID = -3902082840728651125L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Column(name = "DESCRIPCION")
  private String descripcion;

  @JoinColumn(name = "PCP_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.LAZY)
  private PreCuentas pcpId;

  @JoinColumn(name = "PPR_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.LAZY)
  private PrePlantaPresupuestaria pprId;

  @JoinColumn(name = "PLT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.LAZY)
  private PrePlantasTipos pltId;

}
