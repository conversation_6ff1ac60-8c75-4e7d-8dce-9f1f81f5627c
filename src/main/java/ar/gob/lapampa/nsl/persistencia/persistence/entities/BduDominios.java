package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_DOMINIOS")
@NamedQueries(
    value = {@NamedQuery(name = "BduDominios.findAll", query = "SELECT b FROM BduDominios b")})
public class BduDominios implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "dmnId", fetch = FetchType.LAZY)
  private Set<BduDominiosDatos> bduDominiosDatosSet;
  @OneToOne(mappedBy = "dmnId", fetch = FetchType.LAZY)
  private BduMetadatosDominios bduMetadatosDominios;

  public BduDominios(Long id) {
    this.id = id;
  }

  public BduDominios(Long id, String descripcion) {
    this.id = id;
    this.descripcion = descripcion;
  }

}
