package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class BduAtrComprtPK implements Serializable {

  private static final long serialVersionUID = 2537012199930121922L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ATR_ID")
  private long atrId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "VALOR")
  private long valor;

}
