package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_LOCALIDADES")
@NamedQueries(value = {
    @NamedQuery(name = "BduLocalidades.findAll", query = "SELECT b FROM BduLocalidades b")})
public class BduLocalidades implements Serializable {

  private static final long serialVersionUID = -6150212714552691232L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Size(max = 200)
  @Column(name = "NOMBRE")
  private String nombre;

  @Size(max = 10)
  @Column(name = "CODIGO")
  private String codigo;

  @Size(max = 20)
  @Column(name = "CODIGO_POSTAL")
  private String codigoPostal;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ODT_ID")
  private long odtId;

  @Column(name = "ETP_ID")
  private Long etpId;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "lcdId", fetch = FetchType.LAZY)
  private Set<BduBarrios> bduBarriosSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "lcdId", fetch = FetchType.LAZY)
  private Set<BduCuadras> bduCuadrasSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "lcdId", fetch = FetchType.LAZY)
  private Set<BduDomicilios> bduDomiciliosSet;

  @JoinColumn(name = "DEP_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduDepartamentos depId;

  @JoinColumn(name = "PROV_ID", referencedColumnName = "ID")
  @OneToOne(optional = false, fetch = FetchType.LAZY)
  private BduProvincias provincia;


  public BduLocalidades(Long id) {
    this.id = id;
  }

  public BduLocalidades(Long id, long odtId) {
    this.id = id;
    this.odtId = odtId;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduLocalidades[ id=" + id + " ]";
  }


}
