package ar.gob.lapampa.nsl.persistencia.configs;

import org.hibernate.cfg.AvailableSettings;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import ar.gob.lapampa.nsl.persistencia.utils.SqlStatementInspector;

@Configuration
public class AppConfig {

  @Bean
  HibernatePropertiesCustomizer hibernateCustomizer(SqlStatementInspector statementInspector) {
    return properties -> {
      // properties.put(AvailableSettings.STATEMENT_INSPECTOR, statementInspector)
      properties.put(AvailableSettings.SHOW_SQL, false);
      properties.put(AvailableSettings.FORMAT_SQL, false);
      properties.put(AvailableSettings.USE_SQL_COMMENTS, true);
    };
  }
}
