package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.List;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PARM_PARTIDOS_POLITICOS")
public class ParmPartidosPolitico implements Serializable {

  private static final long serialVersionUID = -1220119536701278974L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Column(name = "ACTIVO")
  private String activo;

  @Column(name = "DESCRIPCION")
  private String descripcion;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "parPolId", fetch = FetchType.LAZY)
  private Set<LiqOcupacionesPartPol> liqOcupacionesPartPol;

  @ManyToMany(cascade = CascadeType.REFRESH, fetch = FetchType.LAZY)
  @JoinTable(name = "LIQ_PART_POL_DELEG",
      joinColumns = @JoinColumn(name = "PAR_POL_ID", referencedColumnName = "ID"),
      inverseJoinColumns = @JoinColumn(name = "DEL_ID", referencedColumnName = "ID"))
  private List<ParmDelegPartPol> delegaciones;

}
