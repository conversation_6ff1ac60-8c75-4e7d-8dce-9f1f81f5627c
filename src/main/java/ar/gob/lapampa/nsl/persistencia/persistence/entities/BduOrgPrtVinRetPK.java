package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class BduOrgPrtVinRetPK implements Serializable {

  private static final long serialVersionUID = -6746298312073639662L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ORT_ID_ORIGEN")
  private long ortIdOrigen;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ORT_ID_DESTINO")
  private long ortIdDestino;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "RET_ID")
  private long retId;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduOrgPrtVinRetPK[ ortIdOrigen=" + ortIdOrigen
        + ", ortIdDestino=" + ortIdDestino + ", retId=" + retId + " ]";
  }

}
