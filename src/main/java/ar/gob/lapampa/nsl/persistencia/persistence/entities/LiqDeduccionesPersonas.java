package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_DEDUCCIONES_PERSONAS")
@NamedQueries(value = {@NamedQuery(name = "LiqDeduccionesPersonas.findAll",
    query = "SELECT l FROM LiqDeduccionesPersonas l")})
public class LiqDeduccionesPersonas implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRS_ID")
  private long prsId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MES")
  private short mes;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANIO")
  private short anio;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Basic(optional = false)
  @Nonnull
  @Column(name = "TOTAL_HABERES")
  private BigDecimal totalHaberes;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "TOTAL_DEDUCIDO")
  private BigDecimal totalDeducido;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "TOTAL_REAL")
  private BigDecimal totalReal;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "liqDeduccionesPersonas", fetch = FetchType.LAZY)
  private Set<LiqDeduccionesPersonasDet> liqDeduccionesPersonasDetSet;

  public LiqDeduccionesPersonas(Long id) {
    this.id = id;
  }

  public LiqDeduccionesPersonas(Long id, long prsId, short mes, short anio, BigDecimal totalHaberes,
      BigDecimal totalDeducido, BigDecimal totalReal) {
    this.id = id;
    this.prsId = prsId;
    this.mes = mes;
    this.anio = anio;
    this.totalHaberes = totalHaberes;
    this.totalDeducido = totalDeducido;
    this.totalReal = totalReal;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqDeduccionesPersonas[ id=" + id + " ]";
  }

}
