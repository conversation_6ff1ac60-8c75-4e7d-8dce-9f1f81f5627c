package ar.gob.lapampa.nsl.persistencia.controllers;

import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.request.RrhhEmpresaRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.SyncEmpresasRequestDTO;
import ar.gob.lapampa.nsl.persistencia.security.CustomSecurityEvaluator;
import ar.gob.lapampa.nsl.persistencia.services.RrhhEmpresaService;

/**
 * RRHHEmpresa Controller
 *
 * <AUTHOR> NSL
 *
 */
@RestController
@RequestMapping("/api/v1/app/empresas")
public class EmpresaController {

  private final CustomSecurityEvaluator securityEvaluator;

  public EmpresaController(CustomSecurityEvaluator securityEvaluator) {
    this.securityEvaluator = securityEvaluator;
  }

  @Autowired
  private RrhhEmpresaService empresaService;

  @PostMapping("/listar")
  // @Transactional
  @PreAuthorize("@cs.hasAccess('','EMPRESA_R','','IS')")
  public @ResponseBody GenericResponseDTO listar(@RequestBody RrhhEmpresaRequestDTO request) {
    return empresaService.listar(request);
  }

  @PostMapping("/listarConveniosEmpresa")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','EMPRESA_R','','IS')")
  public @ResponseBody GenericResponseDTO listarConveniosPorEmpresa(
      @RequestBody RrhhEmpresaRequestDTO request) {
    return empresaService.listarConveniosPorEmpresa(request);
  }

  @PutMapping("/modificar-por-rolId/{rolId}")
  @Transactional
  // @PreAuthorize("hasAuthority('USER_WRITE') and
  // hasRole('ADMINISTRADOR')")
  public @ResponseBody GenericResponseDTO modificarEmpresasPorRol(
      @RequestBody SyncEmpresasRequestDTO request, @PathVariable Long rolId) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("EMPRESA_R", "EMPRESA_W");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/empresas/modificar-por-rolId", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return empresaService.actualizarEmpresasByRol(request.getEmpresas(), rolId);
  }
}
