package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_DOMICILIO_CONSULTA_AUDIT")
@NamedQueries(value = {@NamedQuery(name = "BduDomicilioConsultaAudit.findAll",
    query = "SELECT b FROM BduDomicilioConsultaAudit b")})
public class BduDomicilioConsultaAudit implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRS_ID")
  private long prsId;
  @Column(name = "TDM_ID")
  private Long tdmId;
  @Column(name = "FECHA_AUD")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAud;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "SIS_ID")
  private long sisId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACT_ID")
  private long actId;

  public BduDomicilioConsultaAudit(Long id) {
    this.id = id;
  }

  public BduDomicilioConsultaAudit(Long id, long prsId, long sisId, long actId) {
    this.id = id;
    this.prsId = prsId;
    this.sisId = sisId;
    this.actId = actId;
  }

}
