package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class SiafActividadesPresupPK implements Serializable {

  private static final long serialVersionUID = -5419434535640285180L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACT_ID")

  private long actId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FINALIDAD")
  private long finalidad;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FUNCION")
  private long funcion;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "DEPARTAMENTO")
  private long departamento;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "UBIC_GEOGRAFICA")
  private long ubicGeografica;

}
