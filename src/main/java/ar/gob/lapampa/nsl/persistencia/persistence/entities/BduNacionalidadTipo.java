package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_NACIONALIDAD_TIPO")
@NamedQueries(value = {@NamedQuery(name = "BduNacionalidadTipo.findAll",
    query = "SELECT b FROM BduNacionalidadTipo b")})
// @SequenceGenerator(name = "BDU_PRF_SEQ", sequenceName = "BDU_PRF_SEQ",
// allocationSize = 1)
public class BduNacionalidadTipo implements Serializable {

  private static final long serialVersionUID = 77995907623377263L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  // @GeneratedValue(strategy = GenerationType.SEQUENCE, generator =
  // "BDU_PRF_SEQ")
  private Long id;

  @Size(max = 100)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Size(max = 20)
  @Column(name = "ABREVIATURA")
  private String abreviatura;

  @OneToMany(mappedBy = "tnaId", fetch = FetchType.LAZY)
  private Set<BduPersonasFisicas> bduPersonasFisicasSet;

  public BduNacionalidadTipo(Long id) {
    this.id = id;
  }

}
