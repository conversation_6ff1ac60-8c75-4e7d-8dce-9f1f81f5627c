package ar.gob.lapampa.nsl.persistencia.services;

import java.util.List;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.RrhhUsuarioDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.NslUserRequestDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.UsuarioRequestDTO;
import ar.gob.lapampa.nsl.persistencia.models.UsuarioModel;

@Service
public interface RrhhUsuarioService {

  GenericResponseDTO listar(UsuarioRequestDTO request);

  GenericResponseDTO registrar(RrhhUsuarioDTO rrhhUsuariosDTO);

  GenericResponseDTO modificar(RrhhUsuarioDTO rrhhUsuariosDTO);

  GenericResponseDTO eliminar(RrhhUsuarioDTO rrhhUsuariosDTO);

  GenericResponseDTO deshabilitar(RrhhUsuarioDTO rrhhUsuariosDTO);

  GenericResponseDTO habilitar(RrhhUsuarioDTO rrhhUsuariosDTO);

  GenericResponseDTO listarNsl(NslUserRequestDTO request);

  String verificarEnNsl(String username);

  GenericResponseDTO rolPorId(Long id);

  GenericResponseDTO listarRapido();

  GenericResponseDTO actualizarUsuariosByRol(List<UsuarioModel> request, Long rolId);

}
