package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_VARIABLES")
@SequenceGenerator(name = "LIQ_VAR_SEQ", sequenceName = "LIQ_VAR_SEQ", allocationSize = 1)
public class LiqVariables implements Serializable {
  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_VAR_SEQ")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Basic(optional = false)

  @Size(min = 1, max = 40)
  @Column(name = "NOMBRE_CORTO")
  private String nombreCorto;

  @Column(name = "VALOR")
  private BigDecimal valor;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Basic(optional = false)
  @Nonnull
  @Convert(converter = YesNoConverter.class)
  @Column(name = "CON_PARAMETRO")
  private Boolean conParametro;

  @JoinColumn(name = "CAT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PreCategorias catId;

  @Size(max = 100)
  @Column(name = "PROCESO")
  private String proceso;

  @JoinColumn(name = "VAT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqVariablesTipos vatId;

  public LiqVariables(Long id) {
    this.id = id;
  }

  public LiqVariables(Long id, String descripcion, String nombreCorto, BigDecimal valor,
      Boolean activo, Boolean conParametro) {
    this.id = id;
    this.descripcion = descripcion;
    this.nombreCorto = nombreCorto;
    this.valor = valor;
    this.activo = activo;
    this.conParametro = conParametro;
  }

}
