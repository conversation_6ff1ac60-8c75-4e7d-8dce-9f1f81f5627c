package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_OCUPACIONES_CONCEPTOS")
@SequenceGenerator(name = "RRHH_OCU_CON_SEQ", sequenceName = "RRHH_OCU_CON_SEQ", allocationSize = 1)
public class RrhhOcupacionesConceptos implements Serializable {

  private static final long serialVersionUID = -5567465408545409806L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_OCU_CON_SEQ")
  private Long id;

  @Basic(optional = false)
  @Column(name = "ITEM")
  private Long item;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "MES")
  private Short mes;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANIO")
  private Short anio;

  @Column(name = "NOR_ID")
  private Long norId;

  @Column(name = "FECHA_APROBACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAprobacion;

  @Column(name = "ESTADO")
  private String estado;

  @Size(max = 4000)
  @Column(name = "COMENTARIO")
  private String comentario;

  @Column(name = "FECHA_ANULACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAnulacion;

  @Column(name = "CANTIDAD")
  private BigDecimal cantidad;

  @Column(name = "MONTO")
  private BigDecimal monto;

  @Column(name = "NOR_NRO")
  private Long norNro;

  @Size(max = 1000)
  @Column(name = "USUARIO_MOV")
  private String usuarioMov;

  @Column(name = "FECHA_MOV")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaMov;

  @Column(name = "CALCULADO")
  private Character calculado;

  @Column(name = "PRC_ID")
  private BigInteger prcId;

  @JoinColumn(name = "COD_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqCodigos codId;

  @JoinColumn(name = "CPL_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqComplementarias cplId;

  @JoinColumn(name = "OCU_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhOcupaciones ocuId;

  @Basic(optional = true)
  @Column(name = "MES_RETROACTIVO")
  private Short mesRetroactivo;

  @Basic(optional = true)
  @Column(name = "ANIO_RETROACTIVO")
  private Short anioRetroactivo;

}
