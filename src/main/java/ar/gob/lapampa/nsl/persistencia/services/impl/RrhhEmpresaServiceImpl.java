package ar.gob.lapampa.nsl.persistencia.services.impl;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import ar.gob.lapampa.nsl.datatransfer.request.RrhhEmpresaRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.models.EmpresaModel;
import ar.gob.lapampa.nsl.persistencia.models.EscalafonModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.PreEscalafones;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QPreEscalafones;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QRrhhEmpresa;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QRrhhEmpresasEscalafones;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhEmpresa;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhRol;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.PreEscalafonMapper;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.RrhhEmpresaMapper;
import ar.gob.lapampa.nsl.persistencia.repositories.RrhhEmpresaRepository;
import ar.gob.lapampa.nsl.persistencia.repositories.RrhhRolRepository;
import ar.gob.lapampa.nsl.persistencia.services.RrhhEmpresaService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class RrhhEmpresaServiceImpl implements RrhhEmpresaService {

  @Autowired
  private RrhhEmpresaRepository rrhhEmpresaRepository;
  Sort sort = Sort.unsorted();

  @Autowired
  private RrhhRolRepository rrhhRolRepository;

  @PersistenceContext
  private EntityManager entityManager;

  @Override
  public GenericResponseDTO listar(RrhhEmpresaRequestDTO request) {

    final GenericResponseDTO response = new GenericResponseDTO();
    BooleanBuilder booleanBuilder = new BooleanBuilder();

    if (Boolean.TRUE.equals(request.getActivo()))
      booleanBuilder.and(QRrhhEmpresa.rrhhEmpresa.activo.isTrue());
    else
      booleanBuilder.and(QRrhhEmpresa.rrhhEmpresa.activo.isFalse());

    if (!"".equals(request.getSortBy())) {
      if (Boolean.TRUE.equals(request.getAsc())) {
        sort = Sort.by(Sort.Direction.ASC, request.getSortBy());
      } else {
        sort = Sort.by(Sort.Direction.DESC, request.getSortBy());
      }
    } else {
      sort = Sort.unsorted();
    }

    List<RrhhEmpresa> rrhhEmpresas =
        (List<RrhhEmpresa>) this.rrhhEmpresaRepository.findAll(booleanBuilder.getValue(), sort);
    List<EmpresaModel> empresaModels =
        rrhhEmpresas.stream().map(RrhhEmpresaMapper::rrhhEmpresaToEmpresaModel).toList();

    response.setEstadoExito(empresaModels);
    return response;
  }

  @Override
  public GenericResponseDTO listarConveniosPorEmpresa(RrhhEmpresaRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();

    QPreEscalafones convenio = QPreEscalafones.preEscalafones;
    QRrhhEmpresasEscalafones empresaConvenio = QRrhhEmpresasEscalafones.rrhhEmpresasEscalafones;

    JPAQuery<Object> query = new JPAQuery<>(entityManager);

    List<PreEscalafones> preEscalafones =
        query.select(convenio).from(empresaConvenio).join(empresaConvenio.convenio, convenio)
            .where(empresaConvenio.empresa.id.eq(request.getId()))
            .where(empresaConvenio.convenio.activo.isTrue()).orderBy(convenio.nro.asc()).fetch();

    List<EscalafonModel> respuesta =
        preEscalafones.stream().map(PreEscalafonMapper::preEscalafonToEscalafonModel).toList();
    response.setEstadoExito(respuesta);
    return response;
  }

  @Override
  public GenericResponseDTO actualizarEmpresasByRol(List<EmpresaModel> request, Long rolId) {
    final GenericResponseDTO response = new GenericResponseDTO();

    try {
      Optional<RrhhRol> rolOptional = rrhhRolRepository.findById(rolId);
      if (rolOptional.isPresent()) {
        // aseguramos valores unicos
        Set<Long> idsNuevasEmpresas =
            request.stream().map(EmpresaModel::getId).collect(Collectors.toSet());
        // Convertir el set de longs a una lista de strings separados por comas
        String empresasListStr =
            idsNuevasEmpresas.stream().map(String::valueOf).collect(Collectors.joining(","));

        // Sincronizamos con la DB
        String result = rrhhEmpresaRepository.syncEmpresasRoles(rolId, empresasListStr);

        // Verificar si el resultado es nulo antes de usarlo
        if (result == null) {
          response.setEstadoError("La operación en base de datos no devolvió un resultado.");
          log.warn("El stored procedure para rol ID {} devolvió null.", rolId);
          return response;
        } else if (result.toUpperCase().contains("ERROR")) {
          response.setEstadoError(result);
          log.warn("Error devuelto por el stored procedure para rol ID {}: {}", rolId, result);
          return response;
        } else {
          response.setEstadoExito("Ok");
          response.setMensaje(result);
          log.info("Empresas actualizadas mediante stored procedure. Resultado: {}", result);
          return response;
        }
      } else {
        response.setEstadoError("Rol con ID " + rolId + " no encontrado.");
        log.warn("Intento de actualizar empresas de rol no existente: ID={}", rolId);
      }
    } catch (Exception e) {
      log.error("Error al actualizar las empresas para el rol: {}", rolId, e);
      response.setEstadoError("Error interno al actualizar las empresas: " + e.getMessage());
    }
    return response;
  }
}
