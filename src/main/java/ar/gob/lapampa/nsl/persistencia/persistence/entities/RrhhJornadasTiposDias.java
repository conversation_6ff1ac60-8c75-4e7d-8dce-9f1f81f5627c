package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_JORNADAS_TIPOS_DIAS")
@NamedQueries(value = {@NamedQuery(name = "RrhhJornadasTiposDias.findAll",
    query = "SELECT r FROM RrhhJornadasTiposDias r")})
public class RrhhJornadasTiposDias implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected RrhhJornadasTiposDiasPK rrhhJornadasTiposDiasPK;
  @Column(name = "CDAD_HORAS")
  private Short cdadHoras;
  @Column(name = "HORA_DESDE_1")
  private Short horaDesde1;
  @Column(name = "HORA_HASTA_1")
  private Short horaHasta1;
  @Column(name = "HORA_DESDE_2")
  private Short horaDesde2;
  @Column(name = "HORA_HASTA_2")
  private Short horaHasta2;
  @JoinColumn(name = "DSE_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhDiasSemana rrhhDiasSemana;
  @JoinColumn(name = "JOR_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhJornadasTipos rrhhJornadasTipos;

  public RrhhJornadasTiposDias(RrhhJornadasTiposDiasPK rrhhJornadasTiposDiasPK) {
    this.rrhhJornadasTiposDiasPK = rrhhJornadasTiposDiasPK;
  }

  public RrhhJornadasTiposDias(long jorId, long dseId) {
    this.rrhhJornadasTiposDiasPK = new RrhhJornadasTiposDiasPK(jorId, dseId);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhJornadasTiposDias[ rrhhJornadasTiposDiasPK="
        + rrhhJornadasTiposDiasPK + " ]";
  }

}
