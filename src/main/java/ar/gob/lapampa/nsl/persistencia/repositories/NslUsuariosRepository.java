package ar.gob.lapampa.nsl.persistencia.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.NslUsuarios;

public interface NslUsuariosRepository
    extends JpaRepository<NslUsuarios, Long>, QuerydslPredicateExecutor<NslUsuarios> {
  boolean existsByUsername(String username);
}
