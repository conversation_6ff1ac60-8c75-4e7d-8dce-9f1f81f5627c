package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_DOCUMENTAL_PERSONAS")
@NamedQueries(value = {@NamedQuery(name = "BduDocumentalPersonas.findAll",
    query = "SELECT b FROM BduDocumentalPersonas b")})
public class BduDocumentalPersonas implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRS_ID")
  private long prsId;
  @Lob
  @Column(name = "FACSIMIL", columnDefinition = "BLOB")
  private Byte[] facsimil;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDesde;
  @Column(name = "FECHA_HASTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaHasta;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ODT_ID")
  private long odtId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @Size(max = 200)
  @Column(name = "NOMBRE_ARCHIVO")
  private String nombreArchivo;
  @JoinColumn(name = "TDL_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduDocumentalTipo tdlId;

  public BduDocumentalPersonas(Long id) {
    this.id = id;
  }

  public BduDocumentalPersonas(Long id, long prsId, Date fechaDesde, long odtId, Character activo) {
    this.id = id;
    this.prsId = prsId;
    this.fechaDesde = fechaDesde;
    this.odtId = odtId;
    this.activo = activo;
  }

}
