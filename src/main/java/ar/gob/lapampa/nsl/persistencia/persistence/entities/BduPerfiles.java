package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PERFILES")
@NamedQueries(
    value = {@NamedQuery(name = "BduPerfiles.findAll", query = "SELECT b FROM BduPerfiles b")})
public class BduPerfiles implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "NIVEL")
  private Short nivel;
  @Column(name = "NVL_PERMISO")
  private Short nvlPermiso;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "CIRCUITO")
  private short circuito;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduPerfiles", fetch = FetchType.LAZY)
  private Set<BduFuncionesPerfiles> bduFuncionesPerfilesSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduPerfiles", fetch = FetchType.LAZY)
  private Set<BduFuncAccPerfiles> bduFuncAccPerfilesSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "pflId", fetch = FetchType.LAZY)
  private Set<BduActEntidPerf> bduActEntidPerfSet;
  @JoinColumn(name = "PLP_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduPerfLdap plpId;
  @JoinColumn(name = "SIS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduSistemas sisId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "pflId", fetch = FetchType.LAZY)
  private Set<BduPerfilesEventos> bduPerfilesEventosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "pflId", fetch = FetchType.LAZY)
  private Set<BduPerfilesPermisos> bduPerfilesPermisosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "pflNvl2Id", fetch = FetchType.LAZY)
  private Set<BduPerfilesAnidados> bduPerfilesAnidadosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "pflNvl3Id", fetch = FetchType.LAZY)
  private Set<BduPerfilesAnidados> bduPerfilesAnidadosSet1;

  public BduPerfiles(Long id) {
    this.id = id;
  }

  public BduPerfiles(Long id, short circuito) {
    this.id = id;
    this.circuito = circuito;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduPerfiles[ id=" + id + " ]";
  }

}
