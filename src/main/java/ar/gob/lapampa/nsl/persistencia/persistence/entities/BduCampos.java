package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_CAMPOS")
@NamedQueries(
    value = {@NamedQuery(name = "BduCampos.findAll", query = "SELECT b FROM BduCampos b")})
public class BduCampos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "ACTIVO")
  private Character activo;
  @Column(name = "FECHA_DETECTADA_BAJA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDetectadaBaja;
  @Column(name = "FECHA_ALTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAlta;
  @Column(name = "FECHA_MODIFICACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaModificacion;
  @Size(max = 20)
  @Column(name = "TIPO_DATO")
  private String tipoDato;
  @JoinColumn(name = "ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @OneToOne(optional = false, fetch = FetchType.LAZY)
  private BduAtributos bduAtributos;
  @JoinColumn(name = "TBL_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduTablas tblId;

  public BduCampos(Long id) {
    this.id = id;
  }

}
