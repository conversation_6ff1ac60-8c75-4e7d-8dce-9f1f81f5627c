package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * The persistent class for the LIQ_SEGUROS_OP_DETALLE database table.
 * 
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_SEGUROS_OP_DETALLE")
@SequenceGenerator(name = "LIQ_SEG_OP_SEQ", sequenceName = "LIQ_SEG_OP_SEQ", allocationSize = 1)
public class LiqSegurosOpDetalle implements Serializable {

  private static final long serialVersionUID = 3096824811507805499L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_SEG_OP_SEQ")
  private Long id;

  @Convert(converter = YesNoConverter.class)
  @Column(name = "ADIC_ACCIDENTE_CONYUGE")
  private Boolean adicAccidenteConyuge;

  @Convert(converter = YesNoConverter.class)
  @Column(name = "ADIC_ACCIDENTE_TITULAR")
  private Boolean adicAccidenteTitular;


  @Convert(converter = YesNoConverter.class)
  private Boolean conyuge;


  private String estado;

  @Convert(converter = YesNoConverter.class)
  @Column(name = "FAMILIAR")
  private Boolean familiar;

  @Temporal(TemporalType.DATE)
  private Date fecha;

  @Temporal(TemporalType.DATE)
  @Column(name = "FECHA_ANULACION")
  private Date fechaAnulacion;

  @Temporal(TemporalType.DATE)
  @Column(name = "FECHA_APROBACION")
  private Date fechaAprobacion;

  @Temporal(TemporalType.DATE)
  @Column(name = "FECHA_BAJA")
  private Date fechaBaja;

  @Temporal(TemporalType.DATE)
  @Column(name = "FECHA_NAC_CYGE")
  private Date fechaNacCyge;

  @Column(name = "ID_CYGE")
  private BigDecimal idCyge;

  @Column(name = "PRIMA_MES_ANT_CYGE")
  private BigDecimal primaMesAntCyge;

  @Column(name = "PRIMA_MES_ANT_TIT")
  private BigDecimal primaMesAntTit;

  @Column(name = "REM_CATEGORIA")
  private BigDecimal remCategoria;

  /*
   * @JoinColumn(name = "OCU_ID", referencedColumnName = "ID")
   * 
   * @ManyToOne(optional = false, fetch = FetchType.LAZY) private RrhhOcupaciones ocuId
   */

  @JoinColumn(name = "SEG_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqSegurosOp segId;

  @Convert(converter = YesNoConverter.class)
  private Boolean sepelio;

  @Column(name = "TASA_CYGE")
  private BigDecimal tasaCyge;

  @Column(name = "PORC_CYGE")
  private BigDecimal porcCyge; // esto es para saber en cual de los conceptos sale

  @Column(name = "TASA_FAMILIAR")
  private BigDecimal tasaFamiliar;

  public BigDecimal getPorcCyge() {
    return porcCyge;
  }

  public void setPorcCyge(BigDecimal porcCyge) {
    this.porcCyge = porcCyge;
  }

  @Column(name = "TASA_TITULAR")
  private BigDecimal tasaTitular;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private BduPersonasFisicas prsId;

}
