package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ENTIDADES")
@NamedQueries(
    value = {@NamedQuery(name = "BduEntidades.findAll", query = "SELECT b FROM BduEntidades b")})
@SequenceGenerator(name = "BDU_ENT_SEQ", sequenceName = "BDU_ENT_SEQ", allocationSize = 1)
public class BduEntidades implements Serializable {

  private static final long serialVersionUID = -1063065612977792042L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BDU_ENT_SEQ")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "PRS_ID")
  private Long prsId;
  @Column(name = "ORF_ID")
  private Long orfId;
  @Column(name = "LCD_ID")
  private Long lcdId;
  @Column(name = "ORF_ID_BASE")
  private Long orfIdBase;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "entId", fetch = FetchType.LAZY)
  private Set<BduActores> bduActoresSet;
  @JoinColumn(name = "RES_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduNivelesResponsab resId;
  @JoinColumn(name = "ORG_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduOrigenes orgId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "entId", fetch = FetchType.LAZY)
  private Set<BduActEntidPerf> bduActEntidPerfSet;

  public BduEntidades(Long id) {
    this.id = id;
  }

}
