package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_AJUSTES_DET_DDJJ572")
@SequenceGenerator(name = "GAN_AJUSTES_DET_DDJJ572_SQ", sequenceName = "GAN_AJUSTES_DET_DDJJ572_SQ",
    allocationSize = 1)
public class GanAjustesDetDdjj572 implements Serializable {

  private static final long serialVersionUID = -1798697099407777660L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_AJUSTES_DET_DDJJ572_SQ")
  private Long id;

  @JoinColumn(name = "AJUSTE", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  private GanAjustesDdjj572 ajuste;

  @Column(name = "NOMBRE")
  private String nombre;

  @Column(name = "VALOR")
  private String valor;

}
