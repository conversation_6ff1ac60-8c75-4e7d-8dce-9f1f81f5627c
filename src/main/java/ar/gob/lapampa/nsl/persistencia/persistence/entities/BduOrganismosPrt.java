package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ORGANISMOS_PRT")
@NamedQueries(value = {
    @NamedQuery(name = "BduOrganismosPrt.findAll", query = "SELECT b FROM BduOrganismosPrt b")})
public class BduOrganismosPrt implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "CODIGO")
  private Short codigo;
  @Column(name = "DMC_ID")
  private Long dmcId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDesde;
  @Column(name = "ORT_ID_BASE")
  private Long ortIdBase;
  @OneToMany(mappedBy = "ortId", fetch = FetchType.LAZY)
  private Set<BduOrganismosMapeos> bduOrganismosMapeosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduOrganismosPrt", fetch = FetchType.LAZY)
  private Set<BduOrgPrtVinRet> bduOrgPrtVinRetSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduOrganismosPrt1", fetch = FetchType.LAZY)
  private Set<BduOrgPrtVinRet> bduOrgPrtVinRetSet1;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "ortId", fetch = FetchType.LAZY)
  private Set<BduOtrosMediosPrt> bduOtrosMediosPrtSet;
  @JoinColumn(name = "NVL_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduNiveles nvlId;
  @OneToMany(mappedBy = "ortId", fetch = FetchType.LAZY)
  private Set<BduOrganismosPrt> bduOrganismosPrtSet;
  @JoinColumn(name = "ORT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduOrganismosPrt ortId;
  @JoinColumn(name = "ODT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduOrigenesDatos odtId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "ortId", fetch = FetchType.LAZY)
  private Set<BduOrgPrtTelefonos> bduOrgPrtTelefonosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "ortId", fetch = FetchType.LAZY)
  private Set<BduOrgPrtMails> bduOrgPrtMailsSet;

  public BduOrganismosPrt(Long id) {
    this.id = id;
  }

  public BduOrganismosPrt(Long id, Character activo, Date fechaDesde) {
    this.id = id;
    this.activo = activo;
    this.fechaDesde = fechaDesde;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduOrganismosPrt[ id=" + id + " ]";
  }

}
