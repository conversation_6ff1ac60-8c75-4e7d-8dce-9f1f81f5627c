package ar.gob.lapampa.nsl.persistencia.services.impl;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import com.querydsl.core.BooleanBuilder;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.RrhhUsuarioLogDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.UsuarioLogRequestDTO;
import ar.gob.lapampa.nsl.persistencia.models.UsuarioLogModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QRrhhUsuarioLog;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhUsuarioLog;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.RrhhUsuarioLogMapper;
import ar.gob.lapampa.nsl.persistencia.repositories.RrhhUsuarioLogRepository;
import ar.gob.lapampa.nsl.persistencia.services.RrhhUsuarioLogService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class RrhhUsuarioLogServiceImpl implements RrhhUsuarioLogService {

  @Autowired
  private RrhhUsuarioLogRepository rrhhUsuarioLogRepository;

  private Sort sort;

  @Override
  public GenericResponseDTO listar(UsuarioLogRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();
    BooleanBuilder booleanBuilder = new BooleanBuilder();

    log.info("Listando logs de usuario con filtros: login={}, tipo={}, estado={}, desde={}, hasta={}",
             request.getLogin(), request.getTipo(), request.getEstado(), request.getDesde(), request.getHasta());

    // Aplicar filtros según los campos de request
    if (request.getLogin() != null && !request.getLogin().isEmpty()) {
      booleanBuilder
          .and(QRrhhUsuarioLog.rrhhUsuarioLog.login.containsIgnoreCase(request.getLogin()));
    }

    if (request.getRole() != null && !request.getRole().isEmpty()) {
      booleanBuilder.and(QRrhhUsuarioLog.rrhhUsuarioLog.role.containsIgnoreCase(request.getRole()));
    }

    if (request.getTipo() != null && !request.getTipo().isEmpty()) {
      booleanBuilder.and(QRrhhUsuarioLog.rrhhUsuarioLog.tipo.containsIgnoreCase(request.getTipo()));
    }

    if (request.getEstado() != null && !request.getEstado().isEmpty()) {
      booleanBuilder
          .and(QRrhhUsuarioLog.rrhhUsuarioLog.estado.containsIgnoreCase(request.getEstado()));
    }

    if (request.getResultado() != null && !request.getResultado().isEmpty()) {
      booleanBuilder
          .and(QRrhhUsuarioLog.rrhhUsuarioLog.resultado.containsIgnoreCase(request.getResultado()));
    }

    // Filtrado de fechas con ajuste de horas
    if (request.getDesde() != null) {
      Date fechaDesde = ajustarFechaInicio(request.getDesde());
      log.debug("Filtro desde: {} -> {}", request.getDesde(), fechaDesde);
      booleanBuilder.and(QRrhhUsuarioLog.rrhhUsuarioLog.creado.goe(fechaDesde));
    }

    if (request.getHasta() != null) {
      Date fechaHasta = ajustarFechaFin(request.getHasta());
      log.debug("Filtro hasta: {} -> {}", request.getHasta(), fechaHasta);
      booleanBuilder.and(QRrhhUsuarioLog.rrhhUsuarioLog.creado.loe(fechaHasta));
    }

    // Configurar ordenamiento - VERIFICAR QUE EL CAMPO EXISTE
    if (request.getSortBy() != null && !request.getSortBy().isEmpty()) {
      // Verificar que el campo de ordenamiento existe en la entidad
      if (fieldExists(request.getSortBy())) {
        if (Boolean.TRUE.equals(request.getAsc())) {
          sort = Sort.by(Sort.Direction.ASC, request.getSortBy());
        } else {
          sort = Sort.by(Sort.Direction.DESC, request.getSortBy());
        }
      } else {
        // Si el campo no existe, usar el ordenamiento por defecto
        sort = Sort.by(Sort.Direction.DESC, "creado");
      }
    } else {
      sort = Sort.by(Sort.Direction.DESC, "creado"); // Ordenamiento por defecto
    }

    // Ejecutar consulta paginada
    Page<RrhhUsuarioLog> page;
    if (booleanBuilder.getValue() != null) {
      page = rrhhUsuarioLogRepository.findAll(booleanBuilder.getValue(),
          PageRequest.of(request.getPage(), request.getSize(), sort));
    } else {
      page = rrhhUsuarioLogRepository
          .findAll(PageRequest.of(request.getPage(), request.getSize(), sort));
    }

    // Mapear resultados al DTO de respuesta
    Page<UsuarioLogModel> pageDto =
        page.map(RrhhUsuarioLogMapper::rrhhUsuarioLogToRrhhUsuarioLogModel);

    log.info("Consulta completada. Página {}/{}, {} elementos de {} totales",
             pageDto.getNumber() + 1, pageDto.getTotalPages(),
             pageDto.getNumberOfElements(), pageDto.getTotalElements());

    response.setEstadoExito(pageDto);
    return response;
  }

  @Override
  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public GenericResponseDTO registrar(RrhhUsuarioLogDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();

    try {

      // Mapear el DTO a entidad
      RrhhUsuarioLog usuarioLog = new RrhhUsuarioLog();
      usuarioLog.setLogin(request.getLogin());
      usuarioLog.setRole(request.getRole());
      usuarioLog.setTipo(request.getTipo());
      usuarioLog.setEstado(request.getEstado());
      usuarioLog.setResultado(request.getResultado());

      // Establecer fecha de creación si no viene en el DTO
      if (request.getCreado() == null) {
        usuarioLog.setCreado(new Date());
      } else {
        usuarioLog.setCreado(request.getCreado());
      }

      // Guardar en la base de datos
      RrhhUsuarioLog savedLog = rrhhUsuarioLogRepository.save(usuarioLog);
      System.out.println("Log guardado con ID: " + savedLog.getId());

      // Convertir la entidad guardada a modelo para la respuesta
      UsuarioLogModel logModel = RrhhUsuarioLogMapper.rrhhUsuarioLogToRrhhUsuarioLogModel(savedLog);

      // Configurar respuesta exitosa
      response.setEstadoExito(logModel);
      response.setMensaje("Log de usuario registrado exitosamente");

    } catch (Exception e) {
      // Manejar errores
      System.err.println("ERROR en registrar() de RrhhUsuarioLogServiceImpl: " + e.getMessage());
      e.printStackTrace();
      response.setEstadoError("Error al registrar log de usuario: " + e.getMessage());
    }

    return response;
  }

  @Override
  public GenericResponseDTO modificar(RrhhUsuarioLogDTO request, long id) {
    final GenericResponseDTO response = new GenericResponseDTO();

    try {
      // Buscar el registro existente por ID
      Optional<RrhhUsuarioLog> usuarioLogOptional = rrhhUsuarioLogRepository.findById(id);

      if (usuarioLogOptional.isPresent()) {
        RrhhUsuarioLog usuarioLog = usuarioLogOptional.get();

        // Actualizar los campos con los valores del DTO
        if (request.getLogin() != null) {
          usuarioLog.setLogin(request.getLogin());
        }

        if (request.getRole() != null) {
          usuarioLog.setRole(request.getRole());
        }

        if (request.getTipo() != null) {
          usuarioLog.setTipo(request.getTipo());
        }

        if (request.getEstado() != null) {
          usuarioLog.setEstado(request.getEstado());
        }

        if (request.getResultado() != null) {
          usuarioLog.setResultado(request.getResultado());
        }

        if (request.getCreado() != null) {
          usuarioLog.setCreado(request.getCreado());
        }

        // Guardar los cambios
        RrhhUsuarioLog updatedLog = rrhhUsuarioLogRepository.save(usuarioLog);

        // Convertir la entidad actualizada a modelo para la respuesta
        UsuarioLogModel logModel =
            RrhhUsuarioLogMapper.rrhhUsuarioLogToRrhhUsuarioLogModel(updatedLog);

        // Configurar respuesta exitosa
        response.setEstadoExito(logModel);
        response.setMensaje("Log de usuario actualizado exitosamente");

      } else {
        // El registro no existe
        response.setEstadoError("No se encontró el log de usuario con ID: " + id);
      }

    } catch (Exception e) {
      // Manejar errores
      response.setEstadoError("Error al actualizar log de usuario: " + e.getMessage());
    }

    return response;
  }

  @Override
  public GenericResponseDTO eliminar(Long id) {
    final GenericResponseDTO response = new GenericResponseDTO();

    try {
      // Verificar que el DTO tenga un ID válido
      if (id == null) {
        response.setEstadoError("ID de log de usuario no proporcionado");
        return response;
      }

      // Verificar que el registro exista
      Optional<RrhhUsuarioLog> usuarioLogOptional = rrhhUsuarioLogRepository.findById(id);

      if (usuarioLogOptional.isPresent()) {
        // Eliminar el registro
        rrhhUsuarioLogRepository.deleteById(id);

        // Configurar respuesta exitosa
        response.setEstadoExito(id);
        response.setMensaje("Log de usuario eliminado exitosamente");

      } else {
        // El registro no existe
        response.setEstadoError("No se encontró el log de usuario con ID: " + id);
      }

    } catch (Exception e) {
      // Manejar errores
      response.setEstadoError("Error al eliminar log de usuario: " + e.getMessage());
    }

    return response;
  }

  // Método auxiliar para verificar si un campo existe en la entidad RrhhUsuarioLog
  private boolean fieldExists(String fieldName) {
    // Lista de campos válidos en RrhhUsuarioLog
    List<String> validFields =
        Arrays.asList("id", "login", "role", "tipo", "estado", "creado", "resultado");
    return validFields.contains(fieldName);
  }

  /**
   * Ajusta la fecha de inicio para incluir desde las 00:00:00.000 del día especificado
   * Para filtros de rango, normalmente queremos desde el inicio del día
   */
  private Date ajustarFechaInicio(Date fecha) {
    if (fecha == null) {
      return null;
    }

    Calendar cal = Calendar.getInstance();
    cal.setTime(fecha);

    // Para fecha "desde", establecer al inicio del día si no tiene segundos específicos
    if (cal.get(Calendar.SECOND) == 0 && cal.get(Calendar.MILLISECOND) == 0) {
      // Si solo tiene hora y minutos (como "12:56"), mantener esa hora específica
      // Si es medianoche (00:00), también mantenerla
      log.debug("Fecha desde manteniendo hora específica: {}", fecha);
    }

    return cal.getTime();
  }

  /**
   * Ajusta la fecha de fin para incluir registros hasta la hora especificada
   * Para filtros de rango, si no se especifican segundos, incluir hasta el final del minuto
   */
  private Date ajustarFechaFin(Date fecha) {
    if (fecha == null) {
      return null;
    }

    Calendar cal = Calendar.getInstance();
    cal.setTime(fecha);

    // Para fecha "hasta", si no tiene segundos específicos, incluir hasta el final del minuto
    if (cal.get(Calendar.SECOND) == 0 && cal.get(Calendar.MILLISECOND) == 0) {
      // Si es medianoche (00:00), extender hasta el final del día
      if (cal.get(Calendar.HOUR_OF_DAY) == 0 && cal.get(Calendar.MINUTE) == 0) {
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
        log.debug("Fecha hasta ajustada al final del día: {} -> {}", fecha, cal.getTime());
      } else {
        // Si tiene hora específica (como 12:56), incluir hasta el final de ese minuto
        cal.set(Calendar.SECOND, 59);
        cal.set(Calendar.MILLISECOND, 999);
        log.debug("Fecha hasta ajustada al final del minuto: {} -> {}", fecha, cal.getTime());
      }
    } else {
      log.debug("Fecha hasta con segundos específicos: {}", fecha);
    }

    return cal.getTime();
  }
}
