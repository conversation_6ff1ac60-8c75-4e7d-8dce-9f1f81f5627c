package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_EMBARGOS")
@SequenceGenerator(name = "LIQ_EMB_SEQ", sequenceName = "LIQ_EMB_SEQ", allocationSize = 1)
public class LiqEmbargos implements Serializable {

  private static final long serialVersionUID = -2012369768367605637L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_EMB_SEQ")
  private Long id;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduPersonasFisicas prsId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDesde;

  @Basic(optional = false)
  @Column(name = "MONTO_TOTAL")
  private BigDecimal montoTotal;

  @Column(name = "PORC_MENSUAL")
  private BigDecimal porcMensual;

  @Column(name = "MONTO_FIJO_MENSUAL")
  private BigDecimal montoFijoMensual;

  @Column(name = "MONTO_SALDADO")
  private BigDecimal montoSaldado;

  @Column(name = "OBSERVACION")
  private String observacion;

  @Column(name = "PRS_BEN_ID")
  private Long prsBenId;

  @Column(name = "NRO_OFICIO")
  private String nroOficio;

  @Column(name = "FECHA_FIN")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaFin;

  @Column(name = "CARATULA")
  private String caratula;

  @Column(name = "JUEZ")
  private String juez;

  @Column(name = "SECRETARIA")
  private String secretaria;

  @Column(name = "CTA_JUDICIAL")
  private String ctaJudicial;

  @JoinColumn(name = "EMP_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhEmpresa empId;

  @Column(name = "ENT_ID")
  private Long entId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;

  @Column(name = "NOM_BENEF")
  private String nomBenef;

  @Column(name = "APE_BENEF")
  private String apeBenef;

  @Column(name = "INCLUYE_SAC")
  @Convert(converter = YesNoConverter.class)
  private Boolean incluyeSac;

  @JoinColumn(name = "COD_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqCodigos codId;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "liqEmbargos", fetch = FetchType.LAZY)
  private Set<LiqEmbargosDetalle> liqEmbargosDetalleSet;

  @Column(name = "PORC_SAC")
  private BigDecimal porcentajeSac;

  @Column(name = "MONTO_SAC")
  private BigDecimal montoSac;

  @Basic(optional = false)
  @Column(name = "FECHA_NOTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaNotaOficio;

  @Column(name = "ESTADO")
  private String estado;

  @Column(name = "EXPEDIENTE")
  private String expediente;

  @Column(name = "CUENTA_BANCO")
  private String cuentaBanco;

  @Column(name = "CUENTA_BANCO_DENOMINACION")
  private String cuentaBancoDenominacion;

  @Column(name = "DESCRIPCION_HAB_REC")
  private String descripcionHabRec;

  @Column(name = "CBU_DESTINATARIO")
  private String cbuDestinatario;

  @Column(name = "ORDEN")
  private Integer orden;

  @JoinColumn(name = "CIR_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqCircunscripciones cirId;

  @JoinColumn(name = "JUD_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqJuzgadosDefensorias judId;

  @JoinColumn(name = "BAS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqBancosSucursales basId;

  @JoinColumn(name = "FOP_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqFormasPago fopId;

  @JoinColumn(name = "TCB_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqTipoCuentaBancaria tcbId;

  @JoinColumn(name = "TCE_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqTipoCalculoEmbargos tceId;

  public LiqEmbargos(Long id) {
    this.id = id;
  }

}
