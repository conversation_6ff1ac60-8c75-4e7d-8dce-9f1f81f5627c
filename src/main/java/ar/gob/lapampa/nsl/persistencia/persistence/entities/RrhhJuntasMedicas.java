package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_JUNTAS_MEDICAS")
@NamedQueries(value = {
    @NamedQuery(name = "RrhhJuntasMedicas.findAll", query = "SELECT r FROM RrhhJuntasMedicas r")})
public class RrhhJuntasMedicas implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_JM")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaJm;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "NRO_NOTA")
  private BigInteger nroNota;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "NRO_EXPEDIENTE")
  private BigInteger nroExpediente;
  @Size(max = 4000)
  @Column(name = "OBSERVACIONES")
  private String observaciones;
  @Column(name = "NRO_DICTAMEN")
  private BigInteger nroDictamen;
  @Size(max = 4000)
  @Column(name = "SINTESIS")
  private String sintesis;
  @Column(name = "LIC_FECHA_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date licFechaDesde;
  @Column(name = "LIC_FECHA_HASTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date licFechaHasta;
  @Column(name = "CDAD_DIAS_AUTORIZADOS")
  private Integer cdadDiasAutorizados;
  @Column(name = "LIC_CONTINUA")
  private Character licContinua;
  @Size(max = 5)
  @Column(name = "TIPO_TRATAMIENTO")
  private String tipoTratamiento;
  @Size(max = 10)
  @Column(name = "TIPO_ALTA")
  private String tipoAlta;
  @Column(name = "REQUIERE_NUEVA_EVALUACION")
  private Character requiereNuevaEvaluacion;

  public RrhhJuntasMedicas(Long id) {
    this.id = id;
  }

  public RrhhJuntasMedicas(Long id, Date fecha, Date fechaJm, BigInteger nroNota,
      BigInteger nroExpediente) {
    this.id = id;
    this.fecha = fecha;
    this.fechaJm = fechaJm;
    this.nroNota = nroNota;
    this.nroExpediente = nroExpediente;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhJuntasMedicas[ id=" + id + " ]";
  }

}
