package ar.gob.lapampa.nsl.persistencia.services;

import java.util.List;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.request.RrhhEmpresaRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.models.EmpresaModel;

@Service
public interface RrhhEmpresaService {

  GenericResponseDTO listar(RrhhEmpresaRequestDTO request);

  GenericResponseDTO listarConveniosPorEmpresa(RrhhEmpresaRequestDTO request);

  GenericResponseDTO actualizarEmpresasByRol(List<EmpresaModel> request, Long rolId);
}
