package ar.gob.lapampa.nsl.persistencia.dtos.requests;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import ar.gob.lapampa.nsl.persistencia.models.AccionModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SyncPermisosRequestDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 4023764303714935532L;
  private List<AccionModel> permisos;

}
