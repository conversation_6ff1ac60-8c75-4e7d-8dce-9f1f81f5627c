package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class BduPersonasMailsPK implements Serializable {

  private static final long serialVersionUID = -8852035520375339407L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRS_ID")
  private long prsId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "MAI_ID")
  private long maiId;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduPersonasMailsPK[ prsId=" + prsId
        + ", maiId=" + maiId + " ]";
  }

}
