package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_SUBRUBRO")
@NamedQueries(
    value = {@NamedQuery(name = "BduSubrubro.findAll", query = "SELECT b FROM BduSubrubro b")})
public class BduSubrubro implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 20)
  @Column(name = "CODIGO_SUBRUBRO")
  private String codigoSubrubro;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @JoinColumn(name = "RBR_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduRubro rbrId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "srbId", fetch = FetchType.LAZY)
  private Set<BduActividad> bduActividadSet;

  public BduSubrubro(Long id) {
    this.id = id;
  }

  public BduSubrubro(Long id, String codigoSubrubro, String descripcion, Character activo) {
    this.id = id;
    this.codigoSubrubro = codigoSubrubro;
    this.descripcion = descripcion;
    this.activo = activo;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduSubrubro[ id=" + id + " ]";
  }

}
