package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_DOCUMENTOS_TIPOS_VIEW")
public class BduDocumentosTiposNucleo implements Serializable {

  private static final long serialVersionUID = -3955044782492611471L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 20)
  @Column(name = "ABREVIATURA")
  private String abreviatura;

  public BduDocumentosTiposNucleo(Long id) {
this.id = id;
  }

}
