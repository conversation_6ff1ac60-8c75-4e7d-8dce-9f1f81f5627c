package ar.gob.lapampa.nsl.persistencia.dtos;

import ar.gob.lapampa.nsl.utils.common.EstadoWS;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class GenericResponseDTO {

  private EstadoWS resultado = EstadoWS.ESTADO_ERROR;
  private String mensaje = "Sin Datos";
  private Object objeto = null;

  public short getCodigoEstado() {
    return resultado.getCodigo();
  }

  /**
   * Establece el estado de respuesta en error con el mensaje pasado por parámetro
   *
   * @param mensaje
   */
  public void setEstadoError(String mensaje) {
    resultado = EstadoWS.ESTADO_ERROR;
    this.mensaje = mensaje;
  }

  /**
   * Establece el estado de respuesta en exito
   */

  public void setEstadoExito(Object objeto) {
    resultado = EstadoWS.ESTADO_EXITO;
    this.objeto = objeto;
    this.mensaje = EstadoWS.ESTADO_EXITO.getDescripcion();
  }

  /**
   * Establece el estado de respuesta en advertencia con el mensaje pasado por parámetro
   *
   * @param mensaje
   */
  public void setEstadoWarning(String mensaje) {
    resultado = EstadoWS.ESTADO_WARNING;
    this.mensaje = mensaje;
  }

}

