package ar.gob.lapampa.nsl.persistencia.services.impl;

import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.BaseDTO;
import ar.gob.lapampa.nsl.datatransfer.request.ListadoGenericoDTORequest;
import ar.gob.lapampa.nsl.datatransfer.request.ListadoPaginadoDTORequest;
import ar.gob.lapampa.nsl.excepciones.common.BaseDTONoUnicoException;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.services.GenericService;
import ar.gob.lapampa.nsl.utils.common.PaginationResult;
import jakarta.persistence.criteria.JoinType;

@Service
public class GenericServiceImpl implements GenericService {

  @Override
  public PaginationResult<BaseDTO> consultarPorFiltrosConPaginacion(BaseDTO filtro, int index,
      int offset) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public PaginationResult<BaseDTO> consultarPorFiltrosConPaginacionYMultiOrden(BaseDTO filtro,
      int index, int offset) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public Long contar(BaseDTO dto) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public Long contarOtros(BaseDTO dto, BaseDTO dtoPropio) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public Object createQuery(String query) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public List<BaseDTO> findAll(BaseDTO baseDTO) {
    // TODO Auto-generated method stub
    return List.of();
  }

  @Override
  public List<BaseDTO> findAllByExample(BaseDTO baseDTO) {
    // TODO Auto-generated method stub
    return List.of();
  }

  @Override
  public List<BaseDTO> findAllByExampleAndOrder(BaseDTO baseDTO, String campo, String orden) {
    // TODO Auto-generated method stub
    return List.of();
  }

  @Override
  public List<BaseDTO> findAllByOrder(BaseDTO baseDTO, String campo, String orden) {
    // TODO Auto-generated method stub
    return List.of();
  }

  @Override
  public <T> List<T> findByHQL(String hql) {
    // TODO Auto-generated method stub
    return List.of();
  }

  @Override
  public List<BaseDTO> findByNamedQuery(String name, Map<String, Object> paramsMap,
      BaseDTO baseDto) {
    // TODO Auto-generated method stub
    return List.of();
  }

  @Override
  public List<BaseDTO> findByNamedQuery(String name, Object[] params, BaseDTO baseDto) {
    // TODO Auto-generated method stub
    return List.of();
  }

  @Override
  public BaseDTO findOne(BaseDTO baseDTO) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public void persist(BaseDTO baseDTO) throws BaseDTONoUnicoException {
    // TODO Auto-generated method stub

  }

  @Override
  public BaseDTO persistAndReturn(BaseDTO baseDTO) throws BaseDTONoUnicoException {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public void remove(BaseDTO baseDTO) {
    // TODO Auto-generated method stub

  }

  @Override
  public void removeAll(List<BaseDTO> dtos) {
    // TODO Auto-generated method stub

  }

  @Override
  public GenericService setFieldsOrderBy(String fieldOrderBy) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public GenericService setIgnoreZeroes(boolean ignoreZeroes) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public GenericService setJoinType(JoinType joinType) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public GenericService setMatchLike(boolean matchLike) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public GenericService setOrderType(String orderType) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public void update(BaseDTO baseDTO) throws BaseDTONoUnicoException {
    // TODO Auto-generated method stub

  }

  @Override
  public void logException(Throwable t, Class<?> clazz) {
    // TODO Auto-generated method stub

  }

  @Override
  public GenericResponseDTO listarGenerico(ListadoGenericoDTORequest request) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public GenericResponseDTO listarGenericoPaginado(ListadoPaginadoDTORequest request) {
    // TODO Auto-generated method stub
    return null;
  }


}
