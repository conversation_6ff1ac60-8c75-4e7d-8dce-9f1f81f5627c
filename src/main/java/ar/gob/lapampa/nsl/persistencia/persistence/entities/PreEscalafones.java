package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.List;
import java.util.Set;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_ESCALAFONES")
@SequenceGenerator(name = "PRE_ESC_SEQ", sequenceName = "PRE_ESC_SEQ", allocationSize = 1)
public class PreEscalafones implements Serializable {

  private static final long serialVersionUID = 7973885678712076069L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PRE_ESC_SEQ")
  private Long id;

  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "NRO")
  private Long nro;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "AGREGAR_SIEMPRE")
  @Convert(converter = YesNoConverter.class)
  private Boolean agregarSiempre;

  @ManyToMany(mappedBy = "preEscalafonesSet", fetch = FetchType.LAZY)
  private Set<RrhhAntDisciplinariosTipos> rrhhAntDisciplinariosTiposSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "escId", fetch = FetchType.LAZY)
  private Set<PreEscalafonesDa> preEscalafonesDaSet;

  @OneToMany(mappedBy = "escId", fetch = FetchType.LAZY)
  private Set<RrhhTareas> rrhhTareasSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "escId", fetch = FetchType.LAZY)
  private Set<PreEscalafonesPlantasTipos> preEscalafonesPlantasTiposSet;

  @OneToMany(mappedBy = "escId", fetch = FetchType.LAZY)
  private Set<RrhhAntiguedadesReconocidas> rrhhAntiguedadesReconocidasSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "escId", fetch = FetchType.LAZY)
  private Set<LiqTotalesResumenProg> liqTotalesResumenProgSet;

  @OneToMany(mappedBy = "escId", fetch = FetchType.LAZY)
  private Set<RrhhSuplentes> rrhhSuplentesSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "escId", fetch = FetchType.LAZY)
  private Set<SiafCuentasEscalafon> siafCuentasEscalafonSet;

  @OneToMany(mappedBy = "escId", fetch = FetchType.LAZY)
  private Set<LiqProcPasosEsc> liqProcPasosEscSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "escId", fetch = FetchType.LAZY)
  private Set<RrhhFunciones> rrhhFuncionesSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "escId", fetch = FetchType.LAZY)
  private Set<LiqSetVersiones> liqSetVersionesSet;

  @OneToMany(mappedBy = "escId", fetch = FetchType.LAZY)
  private Set<LiqProcesosSetCodigos> liqProcesosSetCodigosSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "escId", fetch = FetchType.LAZY)
  private List<LiqCodigos> liqCodigosSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "escId", fetch = FetchType.LAZY)
  private Set<PreClases> preClasesSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "escId", fetch = FetchType.LAZY)
  private Set<RrhhOcupaciones> ocupaciones;

  @JoinTable(name = "RRHH_USUARIOS_ESCALAFONES",
      joinColumns = {@JoinColumn(name = "PRE_ESC_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "RRHH_USR_ID", referencedColumnName = "ID")})
  @ManyToMany(fetch = FetchType.LAZY)
  private List<RrhhUsuario> usuarios;

  @ManyToMany(fetch = FetchType.LAZY, mappedBy = "convenios")
  private List<RrhhEmpresa> empresas;

  public PreEscalafones(Long id) {

    this.id = id;
  }

  public PreEscalafones(Long id, Long nro, String descripcion) {

    this.id = id;
    this.nro = nro;
    this.descripcion = descripcion;
  }

  public PreEscalafones(Long id, Long nro, Boolean activo) {

    this.id = id;
    this.nro = nro;
    this.activo = activo;
  }

  public List<LiqCodigos> getLiqCodigosSet() {
    return liqCodigosSet;
  }

  public void setLiqCodigosSet(List<LiqCodigos> liqCodigosSet) {
    this.liqCodigosSet = liqCodigosSet;
  }
}
