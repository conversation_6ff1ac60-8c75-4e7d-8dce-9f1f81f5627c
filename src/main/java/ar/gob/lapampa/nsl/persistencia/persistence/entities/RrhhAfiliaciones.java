package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_AFILIACIONES")
public class RrhhAfiliaciones implements Serializable {

  private static final long serialVersionUID = 7381565844615372416L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Column(name = "FECHA_ALTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAlta;

  @Column(name = "FECHA_BAJA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaBaja;

  @Column(name = "NRO_TRAMITE")
  private Long nroTramite;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduPersonasFisicas prsId;

  @Column(name = "NRO_TRAMITE_BAJA")
  private Long nroTramiteBaja;

  @Basic(optional = false)
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;

  @Column(name = "PORCENTAJE")
  private BigDecimal porcentaje;

  @Column(name = "MONTO")
  private BigDecimal monto;

  @JoinColumn(name = "OCU_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhOcupaciones ocuId;

  @JoinColumn(name = "COD_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqCodigos codId;

  @JoinColumn(name = "LDI_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqDescuentosInformados ldiId;

  public RrhhAfiliaciones(Long id) {
    this.id = id;
  }

}
