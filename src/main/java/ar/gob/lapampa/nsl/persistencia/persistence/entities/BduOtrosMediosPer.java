package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_OTROS_MEDIOS_PER")
@NamedQueries(value = {
    @NamedQuery(name = "BduOtrosMediosPer.findAll", query = "SELECT b FROM BduOtrosMediosPer b")})
public class BduOtrosMediosPer implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 10)
  @Column(name = "VALOR")
  private String valor;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRS_ID")
  private long prsId;
  @JoinColumn(name = "OMT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduOtrosMediosTipos omtId;

  public BduOtrosMediosPer(Long id) {
    this.id = id;
  }

  public BduOtrosMediosPer(Long id, String valor, long prsId) {
    this.id = id;
    this.valor = valor;
    this.prsId = prsId;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduOtrosMediosPer[ id=" + id + " ]";
  }

}
