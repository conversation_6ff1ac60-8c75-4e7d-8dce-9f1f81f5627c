package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_ESCALAFONES_DA")
@NamedQueries(value = {
    @NamedQuery(name = "PreEscalafonesDa.findAll", query = "SELECT p FROM PreEscalafonesDa p")})
public class PreEscalafonesDa implements Serializable {
  private static final long serialVersionUID = 1L;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "DAD_ID")
  private long dadId;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @JoinColumn(name = "ESC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PreEscalafones escId;

  public PreEscalafonesDa(Long id) {
    this.id = id;
  }

  public PreEscalafonesDa(Long id, long dadId) {
    this.id = id;
    this.dadId = dadId;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.PreEscalafonesDa[ id=" + id + " ]";
  }

}
