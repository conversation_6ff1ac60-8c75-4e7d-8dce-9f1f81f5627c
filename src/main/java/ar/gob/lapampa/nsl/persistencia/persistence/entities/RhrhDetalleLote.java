package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_DETALLE_LOTE")
@SequenceGenerator(name = "RRHH_DETALLE_SEQ", sequenceName = "RRHH_DETALLE_SEQ", allocationSize = 1)

public class RhrhDetalleLote implements Serializable {

  private static final long serialVersionUID = -341360253480961096L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_DETALLE_SEQ")
  private Long id;


  @JoinColumn(name = "EMP_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private RrhhEmpresa empId;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private BduPersonasFisicas prsId;

  @JoinColumn(name = "CAB_SUC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.EAGER)
  private LiqCabeceraBancoSucursal cabSucId;

  @Column(name = "DOMICILIO")
  private String domicilio;

  @Column(name = "CONTACTO")
  private String contacto;

  @Column(name = "LOCALIDAD")
  private String localidad;


  @JoinColumn(name = "PROVINCIA", referencedColumnName = "ID")
  @OneToOne(fetch = FetchType.EAGER)
  private BduProvincias provincia;

  @Column(name = "COD_POSTAL")
  private String codPostal;

  @Column(name = "ESTADO")
  private Boolean camposCompletos;

  @Column(name = "NRO_LOTE")
  private Integer nroLote;

  @Temporal(TemporalType.TIMESTAMP)
  @Column(name = "F_ENVIO")
  private Date fechaEnvio;

  @Temporal(TemporalType.TIMESTAMP)
  @Column(name = "F_RECEPCION")
  private Date fechaRecepcion;

  @Column(name = "OBSERVACION")
  private String observacion;

}
