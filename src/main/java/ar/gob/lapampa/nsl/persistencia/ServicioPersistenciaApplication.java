package ar.gob.lapampa.nsl.persistencia;

import java.time.Duration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.web.client.RestTemplate;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;

@SpringBootApplication
@OpenAPIDefinition(info = @Info(title = "Microservicio Persistencia REST OpenAPI Documentation",
    description = "Spring Boot REST OpenAPI Documentation", version = "v1.0",
    contact = @Contact(name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>",
        url = "https://www.lapampa.gob.ar/"),
    license = @License(name = "Gobierno de La Pampa", url = "https://www.lapampa.gob.ar/")))
public class ServicioPersistenciaApplication {

  public static void main(String[] args) {
    SpringApplication.run(ServicioPersistenciaApplication.class, args);
  }

  /**
   * Rest template.
   *
   * @param builder the builder
   * @return the rest template
   */
  @Bean
  @LoadBalanced
  RestTemplate restTemplate(RestTemplateBuilder builder) {
    return builder.readTimeout(Duration.ofSeconds(60)).connectTimeout(Duration.ofSeconds(60))
        .build();
  }
}
