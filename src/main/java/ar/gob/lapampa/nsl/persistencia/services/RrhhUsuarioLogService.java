package ar.gob.lapampa.nsl.persistencia.services;

import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.RrhhUsuarioLogDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.UsuarioLogRequestDTO;

@Service
public interface RrhhUsuarioLogService {

  GenericResponseDTO listar(UsuarioLogRequestDTO request);

  GenericResponseDTO registrar(RrhhUsuarioLogDTO rrhhUsuariosDTO);

  GenericResponseDTO modificar(RrhhUsuarioLogDTO rrhhUsuariosDTO, long id);

  GenericResponseDTO eliminar(Long id);

}
