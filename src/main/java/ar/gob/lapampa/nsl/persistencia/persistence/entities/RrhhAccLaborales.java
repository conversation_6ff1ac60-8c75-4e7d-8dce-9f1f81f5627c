package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_ACC_LABORALES")
@NamedQueries(value = {
    @NamedQuery(name = "RrhhAccLaborales.findAll", query = "SELECT r FROM RrhhAccLaborales r")})
public class RrhhAccLaborales implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRS_ID")
  private long prsId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "TIPO")
  private short tipo;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_ACCIDENTE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAccidente;
  @Column(name = "HORA_ACCIDENTE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date horaAccidente;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 4000)
  @Column(name = "LESION")
  private String lesion;
  @Size(max = 4000)
  @Column(name = "LUGAR_LESION")
  private String lugarLesion;
  @Size(max = 4000)
  @Column(name = "LUGAR_ATENCION")
  private String lugarAtencion;
  @Size(max = 4000)
  @Column(name = "SECUELAS")
  private String secuelas;
  @Size(max = 4000)
  @Column(name = "OBSERVACIONES")
  private String observaciones;
  @JoinColumn(name = "ALE_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhAccLaboralesEstados aleId;
  @JoinColumn(name = "ALT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhAccLabTipos altId;

  public RrhhAccLaborales(Long id) {
    this.id = id;
  }

  public RrhhAccLaborales(Long id, long prsId, short tipo, Date fecha, Date fechaAccidente,
      String lesion) {
    this.id = id;
    this.prsId = prsId;
    this.tipo = tipo;
    this.fecha = fecha;
    this.fechaAccidente = fechaAccidente;
    this.lesion = lesion;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhAccLaborales[ id=" + id + " ]";
  }

}
