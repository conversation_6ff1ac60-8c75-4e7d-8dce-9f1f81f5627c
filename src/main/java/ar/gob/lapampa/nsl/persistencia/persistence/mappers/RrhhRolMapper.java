package ar.gob.lapampa.nsl.persistencia.persistence.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import ar.gob.lapampa.nsl.persistencia.dtos.RrhhRolDTO;
import ar.gob.lapampa.nsl.persistencia.models.RolFullModel;
import ar.gob.lapampa.nsl.persistencia.models.RolModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhRol;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RrhhRolMapper {

  RrhhRolMapper INSTANCE = Mappers.getMapper(RrhhRolMapper.class);

  RrhhRolDTO rrhhRolToRrhhRolDto(RrhhRol rol);

  static RolModel rrhhRolToRrhhRolModel(RrhhRol rol) {
    RolModel model = new RolModel(rol.getId());
    model.setNombre(rol.getNombre());
    model.setDescripcion(rol.getDescripcion());
    return model;
  }

  static RolFullModel rrhhRolToRrhhRolFullModel(RrhhRol rol) {
    RolFullModel model = new RolFullModel(rol.getId());
    model.setNombre(rol.getNombre());
    model.setDescripcion(rol.getDescripcion());
    model.setEmpresas(rol.getEmpresas().stream().map(RrhhEmpresaMapper::rrhhEmpresaToEmpresaModel).toList());
    model.setMenues(rol.getMenues().stream().map(RrhhMenuMapper::rrhhMenuToRrhhMenuModel).toList());
    model.setAcciones(rol.getAcciones().stream().map(RrhhAccionMapper::rrhhAccionToRrhhAccionModel).toList());
    model.setUsuarios(rol.getUsuarios().stream().map(RrhhUsuarioMapper::rrhhUsuarioToRrhhUsuarioModel).toList());
    return model;
  }

}
