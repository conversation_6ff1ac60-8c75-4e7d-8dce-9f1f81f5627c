package ar.gob.lapampa.nsl.persistencia.aop;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;
import javax.sql.DataSource;
import org.springframework.jdbc.datasource.DelegatingDataSource;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DataSourceWrapper extends DelegatingDataSource {

  private static final Integer METRIC_MAX_SIZE = 50;
  private String module = "";

  public DataSourceWrapper(DataSource dataSource) {
    super(dataSource);
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null) {
      @SuppressWarnings("unchecked")
      Map<String, Object> info = (Map<String, Object>) authentication.getDetails();
      String serviceNames = (String) info.get("ServiceNames");
      this.module = serviceNames.substring(1, serviceNames.length() - 1);
    }
  }

  @Override
  public Connection getConnection() throws SQLException {
    Connection connection = super.getConnection();

    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null) {
      String currentPrincipalName = authentication.getName();
      try {
        @SuppressWarnings("unchecked")
        Map<String, Object> info = (Map<String, Object>) authentication.getDetails();
        String serviceNames = (String) info.get("ServiceNames");
        if (!"Sesion no encontrada".equals(serviceNames)) {
          this.module = serviceNames.substring(1, serviceNames.length() - 1);
        } else {
          this.module = serviceNames;
        }
      } catch (Exception e) {
        // log.error("No se pudo mapear User Details {}", e.getMessage())
        this.module = "Admin Check";
      }
      connection.setClientInfo("OCSID.CLIENTID", currentPrincipalName);

      if (module.length() > METRIC_MAX_SIZE) {
        connection.setClientInfo("OCSID.MODULE",
            module.substring(module.length() - METRIC_MAX_SIZE));
      } else {
        connection.setClientInfo("OCSID.MODULE", module);
      }

      log.info("Current User: {}", currentPrincipalName);
      log.info("Current App: {}", module);
    }

    return connection;
  }
}
