package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_VINCULOS")
@SequenceGenerator(name = "BDU_VNC_SEQ", sequenceName = "BDU_VNC_SEQ", allocationSize = 1)
public class BduVinculos implements Serializable {

  private static final long serialVersionUID = -2610793406363129239L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BDU_VNC_SEQ")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @JoinColumn(name = "TVN_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduVinculosTipos tvnId;

  @Column(name = "FECHA_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDesde;

  @Column(name = "FECHA_HASTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaHasta;

  @Column(name = "RESPONSABLE")
  private Character responsable;

  @Basic(optional = false)
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Column(name = "COMPORTAMIENTO")
  private Long comportamiento;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ODT_ID")
  private long odtId;

  @Column(name = "NRO_ACTA")
  private Long nroActa;

  @Column(name = "PRS_ID_ORIGEN_BASE")
  private Long prsIdOrigenBase;

  @Column(name = "PRS_ID_DESTINO_BASE")
  private Long prsIdDestinoBase;

  @JoinColumn(name = "PRS_ORIGEN_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduPersonas prsOrigenId;

  @JoinColumn(name = "PRS_DESTINO_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private BduPersonas prsDestinoId;

  @OneToMany(mappedBy = "vncId", fetch = FetchType.LAZY)
  private List<RrhhConceptosFliares> conceptosFliaresList;

  @JoinTable(name = "LIQ_CUOTAS_ALIM_VINCULOS",
      joinColumns = {@JoinColumn(name = "VNC_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "CUA_ID", referencedColumnName = "ID")})
  @ManyToMany(fetch = FetchType.LAZY)
  private List<LiqCuotasAlimentarias> liqCuotasAlimList;

  public BduVinculos(Long id) {

    this.id = id;
  }

}
