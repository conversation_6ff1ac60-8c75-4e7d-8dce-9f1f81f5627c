package ar.gob.lapampa.nsl.persistencia.controllers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.GananciaRequestDTO;
import ar.gob.lapampa.nsl.persistencia.services.GananciaService;
import jakarta.validation.Valid;

/**
 * RRHHUsuario Controller
 * 
 * <AUTHOR>
 *
 */
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/v1/app/ganancias")
public class GananciaController {

  @Autowired
  private GananciaService gananciaService;

  @PostMapping("/listar")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','GAN_R','','IS')")
  public @ResponseBody GenericResponseDTO listarVariables(
      @Valid @RequestBody GananciaRequestDTO request) {

    return gananciaService.listar(request);
  }

  @PostMapping("/listarPasos")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','GAN_R','','IS')")
  public @ResponseBody GenericResponseDTO registrar(
      @Valid @RequestBody GananciaRequestDTO rrhhUsuariosDTO) {

    return gananciaService.listarPasos(rrhhUsuariosDTO);
  }
}
