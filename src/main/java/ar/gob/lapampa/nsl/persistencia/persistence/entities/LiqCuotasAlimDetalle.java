package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_CUOTAS_ALIM_DETALLE")
public class LiqCuotasAlimDetalle implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "MONTO_DESCONTADO")
  private Double montoDescontado;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ITEM", insertable = false, updatable = false)
  private short item;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "MES_LIQ")
  private short mesLiq;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANIO_LIQ")
  private short anioLiq;

  @JoinColumn(name = "CUA_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqCuotasAlimentarias cuaId;

  @JoinColumn(name = "FOC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqFirmasOcupaciones focId;

}
