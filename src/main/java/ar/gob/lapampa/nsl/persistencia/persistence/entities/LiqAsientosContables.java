package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_ASIENTOS_CONTABLES")
public class LiqAsientosContables implements Serializable {

  private static final long serialVersionUID = -3935204738314973421L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Column(name = "MES_LIQ")
  private Short mesLiquidacion;

  @Column(name = "ANIO_LIQ")
  private Short anioLiquidacion;

  @Column(name = "LIQ_ID")
  private Long idProceso;

  @Column(name = "FECHA_PAGO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaPago;

  @Column(name = "FECHA_PROC")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaProceso;

  @JoinColumn(name = "LACT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqAsientosContablesTipos lactId;

  @Column(name = "FINALIDAD_FUNCION")
  private String finalidadFuncion;

  @Column(name = "NRO_LIQ")
  private String nroLiquidacion;

  @Column(name = "TOTAL_NETO")
  private BigDecimal totalNeto;

  @Column(name = "ESTADO")
  private Character estado;

}
