package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_JORNADAS_TIPOS")
public class RrhhJornadasTipos implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ATIPICO")
  @Convert(converter = YesNoConverter.class)
  private Boolean atipico;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ROTATIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean rotativo;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "DIURNO")
  @Convert(converter = YesNoConverter.class)
  private Boolean diurno;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "NOCTURNO")
  @Convert(converter = YesNoConverter.class)
  private Boolean nocturno;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANIO_COMPLETO")
  @Convert(converter = YesNoConverter.class)
  private Boolean anioCompleto;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "GUARDIAS")
  @Convert(converter = YesNoConverter.class)
  private Boolean guardias;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "CDAD_HORAS_SEMANALES")
  private Integer cdadHorasSemanales;

  @Column(name = "NRO")
  private Long nro;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "rrhhJornadasTipos", fetch = FetchType.LAZY)
  private Set<RrhhJornadasTiposDias> rrhhJornadasTiposDiasSet;

  @OneToMany(mappedBy = "jotId", fetch = FetchType.LAZY)
  private Set<RrhhOcupacionesMov> rrhhOcupacionesMovSet;

}
