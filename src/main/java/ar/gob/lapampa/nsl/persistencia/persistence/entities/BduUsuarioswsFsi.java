package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_USUARIOSWS_FSI")
@NamedQueries(value = {
    @NamedQuery(name = "BduUsuarioswsFsi.findAll", query = "SELECT b FROM BduUsuarioswsFsi b")})
public class BduUsuarioswsFsi implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_INICIO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaInicio;
  @Column(name = "FECHA_FIN")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaFin;
  @JoinColumn(name = "FSI_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduFuncionesSis fsiId;
  @JoinColumn(name = "USUARIOSWS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduUsuariosws usuarioswsId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "usuariowsFsiId", fetch = FetchType.LAZY)
  private Set<BduSolicitudws> bduSolicitudwsSet;

  public BduUsuarioswsFsi(Long id) {
    this.id = id;
  }

  public BduUsuarioswsFsi(Long id, Date fechaInicio) {
    this.id = id;
    this.fechaInicio = fechaInicio;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduUsuarioswsFsi[ id=" + id + " ]";
  }

}
