package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ORGANISMOS_PRE")
public class SiafProgramas implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Column(name = "DESCRIPCION", nullable = false)
  private String descripcion;

  @Column(name = "CODIGO")
  private Long codigo;

  public SiafProgramas(Long id) {

    this.id = id;
  }

  public SiafProgramas(Long id, String descripcion) {

    this.id = id;
    this.descripcion = descripcion;
  }

}
