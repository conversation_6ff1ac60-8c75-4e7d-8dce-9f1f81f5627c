package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_REGLAS")
@NamedQueries(
    value = {@NamedQuery(name = "BduReglas.findAll", query = "SELECT b FROM BduReglas b")})
public class BduReglas implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "FIRMA")
  private String firma;
  @Column(name = "COMPORTAMIENTO")
  private Long comportamiento;
  @Size(max = 200)
  @Column(name = "PARAMETROS")
  private String parametros;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "rglId", fetch = FetchType.LAZY)
  private Set<BduReglasIds> bduReglasIdsSet;
  @JoinColumn(name = "TRG_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduReglasTipos trgId;

  public BduReglas(Long id) {
    this.id = id;
  }

  public BduReglas(Long id, String firma, Character activo) {
    this.id = id;
    this.firma = firma;
    this.activo = activo;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduReglas[ id=" + id + " ]";
  }

}
