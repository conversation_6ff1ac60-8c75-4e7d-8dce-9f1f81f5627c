package ar.gob.lapampa.nsl.persistencia.services.impl;

import static org.apache.commons.lang.WordUtils.capitalizeFully;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import com.querydsl.core.BooleanBuilder;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.RegistrarRolesAUsuarioRequestDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.RolRequestDTO;
import ar.gob.lapampa.nsl.persistencia.models.RolFullModel;
import ar.gob.lapampa.nsl.persistencia.models.RolModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QRrhhRol;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QRrhhUsuario;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhAcciones;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhRol;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhUsuario;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.RrhhRolMapper;
import ar.gob.lapampa.nsl.persistencia.repositories.RrhhRolRepository;
import ar.gob.lapampa.nsl.persistencia.repositories.RrhhUsuarioRepository;
import ar.gob.lapampa.nsl.persistencia.services.RrhhRolService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class RrhhRolServiceImpl implements RrhhRolService {

  @Autowired
  private RrhhUsuarioRepository rrhhUsuarioRepository;

  @Autowired
  private RrhhRolRepository rrhhRolRepository;

  Sort sort = Sort.unsorted();

  @Override
  public GenericResponseDTO registrarRolesAUsuario(RegistrarRolesAUsuarioRequestDTO request) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public GenericResponseDTO listarRolesDisponibles(RolRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();

    BooleanBuilder booleanBuilder = new BooleanBuilder();

    // Add filter for id if provided
    if (request.getId() != null && !(request.getId() == 0)) {
      booleanBuilder.and(QRrhhRol.rrhhRol.id.eq(request.getId()));
    }

    // Add filter for nombre if provided
    if (request.getNombre() != null && !request.getNombre().isEmpty()) {
      booleanBuilder.and(QRrhhRol.rrhhRol.nombre.containsIgnoreCase(request.getNombre()));
    }

    // Add filter for descripcion if provided
    if (request.getDescripcion() != null && !request.getDescripcion().isEmpty()) {
      booleanBuilder.and(QRrhhRol.rrhhRol.descripcion.containsIgnoreCase(request.getDescripcion()));
    }

    // Set sorting
    if (request.getSortBy() != null && !request.getSortBy().isEmpty()) {
      if (Boolean.TRUE.equals(request.getAsc())) {
        sort = Sort.by(Sort.Direction.ASC, request.getSortBy());
      } else {
        sort = Sort.by(Sort.Direction.DESC, request.getSortBy());
      }
    } else {
      sort = Sort.unsorted();
    }

    Page<RrhhRol> page;
    if (booleanBuilder.getValue() != null) {
      page = rrhhRolRepository.findAll(booleanBuilder.getValue(),
          PageRequest.of(request.getPage(), request.getSize(), sort));
    } else {
      page = rrhhRolRepository.findAll(PageRequest.of(request.getPage(), request.getSize(), sort));
    }

    Page<RolModel> pageDto = page.map(RrhhRolMapper::rrhhRolToRrhhRolModel);
    response.setEstadoExito(pageDto);
    return response;
  }

  @Override
  public GenericResponseDTO listarRolesPorLogin(String login) {
    final GenericResponseDTO response = new GenericResponseDTO();
    // Obtener user Id por login
    Optional<RrhhUsuario> user =
        rrhhUsuarioRepository.findOne(QRrhhUsuario.rrhhUsuario.login.eq(login));
    if (user.isPresent()) {
      // Obtener roles del usuario
      List<RrhhRol> roles = user.get().getRoles();
      // Mapear los roles a modelos para la respuesta
      List<RolModel> rolesModels = roles.stream().map(rol -> {
        RolModel rolModel = RrhhRolMapper.rrhhRolToRrhhRolModel(rol);
        if (rolModel.getDescripcion() != null) {
          rolModel.setDescripcion(capitalizeFully(rolModel.getDescripcion()));
        }
        return rolModel;
      }).sorted(Comparator.comparing(RolModel::getNombre)).collect(Collectors.toList());

      // Establecer la respuesta exitosa con la lista de roles
      response.setEstadoExito(rolesModels);
    } else {
      response.setEstadoError("Usuario no encontrado");
    }
    return response;
  }

  @Override
  public GenericResponseDTO crearRol(RolRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();

    try {
      // Normalizar el nombre para almacenamiento interno (prefijo + mayúsculas + guiones bajos)
      // Ejemplo: "Gestionar Usuarios" -> "NNSL_GESTIONAR_USUARIOS"
      String nombreInternoRol = request.getNombre().toUpperCase().replaceAll("[^A-Z0-9_]", ""); // Eliminar
                                                                                                // caracteres
                                                                                                // no
                                                                                                // válidos

      Optional<RrhhRol> existente =
          rrhhRolRepository.findOne(QRrhhRol.rrhhRol.nombre.eq(nombreInternoRol));
      if (existente.isPresent()) {
        response.setEstadoError("Ya existe un rol con el nombre interno: " + nombreInternoRol
            + " (derivado de '" + request.getNombre() + "')");
        log.warn("Intento de crear rol duplicado: {}", nombreInternoRol);
        return response;
      }

      RrhhRol nuevoRol = new RrhhRol();
      nuevoRol.setNombre(nombreInternoRol);
      nuevoRol.setDescripcion(request.getDescripcion()); // Puede ser null o vacío

      RrhhRol rolGuardado = rrhhRolRepository.save(nuevoRol);

      // Mapear a RolModel para la respuesta, similar a como se hace en listarRolesDisponibles
      RolModel rolModel = RrhhRolMapper.rrhhRolToRrhhRolModel(rolGuardado);

      response.setEstadoExito(rolModel);
      response.setMensaje("Rol '" + rolGuardado.getNombre() + "' creado exitosamente.");
      log.info("Rol creado: ID={}, NombreInterno={}", rolGuardado.getId(), rolGuardado.getNombre());

    } catch (Exception e) {
      log.error("Error al crear el rol para la solicitud: {}", request.getNombre(), e);
      response.setEstadoError("Error interno al crear el rol: " + e.getMessage());
    }
    return response;
  }

  @Override
  public GenericResponseDTO borrarRol(Long rolId) {
    final GenericResponseDTO response = new GenericResponseDTO();
    Optional<RrhhRol> rolOptional = rrhhRolRepository.findById(rolId);

    if (rolOptional.isEmpty()) {
      response.setEstadoError("Rol con ID " + rolId + " no encontrado.");
      log.warn("Intento de borrar rol no existente: ID={}", rolId);
      return response;
    }

    RrhhRol rol = rolOptional.get();
    final List<String> dependenciasActivas = verificarDependenciasRol(rol);

    if (!dependenciasActivas.isEmpty()) {
      String mensajeError = "El rol '" + rol.getNombre() + "' (ID: " + rolId
          + ") no puede ser eliminado porque tiene: " + String.join(", ", dependenciasActivas)
          + ". Por favor, desasócielo primero.";
      response.setEstadoError(mensajeError);
      log.warn("Intento de borrar rol con dependencias: ID={}, Nombre='{}', Dependencias: {}",
          rolId, rol.getNombre(), dependenciasActivas);
      return response;
    }

    // Si no hay dependencias, proceder con el borrado
    try {
      rrhhRolRepository.deleteById(rolId);
      response.setEstadoExito(rolId);
      response.setMensaje("Rol eliminado exitosamente.");
      log.info("Rol eliminado: ID={}", rolId);
    } catch (Exception e) {
      log.error("Error al eliminar el rol con ID: {}", rolId, e);
      response.setEstadoError("Error interno al eliminar el rol: " + e.getMessage());
    }
    return response;
  }

  @Override
  public GenericResponseDTO modificarRol(RolRequestDTO request, Long rolId) {
    final GenericResponseDTO response = new GenericResponseDTO();
    try {
      // Verificar si el rol existe
      Optional<RrhhRol> existente = rrhhRolRepository.findById(rolId);
      if (existente.isPresent()) {

        // Normalizar el nombre para almacenamiento interno
        String nombreInternoRol = request.getNombre().toUpperCase().replaceAll("[^A-Z0-9_]", ""); // Eliminar
                                                                                                  // caracteres
                                                                                                  // no
                                                                                                  // válidos
        RrhhRol rol = existente.get();
        rol.setNombre(nombreInternoRol);
        rol.setDescripcion(request.getDescripcion()); // Puede ser null o vacío
        RrhhRol rolGuardado = rrhhRolRepository.save(rol);

        // Mapear a RolModel para la respuesta, similar a como se hace en listarRolesDisponibles
        RolModel rolModel = RrhhRolMapper.rrhhRolToRrhhRolModel(rolGuardado);

        response.setEstadoExito(rolModel);
        response.setMensaje("Rol '" + rolGuardado.getNombre() + "' actualizado exitosamente.");
        log.info("Rol actualizado: ID={}, NombreInterno={}", rolGuardado.getId(),
            rolGuardado.getNombre());

      } else {
        response.setEstadoError("Rol con ID " + rolId + " no encontrado.");
        log.warn("Intento de modificar rol no existente: ID={}", rolId);
        return response;
      }

    } catch (Exception e) {
      log.error("Error al actualizar el rol para la solicitud: {}", request.getNombre(), e);
      response.setEstadoError("Error interno al actualizar el rol: " + e.getMessage());
    }
    return response;
  }

  private static List<String> verificarDependenciasRol(RrhhRol rol) {
    List<String> dependenciasActivas = new ArrayList<>();

    // Verificar dependencias
    // Gracias a FetchType.LAZY, estas llamadas .isEmpty()
    // resultan en queries eficientes (SELECT COUNT(*)) en lugar de cargar toda la colección.
    if (rol.getUsuarios() != null && !rol.getUsuarios().isEmpty()) {
      dependenciasActivas.add("usuarios asignados (" + rol.getUsuarios().size() + ")");
    }
    if (rol.getMenues() != null && !rol.getMenues().isEmpty()) {
      dependenciasActivas.add("menús asociados (" + rol.getMenues().size() + ")");
    }
    if (rol.getAcciones() != null && !rol.getAcciones().isEmpty()) {
      dependenciasActivas.add("acciones vinculadas (" + rol.getAcciones().size() + ")");
    }
    if (rol.getEmpresas() != null && !rol.getEmpresas().isEmpty()) {
      dependenciasActivas.add("empresas relacionadas (" + rol.getEmpresas().size() + ")");
    }
    return dependenciasActivas;
  }

  private static List<String> verificarDependenciasAccion(RrhhAcciones accion) {
    List<String> dependenciasActivas = new ArrayList<>();

    // Verificar dependencias
    if (accion.getRoles() != null && !accion.getRoles().isEmpty()) {
      dependenciasActivas.add("roles asignados (" + accion.getRoles().size() + ")");
    }

    return dependenciasActivas;
  }

  @Override
  public GenericResponseDTO rolPorId(Long id) {
    final GenericResponseDTO response = new GenericResponseDTO();
    Optional<RrhhRol> role = rrhhRolRepository.findById(id);
    if (role.isPresent()) {
      RolFullModel rolFullModel = RrhhRolMapper.rrhhRolToRrhhRolFullModel(role.get());
      response.setEstadoExito(rolFullModel);
    } else {
      response.setEstadoError("Rol no encontrado");
    }
    return response;
  }

}
