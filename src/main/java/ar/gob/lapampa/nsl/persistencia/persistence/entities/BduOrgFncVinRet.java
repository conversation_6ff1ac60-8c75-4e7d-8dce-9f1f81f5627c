package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ORG_FNC_VIN_RET")
@NamedQueries(value = {
    @NamedQuery(name = "BduOrgFncVinRet.findAll", query = "SELECT b FROM BduOrgFncVinRet b")})
public class BduOrgFncVinRet implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected BduOrgFncVinRetPK bduOrgFncVinRetPK;
  @Size(max = 1000)
  @Column(name = "USUARIO_MOV")
  private String usuarioMov;
  @Column(name = "FECHA_MOV")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaMov;
  @JoinColumn(name = "RET_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduRelacionesTipos bduRelacionesTipos;

  public BduOrgFncVinRet(BduOrgFncVinRetPK bduOrgFncVinRetPK) {
    this.bduOrgFncVinRetPK = bduOrgFncVinRetPK;
  }

  public BduOrgFncVinRet(long orfIdOrigen, long orfIdDestino, long retId) {
    this.bduOrgFncVinRetPK = new BduOrgFncVinRetPK(orfIdOrigen, orfIdDestino, retId);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduOrgFncVinRet[ bduOrgFncVinRetPK="
        + bduOrgFncVinRetPK + " ]";
  }

}
