package ar.gob.lapampa.nsl.persistencia.persistence.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import ar.gob.lapampa.nsl.persistencia.dtos.RrhhMenuDTO;
import ar.gob.lapampa.nsl.persistencia.models.MenuModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhMenu;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RrhhMenuMapper {

  RrhhMenuMapper INSTANCE = Mappers.getMapper(RrhhMenuMapper.class);

  RrhhMenuDTO rrhhMenuToRrhhMenuDto(RrhhMenu menu);

  static MenuModel rrhhMenuToRrhhMenuModel(RrhhMenu menu) {
    MenuModel model = new MenuModel(menu.getId());
    model.setNombre(menu.getNombre());
    model.setEstado(menu.getEstado());
    model.setIcono(menu.getIcono());
    model.setAbstracto(menu.getAbstracto());
    if (menu.getMenuPadreId() != null) {
     MenuModel menuPadre = new MenuModel();
     menuPadre.setId(menu.getMenuPadreId().getId());
     menuPadre.setNombre(menu.getMenuPadreId().getNombre());
     menuPadre.setEstado(menu.getMenuPadreId().getEstado());
     menuPadre.setIcono(menu.getMenuPadreId().getIcono());
     menuPadre.setAbstracto(menu.getMenuPadreId().getAbstracto());
     model.setMenuPadreId(menuPadre);
    }
    return model;
  }

}
