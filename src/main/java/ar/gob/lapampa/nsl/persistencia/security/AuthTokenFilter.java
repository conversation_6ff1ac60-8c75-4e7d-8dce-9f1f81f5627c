package ar.gob.lapampa.nsl.persistencia.security;

import java.io.IOException;
import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.http.auth.AuthenticationException;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class AuthTokenFilter extends OncePerRequestFilter {

  private final CustomUserDetailsService userDetailsService;

  public AuthTokenFilter(CustomUserDetailsService userDetailsService) {
    super();
    this.userDetailsService = userDetailsService;
  }

  @Override
  protected void doFilterInternal(final HttpServletRequest request,
      final HttpServletResponse response, FilterChain filterChain)
      throws ServletException, IOException {
    try {
      final String token = parseToken(request);
      if (token != null) {

        final Boolean validToken = userDetailsService.validateToken(token);
        if (Boolean.TRUE.equals(validToken)) {

          String serviceNames = userDetailsService.serviceNamesByToken(token);

          final UserDetails userDetails = userDetailsService.loadUserByUsername(token);
          final UsernamePasswordAuthenticationToken authentication =
              new UsernamePasswordAuthenticationToken(userDetails, null,
                  userDetails.getAuthorities());
          authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
          log.info("{}", authentication.getDetails());
          SecurityContextHolder.getContext().setAuthentication(authentication);
          SecurityContext sec = SecurityContextHolder.getContext();
          AbstractAuthenticationToken auth = (AbstractAuthenticationToken) sec.getAuthentication();
          log.info("{}", auth.getDetails());
          String webAuthDetails = auth.getDetails().toString();
          String details = webAuthDetails.substring(26, webAuthDetails.length() - 1);
          Map<String, String> map = Arrays.stream(details.split(",")).map(s -> s.split("="))
              .collect(Collectors.toMap(s -> s[0], s -> s[1]));
          map.put("ServiceNames", serviceNames);
          map.put("token", token);
          auth.setDetails(map);
          log.info("{}", auth.getDetails());

        } else {
          throw new AuthenticationException("Token Expirado.");
        }
      }
    } catch (Exception e) {
      log.error("No se pudo autenticar el usuario: {}", e.getMessage());
    }

    filterChain.doFilter(request, response);
  }

  private String parseToken(final HttpServletRequest request) {
    String headerAuth = request.getHeader("Authorization");

    if (StringUtils.hasText(headerAuth) && headerAuth.startsWith("Bearer ")) {
      headerAuth = headerAuth.substring(7);
    }

    return headerAuth;
  }
}
