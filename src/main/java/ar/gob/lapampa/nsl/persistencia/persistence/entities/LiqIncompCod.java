package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_INCOMP_COD")
@SequenceGenerator(name = "LIQ_ICD_SEQ", sequenceName = "LIQ_ICD_SEQ", allocationSize = 1)
public class LiqIncompCod implements Serializable {
  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_ICD_SEQ")
  private Long id;

  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;

  @Column(name = "COMPORTAMIENTO")
  private Long comportamiento;

  @Column(name = "VALOR")
  private BigInteger valor;

  @Size(max = 2500)
  @Column(name = "COMENTARIO")
  private String comentario;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "icdId", fetch = FetchType.LAZY)
  private List<LiqCodVerIncomp> liqCodVerIncompSet;

  @OneToMany(mappedBy = "icdId", fetch = FetchType.LAZY)
  private Set<LiqIncompCodPersona> liqIncompCodPersonaSet;

  public LiqIncompCod(Long id) {
    this.id = id;
  }

}
