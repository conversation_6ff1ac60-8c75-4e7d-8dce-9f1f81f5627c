package ar.gob.lapampa.nsl.persistencia.security;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Lazy
public class CustomUserDetailsService implements UserDetailsService {

  @Value("${spring.application.name}")
  private String appName;

  @Value("${project.version}")
  private String version;

  private final RestTemplate template;

  public CustomUserDetailsService(RestTemplate template) {
    super();
    this.template = template;
  }

  public Boolean validateToken(String token) {
    JwtRequestDTO jwtRequestDTO = new JwtRequestDTO(token);
    return template.postForObject("http://SERVICIO-SEGURIDAD//oauth//gam//validarToken",
        jwtRequestDTO, Boolean.class);
  }

  @Override
  public UserDetails loadUserByUsername(String token) throws UsernameNotFoundException {
    JwtRequestDTO jwtRequestDTO = new JwtRequestDTO(token);
    log.info("Obtener userDetails...");
    String userDetails =
        template.postForObject("http://SERVICIO-SEGURIDAD//api//v2//app//credenciales//userDetails",
            jwtRequestDTO, String.class);
    return UserDetailsImpl.build(userDetails);
  }

  public String serviceNamesByToken(String token) {
    JwtRequestDTO jwtRequestDTO = new JwtRequestDTO(token);
    jwtRequestDTO.setServiceName(removePrefix(appName) + "_" + version);

    return template.postForObject(
        "http://SERVICIO-SEGURIDAD//api//v2//app//credenciales//serviceNameByToken", jwtRequestDTO,
        String.class);
  }

  private String removePrefix(String name) {
    if ("servicio-".equals(name.substring(0, 9))) {
      return name.substring(9);
    } else {
      return name;
    }
  }

}
