package ar.gob.lapampa.nsl.persistencia.services.impl;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import com.querydsl.core.BooleanBuilder;
import ar.gob.lapampa.nsl.datatransfer.LiqLiquidacionesTiposDTO;
import ar.gob.lapampa.nsl.datatransfer.request.LiqProcesoRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.models.ProcesoModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.LiqProcesos;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.QLiqProcesos;
import ar.gob.lapampa.nsl.persistencia.persistence.mappers.LiqProcesoMapper;
import ar.gob.lapampa.nsl.persistencia.repositories.LiqProcesoRepository;
import ar.gob.lapampa.nsl.persistencia.services.LiqProcesoService;

@Service
public class LiqProcesoServiceImpl implements LiqProcesoService {

  @Autowired
  private LiqProcesoRepository liqProcesoRepository;

  Sort sort = Sort.unsorted();

  @Override
  public GenericResponseDTO listar(LiqProcesoRequestDTO request) {
    final GenericResponseDTO response = new GenericResponseDTO();
    BooleanBuilder booleanBuilder = new BooleanBuilder();
    List<LiqProcesos> procesos;

    if (!"".equals(request.getSortBy())) {
      if (Boolean.TRUE.equals(request.getAsc())) {
        sort = Sort.by(Sort.Direction.ASC, request.getSortBy());
      } else {
        sort = Sort.by(Sort.Direction.DESC, request.getSortBy());
      }
    } else {
      sort = Sort.unsorted();
    }

    if (request.getAnio() != null)
      booleanBuilder.and(QLiqProcesos.liqProcesos.anio.eq(request.getAnio()));
    if (request.getMes() != null)
      booleanBuilder.and(QLiqProcesos.liqProcesos.mes.eq(request.getMes()));

    if (request.getLitId() != null) {
      List<LiqLiquidacionesTiposDTO> tiposLiquidaciones = request.getLitId();
      List<Integer> ids = new ArrayList<>();
      for (LiqLiquidacionesTiposDTO tipoLiq : tiposLiquidaciones) {
        ids.add(Math.toIntExact(tipoLiq.getId()));
      }
      // convierte la lista de ids en un array de Integers para que puedea usarlo el in()
      // new Integer[0] arma un array vacío de Integer
      booleanBuilder.and(QLiqProcesos.liqProcesos.litId.id.in(ids.toArray(new Integer[0])));
    }

    if (booleanBuilder.getValue() != null)
      procesos = (List<LiqProcesos>) liqProcesoRepository.findAll(booleanBuilder.getValue(), sort);
    else
      procesos = liqProcesoRepository.findAll(sort);

    List<ProcesoModel> respuesta =
        procesos.stream().map(LiqProcesoMapper::liqProcesoToProcesoModel).toList();
    response.setEstadoExito(respuesta);
    return response;
  }
}
