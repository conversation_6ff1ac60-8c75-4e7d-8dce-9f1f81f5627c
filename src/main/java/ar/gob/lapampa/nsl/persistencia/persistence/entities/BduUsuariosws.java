package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_USUARIOSWS")
@NamedQueries(
    value = {@NamedQuery(name = "BduUsuariosws.findAll", query = "SELECT b FROM BduUsuariosws b")})
public class BduUsuariosws implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 100)
  @Column(name = "CONTRASENIA")
  private String contrasenia;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 100)
  @Column(name = "USUARIO")
  private String usuario;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "usuarioswsId", fetch = FetchType.LAZY)
  private Set<BduUsuarioswsFsi> bduUsuarioswsFsiSet;
  @JoinColumn(name = "SIS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduSistemas sisId;

  public BduUsuariosws(Long id) {
    this.id = id;
  }

  public BduUsuariosws(Long id, String contrasenia, String usuario) {
    this.id = id;
    this.contrasenia = contrasenia;
    this.usuario = usuario;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduUsuariosws[ id=" + id + " ]";
  }

}
