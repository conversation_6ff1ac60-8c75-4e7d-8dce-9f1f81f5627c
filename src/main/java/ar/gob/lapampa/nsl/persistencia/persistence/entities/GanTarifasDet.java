package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_TARIFAS_DET")
@SequenceGenerator(name = "GAN_TARIFAS_DET_SQ", sequenceName = "GAN_TARIFAS_DET_SQ",
    allocationSize = 1)
public class GanTarifasDet implements Serializable {

  private static final long serialVersionUID = 3436790076059685841L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_TARIFAS_DET_SQ")
  private Long id;

  @JoinColumn(name = "TARIFA", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private GanTarifas tarifaId;

  @Column(name = "MES")
  private Short mes;

  @Column(name = "ORDEN")
  private Short orden;

  @Column(name = "MINIMO_ENTRADA")
  private Double minimoEntrada;

  @Column(name = "MAXIMO_ENTRADA")
  private Double maximoEntrada;

  @Column(name = "ACUMULADO")
  private Double acumulado;

  @Column(name = "ALICUOTA")
  private Double alicuota;

}
