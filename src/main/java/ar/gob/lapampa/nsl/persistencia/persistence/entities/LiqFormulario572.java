package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_FORMULARIO572")
@NamedQueries(value = {
    @NamedQuery(name = "LiqFormulario572.findAll", query = "SELECT l FROM LiqFormulario572 l")})
public class LiqFormulario572 implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected LiqFormulario572PK liqFormulario572PK;
  @Column(name = "CANTIDAD")
  private Integer cantidad;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Column(name = "MONTO")
  private BigDecimal monto;
  @Column(name = "FECHA_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDesde;
  @Column(name = "FECHA_INGRESO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaIngreso;
  @JoinColumn(name = "DED_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqDeducciones liqDeducciones;

  public LiqFormulario572(LiqFormulario572PK liqFormulario572PK) {
    this.liqFormulario572PK = liqFormulario572PK;
  }

  public LiqFormulario572(long dedId, long prsId) {
    this.liqFormulario572PK = new LiqFormulario572PK(dedId, prsId);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqFormulario572[ liqFormulario572PK="
        + liqFormulario572PK + " ]";
  }

}
