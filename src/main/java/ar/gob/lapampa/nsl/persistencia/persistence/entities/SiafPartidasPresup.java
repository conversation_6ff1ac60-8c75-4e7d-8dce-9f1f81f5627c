package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "SIAF_PARTIDAS_PRESUP")
@NamedQueries(value ={
    @NamedQuery(name = "SiafPartidasPresup.findAll", query = "SELECT s FROM SiafPartidasPresup s")})
public class SiafPartidasPresup implements Serializable {

  private static final long serialVersionUID = -5913289543906045911L;

  @EmbeddedId
  protected SiafPartidasPresupPK siafPartidasPresupPK;

  public SiafPartidasPresup(SiafPartidasPresupPK siafPartidasPresupPK) {
    this.siafPartidasPresupPK = siafPartidasPresupPK;
  }

  public SiafPartidasPresup(String ejercicio, String partida) {
    siafPartidasPresupPK = new SiafPartidasPresupPK(ejercicio, partida);
  }

}
