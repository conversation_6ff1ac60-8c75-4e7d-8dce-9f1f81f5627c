package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_FERIADOS_TIPO_NOM")
@NamedQueries(value = {
    @NamedQuery(name = "BduFeriadosTipoNom.findAll", query = "SELECT b FROM BduFeriadosTipoNom b")})
public class BduFeriadosTipoNom implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "TRASLADABLE")
  private Character trasladable;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "ftnId", fetch = FetchType.LAZY)
  private Set<BduFeriados> bduFeriadosSet;

  public BduFeriadosTipoNom(Long id) {
    this.id = id;
  }

  public BduFeriadosTipoNom(Long id, String descripcion, Character activo, Character trasladable) {
    this.id = id;
    this.descripcion = descripcion;
    this.activo = activo;
    this.trasladable = trasladable;
  }

}
