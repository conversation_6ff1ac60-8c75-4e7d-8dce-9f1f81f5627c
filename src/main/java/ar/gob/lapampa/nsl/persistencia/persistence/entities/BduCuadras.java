package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_CUADRAS")
@NamedQueries(
    value = {@NamedQuery(name = "BduCuadras.findAll", query = "SELECT b FROM BduCuadras b")})
public class BduCuadras implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Column(name = "NUMERACION_DESDE")
  private Integer numeracionDesde;
  @Column(name = "NUMERACION_HASTA")
  private Integer numeracionHasta;
  @Size(max = 20)
  @Column(name = "CODIGO_POSTAL")
  private String codigoPostal;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ODT_ID")
  private long odtId;
  @JoinColumn(name = "CLL_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduCalles cllId;
  @JoinColumn(name = "LCD_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduLocalidades lcdId;

  public BduCuadras(Long id) {
    this.id = id;
  }

  public BduCuadras(Long id, long odtId) {
    this.id = id;
    this.odtId = odtId;
  }

}
