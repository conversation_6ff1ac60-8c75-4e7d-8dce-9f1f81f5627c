package ar.gob.lapampa.nsl.persistencia.models;

import java.io.Serializable;
import java.util.List;
import com.querydsl.core.annotations.QueryProjection;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.LiqCodigos;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EscalafonModel extends BaseRRHHDTO implements Serializable {

    private static final long serialVersionUID = 2507717648977008993L;
    private String descripcion;
    private Long nro;
    private List<LiqCodigos> codigos;

    @QueryProjection
    public EscalafonModel(Long id) {
        super();
        this.id = id;
    }

    /**
     * Obtiene Nombre de class name solo
     *
     * @return String
     */
    @Override
    public String getBeanName() {
        return EscalafonModel.class.getSimpleName();
    }

    /**
     * Obtiene Nombre de package + class name
     *
     * @return String
     */
    @Override
    public String getCualifiedName() {
        return EscalafonModel.class.getCanonicalName();
    }
}
