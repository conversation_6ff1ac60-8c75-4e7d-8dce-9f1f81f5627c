package ar.gob.lapampa.nsl.persistencia.services;

import java.util.List;
import java.util.Map;
import org.springframework.stereotype.Service;
import ar.gob.lapampa.nsl.datatransfer.BaseDTO;
import ar.gob.lapampa.nsl.datatransfer.request.ListadoGenericoDTORequest;
import ar.gob.lapampa.nsl.datatransfer.request.ListadoPaginadoDTORequest;
import ar.gob.lapampa.nsl.excepciones.common.BaseDTONoUnicoException;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.utils.common.PaginationResult;
import jakarta.persistence.criteria.JoinType;

@Service
public interface GenericService {

  PaginationResult<BaseDTO> consultarPorFiltrosConPaginacion(BaseDTO filtro, int index, int offset);

  PaginationResult<BaseDTO> consultarPorFiltrosConPaginacionYMultiOrden(BaseDTO filtro, int index,
      int offset);

  Long contar(BaseDTO dto);

  Long contarOtros(BaseDTO dto, BaseDTO dtoPropio);

  Object createQuery(String query);

  List<BaseDTO> findAll(BaseDTO baseDTO);

  List<BaseDTO> findAllByExample(BaseDTO baseDTO);

  List<BaseDTO> findAllByExampleAndOrder(BaseDTO baseDTO, String campo, String orden);

  List<BaseDTO> findAllByOrder(BaseDTO baseDTO, String campo, String orden);

  <T> List<T> findByHQL(String hql);

  List<BaseDTO> findByNamedQuery(String name, Map<String, Object> paramsMap, BaseDTO baseDto);

  List<BaseDTO> findByNamedQuery(String name, Object[] params, BaseDTO baseDto);

  BaseDTO findOne(BaseDTO baseDTO);

  void persist(BaseDTO baseDTO) throws BaseDTONoUnicoException;

  BaseDTO persistAndReturn(BaseDTO baseDTO) throws BaseDTONoUnicoException;

  void remove(BaseDTO baseDTO);

  void removeAll(List<BaseDTO> dtos);

  GenericService setFieldsOrderBy(String fieldOrderBy);

  GenericService setIgnoreZeroes(boolean ignoreZeroes);

  GenericService setJoinType(JoinType joinType);

  GenericService setMatchLike(boolean matchLike);

  GenericService setOrderType(String orderType);

  void update(BaseDTO baseDTO) throws BaseDTONoUnicoException;

  void logException(Throwable t, Class<?> clazz);

  GenericResponseDTO listarGenerico(ListadoGenericoDTORequest request);

  GenericResponseDTO listarGenericoPaginado(ListadoPaginadoDTORequest request);

}
