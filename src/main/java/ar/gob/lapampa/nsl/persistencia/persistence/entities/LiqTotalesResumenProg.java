package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_TOTALES_RESUMEN_PROG")
@NamedQueries(value = {@NamedQuery(name = "LiqTotalesResumenProg.findAll",
    query = "SELECT l FROM LiqTotalesResumenProg l")})
public class LiqTotalesResumenProg implements Serializable {
  private static final long serialVersionUID = 1L;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private BigDecimal id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PROG_ID")
  private long progId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "NOMINAL_REM")
  private BigDecimal nominalRem;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "NOMINAL_NO_REM")
  private BigDecimal nominalNoRem;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ASIG_FLIARES")
  private BigDecimal asigFliares;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PATRONALES")
  private BigDecimal patronales;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "DESCUENTOS")
  private BigDecimal descuentos;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "LIQUIDO")
  private BigDecimal liquido;
  @JoinColumn(name = "PRC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqProcesos prcId;
  @JoinColumn(name = "ESC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PreEscalafones escId;
  @JoinColumn(name = "PLT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PrePlantasTipos pltId;

  public LiqTotalesResumenProg(BigDecimal id) {
    this.id = id;
  }

  public LiqTotalesResumenProg(BigDecimal id, long progId, BigDecimal nominalRem,
      BigDecimal nominalNoRem, BigDecimal asigFliares, BigDecimal patronales, BigDecimal descuentos,
      BigDecimal liquido) {
    this.id = id;
    this.progId = progId;
    this.nominalRem = nominalRem;
    this.nominalNoRem = nominalNoRem;
    this.asigFliares = asigFliares;
    this.patronales = patronales;
    this.descuentos = descuentos;
    this.liquido = liquido;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqTotalesResumenProg[ id=" + id + " ]";
  }

}
