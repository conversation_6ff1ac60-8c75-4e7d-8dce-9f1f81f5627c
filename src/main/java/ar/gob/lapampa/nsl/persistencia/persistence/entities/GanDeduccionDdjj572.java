package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_DEDUCCION_DDJJ572")
@SequenceGenerator(name = "GAN_DEDUCCION_DDJJ572_SQ", sequenceName = "GAN_DEDUCCION_DDJJ572_SQ",
    allocationSize = 1)
public class GanDeduccionDdjj572 implements Serializable {

  private static final long serialVersionUID = -6222121616656657013L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_DEDUCCION_DDJJ572_SQ")
  private Long id;

  @Column(name = "DEDUCCION_TIPO")
  private Long deduccionTipo;

  @Column(name = "DENOMINACION")
  private String denominacion;

  @Column(name = "DESCBASICA")
  private String descbasica;

  @Column(name = "DESCADICIONAL")
  private String descadicional;

  @Column(name = "MONTOTOTAL")
  private BigDecimal montototal;

  @Column(name = "NRODOC")
  private String nrodoc;

  @JoinColumn(name = "PRESENTACION", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER, cascade = CascadeType.ALL)
  private GanPresentDdjj572 presentacion;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "deduccion", fetch = FetchType.LAZY)
  private List<GanDeduccionDetDdjj572> detalles;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "deduccion", fetch = FetchType.LAZY)
  private List<GanDeduccionPerDdjj572> periodos;

  @Column(name = "TIPODOC")
  private Long tipodoc;

}
