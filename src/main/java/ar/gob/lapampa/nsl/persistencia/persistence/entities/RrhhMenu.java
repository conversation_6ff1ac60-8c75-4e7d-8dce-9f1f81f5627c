package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import org.hibernate.type.YesNoConverter;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_MENU")
@SequenceGenerator(name = "RRHH_MENU_SEQ", sequenceName = "RRHH_MENU_SEQ", allocationSize = 1)
public class RrhhMenu implements Serializable {

  @Serial
  private static final long serialVersionUID = 4105497074379378347L;

  @Id
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_MENU_SEQ")
  private Long id;

  @Basic(optional = false)
  @Column(name = "NOMBRE")
  private String nombre;

  @Basic(optional = false)
  @Column(name = "ESTADO")
  private String estado;

  @ManyToOne(optional = true)
  @JoinColumn(name = "MENU_PADRE_ID", referencedColumnName = "id")
  private RrhhMenu menuPadreId;

  @Basic(optional = true)
  @Column(name = "ICONO")
  private String icono;

  @Column(name = "ABSTRACTO")
  @Convert(converter = YesNoConverter.class)
  private Boolean abstracto;

  // NO SE USAN PERO ESTAN EN TABLA (DESDE LEMON DATA)
  // @Basic(optional = true)
  // @Column(name = "VERSION")
  // private String version;
  //
  // @Basic(optional = true)
  // @Column(name = "NUEVA_RUTA")
  // private String nuevaRuta;


  @JoinTable(name = "RRHH_MENU_ROLES",
      joinColumns = {@JoinColumn(name = "RRHH_MENU_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "RRHH_ROL_ID", referencedColumnName = "ID")})
  @ManyToMany(fetch = FetchType.LAZY)
  private List<RrhhRol> roles;

  public RrhhMenu(Long id) {
    this.id = id;
  }

}
