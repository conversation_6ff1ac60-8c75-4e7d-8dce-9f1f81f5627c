package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ACT_ENTID_PERF_EVE")
@NamedQueries(value = {
    @NamedQuery(name = "BduActEntidPerfEve.findAll", query = "SELECT b FROM BduActEntidPerfEve b")})
public class BduActEntidPerfEve implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;
  @Column(name = "PRF_ID")
  private Long prfId;
  @Size(max = 400)
  @Column(name = "OBSERVACIONES")
  private String observaciones;
  @JoinColumn(name = "ACE_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduActEntidPerf aceId;
  @JoinColumn(name = "USUARIO", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduActores usuario;
  @JoinColumn(name = "TEV_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduEventosTipos tevId;

  public BduActEntidPerfEve(Long id) {
    this.id = id;
  }

  public BduActEntidPerfEve(Long id, Date fecha) {
    this.id = id;
    this.fecha = fecha;
  }

}
