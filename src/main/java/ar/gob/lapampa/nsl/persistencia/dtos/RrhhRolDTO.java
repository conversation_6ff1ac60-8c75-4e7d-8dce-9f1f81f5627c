package ar.gob.lapampa.nsl.persistencia.dtos;

import java.io.Serializable;
import java.util.List;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import ar.gob.lapampa.nsl.datatransfer.RrhhAccionesDTO;
import ar.gob.lapampa.nsl.datatransfer.RrhhEmpresasDTO;
import ar.gob.lapampa.nsl.datatransfer.RrhhMenuDTO;
import ar.gob.lapampa.nsl.datatransfer.RrhhRolesDTO;
import ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RrhhRolDTO extends BaseRRHHDTO implements Serializable {

  private static final long serialVersionUID = 6666430376952121565L;
  private String nombre;
  private String descripcion;
  private List<RrhhAccionesDTO> acciones;
  private List<RrhhMenuDTO> menues;
  private List<RrhhEmpresasDTO> empresas;
  private List<RrhhUsuariosDTO> usuarios;

  public RrhhRolDTO(Long id) {
    super();
    this.id = id;
  }

  public RrhhRolDTO(List<RrhhUsuariosDTO> usuarios) {
    super();
    this.usuarios = usuarios;
  }

  @Override
  public String getBeanName() {
    return RrhhRolesDTO.class.getName();
  }

}

