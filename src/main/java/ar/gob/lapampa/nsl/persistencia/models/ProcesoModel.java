package ar.gob.lapampa.nsl.persistencia.models;

import java.io.Serializable;
import java.util.Date;
import com.querydsl.core.annotations.QueryProjection;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.LiqLiquidacionesTipos;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.LiqProcesos;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProcesoModel extends BaseRRHHDTO implements Serializable {

    private static final long serialVersionUID = -5006176003370476554L;
    private Date fechaInicio;
    private Date fechaFin;

    private Short mes;
    private Short anio;
    private Date fechaCierre;

    private Character tipoProceso;

    private Character estado;
    private LiqProcesos prcId;

    private LiqLiquidacionesTipos litId;
    private String nombreLiquidacion;

    @QueryProjection
    public ProcesoModel(Long id) {
        super();
        this.id = id;
    }

    /**
     * Obtiene Nombre de class name solo
     *
     * @return String
     */
    @Override
    public String getBeanName() {
        return ProcesoModel.class.getSimpleName();
    }

    /**
     * Obtiene Nombre de package + class name
     *
     * @return String
     */
    @Override
    public String getCualifiedName() {
        return ProcesoModel.class.getCanonicalName();
    }
}
