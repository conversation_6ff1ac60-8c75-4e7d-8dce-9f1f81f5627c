package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_ACCIONES")
@SequenceGenerator(name = "RRHH_ACCIONES_SEQ", sequenceName = "RRHH_ACCIONES_SEQ",
    allocationSize = 1)
public class RrhhAcciones implements Serializable {

  @Serial
  private static final long serialVersionUID = -6661813076175707278L;

  @Id
  @Basic(optional = false)
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_ACCIONES_SEQ")
  private Long id;

  @Basic(optional = false)
  @Column(name = "NOMBRE")
  private String nombre;

  @Basic(optional = true)
  @Column(name = "ENDPOINT")
  private String endpoint;

  @Basic(optional = true)
  @Column(name = "NOTA")
  private String nota;

  @JoinTable(name = "RRHH_ACCIONES_ROLES",
      joinColumns = {@JoinColumn(name = "RRHH_ACCION_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "RRHH_ROL_ID", referencedColumnName = "ID")})
  @ManyToMany(fetch = FetchType.LAZY)
  private List<RrhhRol> roles;

  public RrhhAcciones(Long id) {
    this.id = id;
  }


}
