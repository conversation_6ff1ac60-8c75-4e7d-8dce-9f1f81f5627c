package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_MODALIDADES")
@NamedQueries(value = {
    @NamedQuery(name = "LiqModalidades.findAll", query = "SELECT l FROM LiqModalidades l")})
public class LiqModalidades implements Serializable {
  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 4000)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @OneToMany(mappedBy = "modId", fetch = FetchType.LAZY)
  private Set<LiqPasos> liqPasosSet;
  @OneToMany(mappedBy = "modId", fetch = FetchType.LAZY)
  private Set<LiqCodigos> liqCodigosSet;

  public LiqModalidades(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqModalidades[ id=" + id + " ]";
  }

}
