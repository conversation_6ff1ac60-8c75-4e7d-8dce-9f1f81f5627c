package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_CALLES_ALIAS")
@NamedQueries(value = {
    @NamedQuery(name = "BduCallesAlias.findAll", query = "SELECT b FROM BduCallesAlias b")})
public class BduCallesAlias implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "CLL_ID")
  private long cllId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "LCD_ID")
  private long lcdId;
  @Size(max = 200)
  @Column(name = "NOMBRE")
  private String nombre;
  @JoinColumn(name = "ODT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduOrigenesDatos odtId;

  public BduCallesAlias(Long id) {
    this.id = id;
  }

  public BduCallesAlias(Long id, long cllId, long lcdId) {
    this.id = id;
    this.cllId = cllId;
    this.lcdId = lcdId;
  }

}
