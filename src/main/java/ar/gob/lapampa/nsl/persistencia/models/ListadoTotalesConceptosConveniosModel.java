package ar.gob.lapampa.nsl.persistencia.models;

import java.io.Serializable;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ListadoTotalesConceptosConveniosModel extends BaseRRHHDTO implements Serializable {

    private static final long serialVersionUID = -339052053782615700L;
    private Long convenioNro;
    private String convenioNombre;
    private String codigoNro;
    private String concepto;
    private Double haberes;
    private Double descuentos;
    private String codigoEmpresa;
    private String descripcionEmpresa;
    private Long cantHaberes;
    private Long cantDescuentos;
    private Long registrosTotales;


    /**
     * Obtiene Nombre de class name solo
     *
     * @return String
     */
    @Override
    public String getBeanName() {
        return ListadoTotalesConceptosConveniosModel.class.getSimpleName();
    }

    /**
     * Obtiene Nombre de package + class name
     *
     * @return String
     */
    @Override
    public String getCualifiedName() {
        return ListadoTotalesConceptosConveniosModel.class.getCanonicalName();
    }
}
