package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_PARAMETROS_MENSUALES")
@SequenceGenerator(name = "LIQ_PAM_SEQ", sequenceName = "LIQ_PAM_SEQ", allocationSize = 1)
public class LiqParametrosMensuales implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_PAM_SEQ")
  private Long id;
  @Column(name = "ANIO")
  private Short anio;
  @Column(name = "MES")
  private Short mes;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Column(name = "MONTO")
  private BigDecimal monto;
  @Column(name = "CDAD")
  private Long cdad;
  @Column(name = "PORC")
  private BigDecimal porc;
  @Column(name = "MONTO2")
  private BigDecimal monto2;
  @Size(max = 4000)
  @Column(name = "MENSAJES")
  private String mensajes;
  @JoinColumn(name = "PAT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqParametrosTipos patId;

  public LiqParametrosMensuales(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqParametrosMensuales[ id=" + id + " ]";
  }

}
