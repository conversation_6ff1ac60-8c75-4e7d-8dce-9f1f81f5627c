package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_CMU_SANCIONES_TIPOS")
@NamedQueries(value = {@NamedQuery(name = "RrhhCmuSancionesTipos.findAll",
    query = "SELECT r FROM RrhhCmuSancionesTipos r")})
public class RrhhCmuSancionesTipos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(max = 1000)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @OneToMany(mappedBy = "satId", fetch = FetchType.LAZY)
  private Set<RrhhReconocimientosMedicos> rrhhReconocimientosMedicosSet;

  public RrhhCmuSancionesTipos(Long id) {
    this.id = id;
  }

  public RrhhCmuSancionesTipos(Long id, String descripcion) {
    this.id = id;
    this.descripcion = descripcion;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhCmuSancionesTipos[ id=" + id + " ]";
  }

}
