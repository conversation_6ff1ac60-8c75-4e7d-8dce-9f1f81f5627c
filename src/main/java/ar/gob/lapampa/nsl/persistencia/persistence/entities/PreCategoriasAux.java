package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_CATEGORIAS_AUX")
@NamedQueries(value = {
    @NamedQuery(name = "PreCategoriasAux.findAll", query = "SELECT p FROM PreCategoriasAux p")})
public class PreCategoriasAux implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "TRA_ID")
  private long traId;
  @Column(name = "ORDEN")
  private Long orden;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @Column(name = "NRO")
  private Short nro;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Column(name = "HABER_BASE")
  private BigDecimal haberBase;
  @Column(name = "NRO2")
  private Short nro2;
  @Column(name = "ID_ACTUAL")
  private Long idActual;

  public PreCategoriasAux(Long id) {
    this.id = id;
  }

  public PreCategoriasAux(Long id, long traId, Character activo) {
    this.id = id;
    this.traId = traId;
    this.activo = activo;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.PreCategoriasAux[ id=" + id + " ]";
  }

}
