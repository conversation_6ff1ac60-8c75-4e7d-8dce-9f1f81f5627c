package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_GANANCIAS_ACUMULADAS")
@NamedQueries(value = {@NamedQuery(name = "LiqGananciasAcumuladas.findAll",
    query = "SELECT l FROM LiqGananciasAcumuladas l")})
public class LiqGananciasAcumuladas implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRS_ID")
  private long prsId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANIO")
  private short anio;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MES")
  private short mes;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACUMULADO_HABERES")
  private BigDecimal acumuladoHaberes;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACUMULADO_GANANCIAS")
  private BigDecimal acumuladoGanancias;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACUMULADO_DEDUCCIONES")
  private BigDecimal acumuladoDeducciones;
  @Column(name = "RETENCION")
  private BigDecimal retencion;

  public LiqGananciasAcumuladas(Long id) {
    this.id = id;
  }

  public LiqGananciasAcumuladas(Long id, long prsId, short anio, short mes,
      BigDecimal acumuladoHaberes, BigDecimal acumuladoGanancias, BigDecimal acumuladoDeducciones) {
    this.id = id;
    this.prsId = prsId;
    this.anio = anio;
    this.mes = mes;
    this.acumuladoHaberes = acumuladoHaberes;
    this.acumuladoGanancias = acumuladoGanancias;
    this.acumuladoDeducciones = acumuladoDeducciones;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqGananciasAcumuladas[ id=" + id + " ]";
  }

}
