package ar.gob.lapampa.nsl.persistencia.security;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class JwtRequestDTO {

  private String token;

  private String serviceName;

  public JwtRequestDTO(String token) {
    this.token = token;
    this.serviceName = "";
    // this.serviceName = appName + "_" + version;
  }
}
