package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_CGE_RECIBOS")
@NamedQueries(
    value = {@NamedQuery(name = "LiqCgeRecibos.findAll", query = "SELECT l FROM LiqCgeRecibos l")})
public class LiqCgeRecibos implements Serializable {
  private static final long serialVersionUID = 1L;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private BigDecimal id;
  @Size(max = 200)
  @Column(name = "APELLIDO")
  private String apellido;
  @Size(max = 200)
  @Column(name = "NOMBRE")
  private String nombre;
  @Column(name = "DOCUMENTO")
  private BigInteger documento;
  @Column(name = "LEGAJO")
  private BigInteger legajo;
  @Column(name = "ANTIGUEDAD_MES")
  private BigInteger antiguedadMes;
  @Column(name = "ANTIGUEDAD_ANIO")
  private BigInteger antiguedadAnio;
  @Column(name = "MES_LIQUIDACION")
  private BigInteger mesLiquidacion;
  @Column(name = "ANIO_LIQUIDACION")
  private BigInteger anioLiquidacion;
  @Size(max = 200)
  @Column(name = "CUENTA_DEPOSITAR")
  private String cuentaDepositar;
  @Size(max = 200)
  @Column(name = "SUCURSAL")
  private String sucursal;
  @Column(name = "TOTAL_HABERES")
  private BigDecimal totalHaberes;
  @Column(name = "TOTAL_DESCUENTOS")
  private BigDecimal totalDescuentos;
  @Column(name = "TOTAL_BANCO")
  private BigDecimal totalBanco;
  @Column(name = "TOTAL_BRUTO")
  private BigDecimal totalBruto;
  @Column(name = "TOTAL_NETO")
  private BigDecimal totalNeto;
  @Column(name = "LIQUIDO_A_PAGAR")
  private BigDecimal liquidoAPagar;
  @Column(name = "NRO_RECIBO")
  private BigInteger nroRecibo;
  @Size(max = 200)
  @Column(name = "ESTABLECIMIENTO")
  private String establecimiento;
  @Column(name = "DAD_ID")
  private BigInteger dadId;
  @Column(name = "PRO_ID")
  private BigInteger proId;
  @Column(name = "ACT_ID")
  private BigInteger actId;
  @Column(name = "CAT_ID")
  private BigInteger catId;
  @Column(name = "EST_ID")
  private BigInteger estId;
  @Column(name = "CEQ_CAT_ID")
  private Long ceqCatId;

  public LiqCgeRecibos(BigDecimal id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqCgeRecibos[ id=" + id + " ]";
  }

}
