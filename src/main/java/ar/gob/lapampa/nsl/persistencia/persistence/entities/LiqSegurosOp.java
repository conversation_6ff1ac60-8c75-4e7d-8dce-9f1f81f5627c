package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_SEGUROS_OP")
public class LiqSegurosOp implements Serializable {

  private static final long serialVersionUID = 6846666679900636526L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  private String activo;

  @Column(name = "CAP_SUELDO_CYGE")
  private BigDecimal capSueldoCyge;

  @Column(name = "CAP_SUELDO_TIT")
  private BigDecimal capSueldoTit;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "segId", fetch = FetchType.LAZY)
  private List<LiqSegurosOpDetalle> detalle;

  private String descripcion;

}
