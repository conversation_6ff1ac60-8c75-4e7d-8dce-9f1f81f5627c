package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class RrhhLicenciasDetMensualPK implements Serializable {

  private static final long serialVersionUID = 7192036223982502901L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "LIC_OCU_ID")
  private long licOcuId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "LIC_ITEM")
  private long licItem;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANIO")
  private short anio;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "MES")
  private short mes;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhLicenciasDetMensualPK[ licOcuId=" + licOcuId
        + ", licItem=" + licItem + ", anio=" + anio + ", mes=" + mes + " ]";
  }

}
