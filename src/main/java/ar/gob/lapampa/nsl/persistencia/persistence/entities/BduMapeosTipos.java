package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_MAPEOS_TIPOS")
@NamedQueries(value = {
    @NamedQuery(name = "BduMapeosTipos.findAll", query = "SELECT b FROM BduMapeosTipos b")})
public class BduMapeosTipos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "POR_TRG")
  private Character porTrg;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "mptId", fetch = FetchType.LAZY)
  private Set<BduOrganismosMapeos> bduOrganismosMapeosSet;

  public BduMapeosTipos(Long id) {
    this.id = id;
  }

  public BduMapeosTipos(Long id, String descripcion) {
    this.id = id;
    this.descripcion = descripcion;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduMapeosTipos[ id=" + id + " ]";
  }

}
