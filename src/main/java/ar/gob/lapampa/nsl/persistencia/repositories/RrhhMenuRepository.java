package ar.gob.lapampa.nsl.persistencia.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhMenu;

public interface RrhhMenuRepository
    extends JpaRepository<RrhhMenu, Long>, QuerydslPredicateExecutor<RrhhMenu> {

  @Procedure(procedureName = "LAPAMPA.SYNC_RRHH_MENU_ROLES")
  String syncMenusRoles(@Param("p_rrhh_rol_id") Long rrhhRolId,
      @Param("p_menu_list") String rolesList);
}
