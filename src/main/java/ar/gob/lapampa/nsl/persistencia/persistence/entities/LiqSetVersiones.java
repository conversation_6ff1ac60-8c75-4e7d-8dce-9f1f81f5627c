package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_SET_VERSIONES")
@SequenceGenerator(name = "LIQ_SVE_SEQ", sequenceName = "LIQ_SVE_SEQ", allocationSize = 1)
public class LiqSetVersiones implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_SVE_SEQ")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_ALTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAlta;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "OFICIALES")
  @Convert(converter = YesNoConverter.class)
  private Boolean oficiales;

  @Column(name = "MES")
  private Short mes;

  @Column(name = "ANIO")
  private Short anio;

  @JoinColumn(name = "ESC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PreEscalafones escId;

  @JoinColumn(name = "LSVT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqSetVersionesTipos lsvtId;

  @JoinTable(name = "LIQ_SET_VERSIONES_CONF",
      joinColumns = {@JoinColumn(name = "SVE_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "COV_ID", referencedColumnName = "ID")})
  @ManyToMany(fetch = FetchType.LAZY)
  private List<LiqCodigosVersiones> liqCodigosVersionesSet;

  @OneToMany(mappedBy = "sveId", fetch = FetchType.LAZY)
  private List<LiqProcPasosEsc> liqProcPasosEscSet;

  @OneToMany(mappedBy = "sveId", fetch = FetchType.LAZY)
  private List<LiqProcesosSetCodigos> liqProcesosSetCodigosSet;

  public LiqSetVersiones(Long id) {
    this.id = id;
  }

  public LiqSetVersiones(Long id, String descripcion, Date fechaAlta, Boolean oficiales,
      Boolean oficialesOmnibus) {
    this.id = id;
    this.descripcion = descripcion;
    this.fechaAlta = fechaAlta;
    this.oficiales = oficiales;
  }

}
