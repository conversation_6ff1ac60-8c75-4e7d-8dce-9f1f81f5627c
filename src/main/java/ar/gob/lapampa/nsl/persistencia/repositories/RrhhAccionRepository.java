package ar.gob.lapampa.nsl.persistencia.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhAcciones;

public interface RrhhAccionRepository
    extends JpaRepository<RrhhAcciones, Long>, QuerydslPredicateExecutor<RrhhAcciones> {

  @Procedure(procedureName = "LAPAMPA.SYNC_RRHH_ACCIONES_ROLES")
  String syncAccionesRoles(@Param("p_rrhh_rol_id") Long rrhhRolId,
      @Param("p_accion_list") String accionList);
}
