package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_ACUMULADORES_INFO")
public class GanAcumuladoresInfo implements Serializable {

  private static final long serialVersionUID = 7486946166771738046L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Column(name = "DETALLE")
  private String detalle;

  @Column(name = "COMENTARIO")
  private String comentario;

  @Column(name = "NOMBRE_CORTO")
  private String nombreCorto;

  @Column(name = "MOSTRAR")
  @Convert(converter = YesNoConverter.class)
  private Boolean mostrar;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "ACUM_ID", nullable = false)
  private GanAcumuladores acumulador;

}
