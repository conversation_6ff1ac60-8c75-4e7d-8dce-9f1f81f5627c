package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_ACUMULADORES_PGO_DET")
@SequenceGenerator(name = "GAN_ACUMULADORES_PGO_DET_SQ",
    sequenceName = "GAN_ACUMULADORES_PGO_DET_SQ", allocationSize = 1)
public class GanAcumuladoresPagoDet implements Serializable {

  private static final long serialVersionUID = -2775968379941840908L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_ACUMULADORES_PGO_DET_SQ")
  private Long id;

  @Column(name = "MES_DESDE")
  private Short mesDesde;

  @Column(name = "MES_HASTA")
  private Short mesHasta;

  @Column(name = "ANIO")
  private Short anio;

  @Column(name = "COEFICIENTE")
  private Double coeficiente;

  @Column(name = "SIGNO")
  private Short signo;

  @JoinColumn(name = "ESC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PreEscalafones escId;

  @JoinColumn(name = "AP_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private GanAcumuladoresPago acpId;

}
