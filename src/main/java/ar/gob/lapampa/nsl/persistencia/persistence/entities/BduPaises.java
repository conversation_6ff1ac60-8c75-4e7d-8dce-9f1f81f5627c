package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PAISES")
@NamedQueries(
    value = {@NamedQuery(name = "BduPaises.findAll", query = "SELECT b FROM BduPaises b")})
public class BduPaises implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "NOMBRE")
  private String nombre;
  @Size(max = 10)
  @Column(name = "CODIGO")
  private String codigo;
  @Size(max = 10)
  @Column(name = "CODIGO_TE")
  private String codigoTe;
  @Size(max = 200)
  @Column(name = "GENTILICIO")
  private String gentilicio;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ODT_ID")
  private long odtId;

  public BduPaises(Long id) {
    this.id = id;
  }

  public BduPaises(Long id, long odtId) {
    this.id = id;
    this.odtId = odtId;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduPaises[ id=" + id + " ]";
  }

}
