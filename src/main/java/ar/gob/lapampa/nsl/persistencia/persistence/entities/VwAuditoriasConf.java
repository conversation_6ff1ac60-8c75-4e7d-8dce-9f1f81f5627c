package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

// @Embeddable
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@IdClass(VwAuditoriasConf.class)
@Table(name = "AUDITORIA_CONF_VIEW")
public class VwAuditoriasConf implements Serializable {

  private static final long serialVersionUID = -7472593886512141754L;

  @Column(name = "TABLA")
  @Id
  private String tablaOrigen;

  @Id
  @Column(name = "NOMBRE_TRIGGER")
  private String nombreTrigger;

  @Id
  @Column(name = "TABLA_AUDITORIA")
  private String tablaAuditoria;

}
