package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_PARAMETROS_TIPOS")
@SequenceGenerator(name = "LIQ_PAT_SEQ", sequenceName = "LIQ_PAT_SEQ", allocationSize = 1)
public class LiqParametrosTipos implements Serializable {

  private static final long serialVersionUID = 1617226544299731130L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_PAT_SEQ")
  private Long id;

  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @OneToMany(mappedBy = "patId", fetch = FetchType.LAZY)
  private Set<LiqParametros> liqParametrosSet;

  @OneToMany(mappedBy = "patId", fetch = FetchType.LAZY)
  private Set<LiqParametrosMensuales> liqParametrosMensualesSet;

  public LiqParametrosTipos(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqParametrosTipos[ id=" + id + " ]";
  }

}
