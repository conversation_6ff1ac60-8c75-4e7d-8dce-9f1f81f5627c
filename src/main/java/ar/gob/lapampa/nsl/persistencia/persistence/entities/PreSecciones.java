package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_SECCIONES")
public class PreSecciones implements Serializable {

  private static final long serialVersionUID = -3985137525870828503L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Column(name = "CODIGO")
  private Long codigo;

  @Column(name = "DESCRIPCION")
  private String descripcion;


}
