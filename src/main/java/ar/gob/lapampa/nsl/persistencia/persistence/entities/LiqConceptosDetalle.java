package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_CONCEPTOS_DETALLE")
@NamedQueries(value = {@NamedQuery(name = "LiqConceptosDetalle.findAll",
    query = "SELECT l FROM LiqConceptosDetalle l")})
public class LiqConceptosDetalle implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Column(name = "MONTO_VIEJO")
  private BigDecimal montoViejo;
  @Column(name = "MONTO_NUEVO")
  private BigDecimal montoNuevo;
  @Column(name = "MES")
  private Short mes;
  @Column(name = "ANIO")
  private Short anio;
  @Column(name = "OCU_ID")
  private Long ocuId;
  @Column(name = "ITEM")
  private BigInteger item;
  @Size(max = 4000)
  @Column(name = "COMENTARIO")
  private String comentario;
  @Size(max = 20)
  @Column(name = "COD_ID")
  private String codId;

  public LiqConceptosDetalle(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqConceptosDetalle[ id=" + id + " ]";
  }

}
