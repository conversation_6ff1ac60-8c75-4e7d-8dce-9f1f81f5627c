package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_FERIADOS")
@NamedQueries(
    value = {@NamedQuery(name = "BduFeriados.findAll", query = "SELECT b FROM BduFeriados b")})
public class BduFeriados implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "NACIONALES")
  private Character nacionales;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "TRASLADABLE")
  private Character trasladable;
  @Column(name = "FECHA_ORIGEN")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaOrigen;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_FERIADO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaFeriado;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @JoinColumn(name = "FTN_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduFeriadosTipoNom ftnId;
  @JoinColumn(name = "FTD_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduFeriadoTipoDesc ftdId;
  @JoinColumn(name = "ODT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduOrigenesDatos odtId;

  public BduFeriados(Long id) {
    this.id = id;
  }

  public BduFeriados(Long id, String descripcion, Character nacionales, Character trasladable,
      Date fechaFeriado, Character activo) {
    this.id = id;
    this.descripcion = descripcion;
    this.nacionales = nacionales;
    this.trasladable = trasladable;
    this.fechaFeriado = fechaFeriado;
    this.activo = activo;
  }

}
