package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_RETPERPAGOS_PER_DDJJ572")
@SequenceGenerator(name = "GAN_RETPERPAGOS_PER_DDJJ572_SQ",
    sequenceName = "GAN_RETPERPAGOS_PER_DDJJ572_SQ", allocationSize = 1)
public class GanRetperpagosPerDdjj572 implements Serializable {

  private static final long serialVersionUID = 3106134227576527420L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_RETPERPAGOS_PER_DDJJ572_SQ")
  private Long id;

  @JoinColumn(name = "RETPERPAGOS", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  private GanRetperpagosDdjj572 retPerPagos;

  @Column(name = "MES_DESDE")
  private Long mesDesde;

  @Column(name = "MES_HASTA")
  private Long mesHasta;

  @Column(name = "MONTOMENSUAL")
  private BigDecimal montoMensual;

}
