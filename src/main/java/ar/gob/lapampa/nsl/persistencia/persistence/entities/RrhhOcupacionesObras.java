package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_OCUPACIONES_OBRAS")
@NamedQueries(value = {@NamedQuery(name = "RrhhOcupacionesObras.findAll",
    query = "SELECT r FROM RrhhOcupacionesObras r")})
public class RrhhOcupacionesObras implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected RrhhOcupacionesObrasPK rrhhOcupacionesObrasPK;
  @Column(name = "FECHA_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDesde;
  @Column(name = "FECHA_HASTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaHasta;
  @JoinColumn(name = "OBR_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhObras rrhhObras;

  public RrhhOcupacionesObras(RrhhOcupacionesObrasPK rrhhOcupacionesObrasPK) {
    this.rrhhOcupacionesObrasPK = rrhhOcupacionesObrasPK;
  }

  public RrhhOcupacionesObras(long ocuId, long obrId) {
    this.rrhhOcupacionesObrasPK = new RrhhOcupacionesObrasPK(ocuId, obrId);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhOcupacionesObras[ rrhhOcupacionesObrasPK="
        + rrhhOcupacionesObrasPK + " ]";
  }

}
