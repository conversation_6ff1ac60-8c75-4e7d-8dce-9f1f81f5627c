package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ORGANISMOS_FNC")
@NamedQueries(value = {
    @NamedQuery(name = "BduOrganismosFnc.findAll", query = "SELECT b FROM BduOrganismosFnc b")})
public class BduOrganismosFnc implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "CODIGO")
  private Integer codigo;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ODT_ID")
  private long odtId;
  @Column(name = "DMC_ID")
  private Long dmcId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDesde;
  @Column(name = "NVL_ID")
  private Long nvlId;
  @Column(name = "ORF_ID_BASE")
  private Long orfIdBase;
  @OneToMany(mappedBy = "orfId", fetch = FetchType.LAZY)
  private Set<BduOrganismosFnc> bduOrganismosFncSet;
  @JoinColumn(name = "ORF_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduOrganismosFnc orfId;

  public BduOrganismosFnc(Long id) {
    this.id = id;
  }

  public BduOrganismosFnc(Long id, long odtId, Character activo, Date fechaDesde) {
    this.id = id;
    this.odtId = odtId;
    this.activo = activo;
    this.fechaDesde = fechaDesde;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduOrganismosFnc[ id=" + id + " ]";
  }

}
