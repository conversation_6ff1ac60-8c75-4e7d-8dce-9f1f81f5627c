package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_DEDUCCIONES_PERSONAS_DET")
@NamedQueries(value = {@NamedQuery(name = "LiqDeduccionesPersonasDet.findAll",
    query = "SELECT l FROM LiqDeduccionesPersonasDet l")})
public class LiqDeduccionesPersonasDet implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected LiqDeduccionesPersonasDetPK liqDeduccionesPersonasDetPK;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MONTO")
  private BigDecimal monto;
  @JoinColumn(name = "DED_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqDeducciones liqDeducciones;
  @JoinColumn(name = "DDP_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqDeduccionesPersonas liqDeduccionesPersonas;

  public LiqDeduccionesPersonasDet(LiqDeduccionesPersonasDetPK liqDeduccionesPersonasDetPK) {
    this.liqDeduccionesPersonasDetPK = liqDeduccionesPersonasDetPK;
  }

  public LiqDeduccionesPersonasDet(LiqDeduccionesPersonasDetPK liqDeduccionesPersonasDetPK,
      BigDecimal monto) {
    this.liqDeduccionesPersonasDetPK = liqDeduccionesPersonasDetPK;
    this.monto = monto;
  }

  public LiqDeduccionesPersonasDet(long ddpId, long dedId) {
    this.liqDeduccionesPersonasDetPK = new LiqDeduccionesPersonasDetPK(ddpId, dedId);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqDeduccionesPersonasDet[ liqDeduccionesPersonasDetPK="
        + liqDeduccionesPersonasDetPK + " ]";
  }

}
