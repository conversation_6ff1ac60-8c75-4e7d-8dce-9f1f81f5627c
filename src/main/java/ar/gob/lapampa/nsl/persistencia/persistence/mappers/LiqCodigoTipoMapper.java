package ar.gob.lapampa.nsl.persistencia.persistence.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import ar.gob.lapampa.nsl.datatransfer.LiqCodigosTiposDTO;
import ar.gob.lapampa.nsl.persistencia.models.CodigoTipoModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.LiqCodigosTipos;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LiqCodigoTipoMapper {

    LiqCodigoTipoMapper INSTANCE = Mappers.getMapper(LiqCodigoTipoMapper.class);

    // TODO: Ignoramos todas la listas para evitar un dto ciclico ver de mejorar
    //@Mapping(target = "usuarios", ignore = true)
    LiqCodigosTiposDTO liqCodigoTipoToLiqCodigoTipoDto(LiqCodigosTipos liqCodigosTipos);

    static CodigoTipoModel liqCodigoTipoToCodigoTipoModel(LiqCodigosTipos liqCodigosTipos) {
        CodigoTipoModel model = new CodigoTipoModel(liqCodigosTipos.getId());
        model.setDescripcion(liqCodigosTipos.getDescripcion());
        return model;
    }
}
