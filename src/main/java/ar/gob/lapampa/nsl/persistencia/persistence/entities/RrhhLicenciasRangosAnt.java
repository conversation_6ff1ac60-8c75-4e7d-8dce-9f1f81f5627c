package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_LICENCIAS_RANGOS_ANT")
@NamedQueries(value = {@NamedQuery(name = "RrhhLicenciasRangosAnt.findAll",
    query = "SELECT r FROM RrhhLicenciasRangosAnt r")})
public class RrhhLicenciasRangosAnt implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANT_HASTA")
  private short antHasta;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "CANT_DIAS")
  private short cantDias;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANT_DESDE")
  private short antDesde;
  @JoinColumn(name = "LIT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhLicenciasTipos litId;

  public RrhhLicenciasRangosAnt(Long id) {
    this.id = id;
  }

  public RrhhLicenciasRangosAnt(Long id, short antHasta, short cantDias, short antDesde) {
    this.id = id;
    this.antHasta = antHasta;
    this.cantDias = cantDias;
    this.antDesde = antDesde;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhLicenciasRangosAnt[ id=" + id + " ]";
  }

}
