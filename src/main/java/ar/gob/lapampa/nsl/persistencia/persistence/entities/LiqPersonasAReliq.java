package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_PERSONAS_A_RELIQ")
@SequenceGenerator(name = "LIQ_LPR_SEQ", sequenceName = "LIQ_LPR_SEQ", allocationSize = 1)
public class LiqPersonasAReliq implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_LPR_SEQ")
  private Long id;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduPersonas prsId;

  @JoinColumn(name = "PRC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqProcesos prcId;

  public LiqPersonasAReliq(Long id) {
    this.id = id;
  }

}
