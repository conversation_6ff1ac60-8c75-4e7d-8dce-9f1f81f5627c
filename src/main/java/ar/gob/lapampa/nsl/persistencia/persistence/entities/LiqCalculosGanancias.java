package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_CALCULOS_GANANCIAS")
@NamedQueries(value = {@NamedQuery(name = "LiqCalculosGanancias.findAll",
    query = "SELECT l FROM LiqCalculosGanancias l")})
public class LiqCalculosGanancias implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MES")
  private short mes;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Basic(optional = false)
  @Nonnull
  @Column(name = "COTA_INFERIOR")
  private BigDecimal cotaInferior;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "COTA_SUPERIOR")
  private BigDecimal cotaSuperior;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MONTO_FIJO")
  private BigDecimal montoFijo;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PORCENTAJE")
  private BigDecimal porcentaje;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "BASE_PORCENTAJE")
  private BigDecimal basePorcentaje;

  public LiqCalculosGanancias(Long id) {
    this.id = id;
  }

  public LiqCalculosGanancias(Long id, short mes, BigDecimal cotaInferior, BigDecimal cotaSuperior,
      BigDecimal montoFijo, BigDecimal porcentaje, BigDecimal basePorcentaje) {
    this.id = id;
    this.mes = mes;
    this.cotaInferior = cotaInferior;
    this.cotaSuperior = cotaSuperior;
    this.montoFijo = montoFijo;
    this.porcentaje = porcentaje;
    this.basePorcentaje = basePorcentaje;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqCalculosGanancias[ id=" + id + " ]";
  }

}
