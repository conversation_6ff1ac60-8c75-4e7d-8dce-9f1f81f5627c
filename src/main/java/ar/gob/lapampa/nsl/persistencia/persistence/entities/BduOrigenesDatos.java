package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ORIGENES_DATOS")
@NamedQueries(value = {
    @NamedQuery(name = "BduOrigenesDatos.findAll", query = "SELECT b FROM BduOrigenesDatos b")})
public class BduOrigenesDatos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PONDERACION")
  private short ponderacion;
  @Column(name = "EN_LECTURA")
  private Character enLectura;
  @Column(name = "EN_ESCRITURA")
  private Character enEscritura;
  @Column(name = "PUEDE_INSERTAR")
  private Character puedeInsertar;
  @Column(name = "PUEDE_MODIFICAR")
  private Character puedeModificar;
  @Column(name = "PUEDE_BORRAR")
  private Character puedeBorrar;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "odtId", fetch = FetchType.LAZY)
  private Set<BduValoresSoa> bduValoresSoaSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "odtId", fetch = FetchType.LAZY)
  private Set<BduMtdValoresNum> bduMtdValoresNumSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "odtId", fetch = FetchType.LAZY)
  private Set<BduCallesAlias> bduCallesAliasSet;
  @JoinColumn(name = "MTD_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduMetadatos mtdId;
  @JoinColumn(name = "ORG_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduOrigenes orgId;
  @JoinColumn(name = "SIS_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduSistemas sisId;
  @JoinColumn(name = "TBL_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduTablas tblId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "odtId", fetch = FetchType.LAZY)
  private Set<BduPersonasEstados> bduPersonasEstadosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "odtId", fetch = FetchType.LAZY)
  private Set<BduOrganismosPrt> bduOrganismosPrtSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "odtId", fetch = FetchType.LAZY)
  private Set<BduOrganismosOrg> bduOrganismosOrgSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "odtId", fetch = FetchType.LAZY)
  private Set<BduFeriados> bduFeriadosSet;

  public BduOrigenesDatos(Long id) {
    this.id = id;
  }

  public BduOrigenesDatos(Long id, Character activo, short ponderacion) {
    this.id = id;
    this.activo = activo;
    this.ponderacion = ponderacion;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduOrigenesDatos[ id=" + id + " ]";
  }

}
