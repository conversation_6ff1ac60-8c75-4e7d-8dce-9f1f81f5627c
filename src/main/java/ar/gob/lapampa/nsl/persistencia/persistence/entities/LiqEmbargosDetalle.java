package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_EMBARGOS_DETALLE")
public class LiqEmbargosDetalle implements Serializable {

  private static final long serialVersionUID = 1L;

  @Basic(optional = false)
  @Nonnull
  @Id
  @Column(name = "ITEM")
  private Long item;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "MONTO_DESCONTADO")
  private BigDecimal montoDescontado;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "MES_LIQ")
  private Short mesLiq;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANIO_LIQ")
  private Short anioLiq;

  @JoinColumn(name = "EMB_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqEmbargos liqEmbargos;

  @JoinColumn(name = "FOC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqFirmasOcupaciones focId;

  public LiqEmbargosDetalle(BigDecimal montoDescontado, short mesLiq, short anioLiq) {
    this.montoDescontado = montoDescontado;
    this.mesLiq = mesLiq;
    this.anioLiq = anioLiq;
  }

}
