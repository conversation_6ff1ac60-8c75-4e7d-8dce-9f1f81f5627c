package ar.gob.lapampa.nsl.persistencia.controllers;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.datatransfer.request.LiqProcesoRequestDTO;
import ar.gob.lapampa.nsl.datatransfer.response.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.services.LiqProcesoService;
import lombok.NoArgsConstructor;

/**
 * LiqProceso Controller
 *
 * <AUTHOR> NSL
 *
 */
@RestController
@NoArgsConstructor
@RequestMapping("/api/v1/app/procesos")
public class ProcesoController {

  @Autowired
  private LiqProcesoService liqProcesoService;

  @PostMapping("/listar")
  @Transactional
  @PreAuthorize("@cs.hasAccess('','PROC_R','','IS')")
  public @ResponseBody GenericResponseDTO listar(@RequestBody LiqProcesoRequestDTO request) {
    return liqProcesoService.listar(request);
  }
}
