package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_RECONOCIMIENTOS_MEDICOS")
public class RrhhReconocimientosMedicos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "HORA_CONSTATACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date horaConstatacion;
  @Size(max = 4000)
  @Column(name = "DIAGNOSTICO")
  private String diagnostico;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "CDAD_DIAS_AUTORIZADOS")
  private BigInteger cdadDiasAutorizados;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "CERTIFICADO")
  private Character certificado;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "JUNTA_MEDICA")
  private Character juntaMedica;
  @Size(max = 4000)
  @Column(name = "OBSERVACION")
  private String observacion;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRS_ID_MEDICO_AUTORIZANTE")
  private long prsIdMedicoAutorizante;
  @Column(name = "PRS_ID_MEDICO_TRATANTE")
  private Long prsIdMedicoTratante;
  @Column(name = "ATENCION")
  private Character atencion;
  @ManyToMany(mappedBy = "rrhhReconocimientosMedicosSet", fetch = FetchType.LAZY)
  private Set<RrhhDiagnosticos> rrhhDiagnosticosSet;
  @JoinColumn(name = "SAT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhCmuSancionesTipos satId;
  // @JoinColumns({ @JoinColumn(name = "LIC_OCU_ID", referencedColumnName =
  // "OCU_ID"), @JoinColumn(name = "LIC_ITEM", referencedColumnName = "ITEM")
  // })
  // @ManyToOne(optional = false, fetch = FetchType.LAZY)
  // private RrhhLicencias rrhhLicencias

  public RrhhReconocimientosMedicos(Long id) {
    this.id = id;
  }

  public RrhhReconocimientosMedicos(Long id, Date horaConstatacion, BigInteger cdadDiasAutorizados,
      Character certificado, Character juntaMedica, long prsIdMedicoAutorizante) {
    this.id = id;
    this.horaConstatacion = horaConstatacion;
    this.cdadDiasAutorizados = cdadDiasAutorizados;
    this.certificado = certificado;
    this.juntaMedica = juntaMedica;
    this.prsIdMedicoAutorizante = prsIdMedicoAutorizante;
  }

}
