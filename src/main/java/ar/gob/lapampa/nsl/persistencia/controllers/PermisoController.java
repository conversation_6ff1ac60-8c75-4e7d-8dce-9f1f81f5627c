package ar.gob.lapampa.nsl.persistencia.controllers;

import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.AccionRequestDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.PermisoRequestDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.SyncPermisosRequestDTO;
import ar.gob.lapampa.nsl.persistencia.security.CustomSecurityEvaluator;
import ar.gob.lapampa.nsl.persistencia.services.RrhhPermisoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

/**
 * RRHHUsuario Controller
 * 
 * <AUTHOR> NSL
 *
 */
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/v1/app/permisos")
public class PermisoController {

  private final CustomSecurityEvaluator securityEvaluator;

  public PermisoController(CustomSecurityEvaluator securityEvaluator) {
    this.securityEvaluator = securityEvaluator;
  }

  @Autowired
  private RrhhPermisoService permisoService;

  @PostMapping("/listar-permisos-disponibles")
  // @PreAuthorize("hasRole('ADMINISTRADOR')")
  @PreAuthorize("@cs.hasAccess('','USER_READ','','IS')")
  @Transactional
  public GenericResponseDTO listarAccionesDisponibles(@RequestBody AccionRequestDTO request) {
    return permisoService.listarAccionesDisponibles(request);
  }

  @GetMapping("/listar-permisos-por-rol/{rolId}")
  // @PreAuthorize("hasRole('ADMINISTRADOR')")
  @PreAuthorize("@cs.hasAccess('','USER_READ','','IS')")
  @Transactional
  public GenericResponseDTO listarPermisosPorRol(@PathVariable Long rolId) {
    return permisoService.listarAccionesPorRol(rolId);
  }

  @GetMapping("/por-id/{permisoId}")
  // @PreAuthorize("hasRole('ADMINISTRADOR')")
  @PreAuthorize("@cs.hasAccess('','USER_READ','','IS')")
  @Transactional
  public GenericResponseDTO permisoPorId(@PathVariable Long permisoId) {
    return permisoService.permisoPorId(permisoId);
  }

  @PostMapping("/crear-permiso")
  @Transactional
  // @PreAuthorize("hasAuthority('USER_READ') and
  // hasRole('PRESUPUESTO')")
  public @ResponseBody GenericResponseDTO crearPermiso(@RequestBody PermisoRequestDTO request) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("USER_READ", "USER_WRITE");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/permisos/crear-permiso", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return permisoService.crearAccion(request);
  }

  @DeleteMapping("/eliminar-permiso/{permisoId}")
  @Transactional
  // @PreAuthorize("hasAuthority('USER_WRITE') and
  // hasRole('ADMINISTRADOR')")
  public @ResponseBody GenericResponseDTO borrarPermiso(@PathVariable Long permisoId) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("USER_READ", "USER_WRITE");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/permisos/eliminar-permiso", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return permisoService.borrarAccion(permisoId);
  }

  @PutMapping("/modificar-permiso/{permisoId}")
  @Transactional
  // @PreAuthorize("hasAuthority('USER_WRITE') and
  // hasRole('ADMINISTRADOR')")
  public @ResponseBody GenericResponseDTO modificarPermiso(@RequestBody PermisoRequestDTO request,
      @PathVariable Long permisoId) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("USER_READ", "USER_WRITE");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/permisos/modificar-permiso", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return permisoService.modificarPermiso(request, permisoId);
  }

  @PutMapping("/modificar-por-rolId/{rolId}")
  @Transactional
  // @PreAuthorize("hasAuthority('USER_WRITE') and
  // hasRole('ADMINISTRADOR')")
  public @ResponseBody GenericResponseDTO modificarPermisosPorRol(
      @RequestBody SyncPermisosRequestDTO request, @PathVariable Long rolId) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("USER_READ", "USER_WRITE");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/permisos/modificar-por-rolId", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return permisoService.actualizarPermisosByRol(request.getPermisos(), rolId);
  }


  @Operation(summary = "Listado directo de permisos / acciones de usuarios RRHH",
      description = "No recibe  parámetros", tags = {"Usuarios"}, hidden = false,
      deprecated = false)
  @ApiResponses({@ApiResponse(responseCode = "200", description = "Listado completo"),
      @ApiResponse(responseCode = "400", description = "Bad Request")})
  @GetMapping("/listar-permisos-legacy")
  @Transactional
  // @PreAuthorize("hasAuthority('PERMISSION_USERS_READ') and
  // hasRole('PRESUPUESTO')")
  public @ResponseBody GenericResponseDTO listarPermisoLegacy() {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    Set<String> permisosRequeridos = Set.of("USER_READ", "USER_WRITE");
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/permisos/listar-permisos-legacy",
        authentication, rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso

    return permisoService.listarAccionesLegacy();
  }

}
