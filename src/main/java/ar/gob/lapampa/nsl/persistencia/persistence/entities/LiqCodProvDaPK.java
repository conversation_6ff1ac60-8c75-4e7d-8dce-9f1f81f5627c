package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class LiqCodProvDaPK implements Serializable {

  private static final long serialVersionUID = 4695918727864210550L;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "COD_ID")
  private long codId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PROV_ID")
  private long provId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "DAD_ID")
  private long dadId;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqCodProvDaPK[ codId=" + codId + ", provId="
        + provId + ", dadId=" + dadId + " ]";
  }

}
