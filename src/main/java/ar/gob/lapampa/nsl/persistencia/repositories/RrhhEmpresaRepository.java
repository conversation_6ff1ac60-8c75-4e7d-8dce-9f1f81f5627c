package ar.gob.lapampa.nsl.persistencia.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhEmpresa;

public interface RrhhEmpresaRepository
    extends JpaRepository<RrhhEmpresa, Long>, QuerydslPredicateExecutor<RrhhEmpresa> {

  @Procedure(procedureName = "LAPAMPA.SYNC_RRHH_EMPRESAS_ROLES")
  String syncEmpresasRoles(@Param("p_rrhh_rol_id") Long rrhhRolId,
      @Param("p_empresa_list") String empresasList);
}
