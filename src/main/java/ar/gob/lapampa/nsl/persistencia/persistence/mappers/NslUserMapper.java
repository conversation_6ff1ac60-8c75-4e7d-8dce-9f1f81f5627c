package ar.gob.lapampa.nsl.persistencia.persistence.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import ar.gob.lapampa.nsl.persistencia.models.NslUserModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.NslUsuarios;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NslUserMapper {

  NslUserMapper INSTANCE = Mappers.getMapper(NslUserMapper.class);

  NslUserModel NslUsuariosToNslUserDTO(NslUsuarios user);

  static NslUserModel NslUsuariosToNslUserModel(NslUsuarios user) {
    NslUserModel model = new NslUserModel(user.getId());
    model.setUsername(user.getUsername());
    model.setEmail(user.getEmail());
    model.setPassword(user.getPassword());
    
    // Agregar nombre y apellido si la persona existe
    if (user.getPersona() != null) {
      model.setApellidos(user.getPersona().getApellido());
      model.setNombres(user.getPersona().getNombres());
    }
    
    return model;
  }

}
