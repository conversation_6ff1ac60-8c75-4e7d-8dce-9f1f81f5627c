package ar.gob.lapampa.nsl.persistencia.models;

import java.io.Serializable;
import com.querydsl.core.annotations.QueryProjection;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CodigoTipoModel extends BaseRRHHDTO implements Serializable{

    String descripcion;
    Long cotPadreId;

    @QueryProjection
    public CodigoTipoModel(Long id) {
        super();
        this.id = id;
    }

    /**
     * Obtiene Nombre de class name solo
     *
     * @return String
     */
    @Override
    public String getBeanName() {
        return CodigoTipoModel.class.getSimpleName();
    }

    /**
     * Obtiene Nombre de package + class name
     *
     * @return String
     */
    @Override
    public String getCualifiedName() {
        return CodigoTipoModel.class.getCanonicalName();
    }

}
