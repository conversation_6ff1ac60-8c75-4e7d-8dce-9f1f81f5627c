package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ACTORES_EVENTOS")
@NamedQueries(value ={
    @NamedQuery(name = "BduActoresEventos.findAll", query = "SELECT b FROM BduActoresEventos b")})
public class BduActoresEventos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;
  @Column(name = "PRF_ID")
  private Long prfId;
  @Size(max = 400)
  @Column(name = "OBSERVACIONES")
  private String observaciones;
  @Size(max = 1000)
  @Column(name = "USUARIO_MOV")
  private String usuarioMov;
  @Column(name = "FECHA_MOV")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaMov;
  @JoinColumn(name = "ACT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduActores actId;
  @JoinColumn(name = "USUARIO", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduActores usuario;
  @JoinColumn(name = "TEV_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduEventosTipos tevId;

  public BduActoresEventos(Long id) {
    this.id = id;
  }

  public BduActoresEventos(Long id, Date fecha) {
    this.id = id;
    this.fecha = fecha;
  }

}
