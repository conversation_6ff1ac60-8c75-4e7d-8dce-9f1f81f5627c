package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_TABLAS")
@NamedQueries(
    value = {@NamedQuery(name = "BduTablas.findAll", query = "SELECT b FROM BduTablas b")})
public class BduTablas implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "ORIGEN_DATO")
  private Character origenDato;
  @Column(name = "ACTIVO")
  private Character activo;
  @Column(name = "FECHA_DETECTADA_BAJA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDetectadaBaja;
  @Column(name = "TIPO_TABLA")
  private Character tipoTabla;
  @Column(name = "FECHA_ALTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAlta;
  @Column(name = "FECHA_MODIFICACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaModificacion;
  @Column(name = "FECHA_MOD_CAMPOS")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaModCampos;
  @Column(name = "AUDITA_BD")
  private Short auditaBd;
  @JoinColumn(name = "ESQ_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduEsquemas esqId;
  @JoinColumn(name = "SEM_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduSemanticas semId;
  @OneToMany(mappedBy = "tblId", fetch = FetchType.LAZY)
  private Set<BduOrigenesDatos> bduOrigenesDatosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "tblId", fetch = FetchType.LAZY)
  private Set<BduMetadatos> bduMetadatosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "tblId", fetch = FetchType.LAZY)
  private Set<BduPermisosTablas> bduPermisosTablasSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "tblId", fetch = FetchType.LAZY)
  private Set<BduCampos> bduCamposSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "tblId", fetch = FetchType.LAZY)
  private Set<BduReglasUsos> bduReglasUsosSet;

  public BduTablas(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduTablas[ id=" + id + " ]";
  }

}
