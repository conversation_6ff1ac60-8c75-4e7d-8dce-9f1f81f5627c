package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ATR_COMPRT")
@NamedQueries(value ={@NamedQuery(name = "BduAtrComprt.findAll", query = "SELECT b FROM BduAtrComprt b")})
public class BduAtrComprt implements Serializable {

  private static final long serialVersionUID = 1417833207088077208L;

  @EmbeddedId
  protected BduAtrComprtPK bduAtrComprtPK;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @JoinColumn(name = "ATR_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduAtributos bduAtributos;

  public BduAtrComprt(BduAtrComprtPK bduAtrComprtPK) {
    this.bduAtrComprtPK = bduAtrComprtPK;
  }

  public BduAtrComprt(BduAtrComprtPK bduAtrComprtPK, String descripcion) {
    this.bduAtrComprtPK = bduAtrComprtPK;
    this.descripcion = descripcion;
  }

  public BduAtrComprt(long atrId, long valor) {
    bduAtrComprtPK = new BduAtrComprtPK(atrId, valor);
  }

}
