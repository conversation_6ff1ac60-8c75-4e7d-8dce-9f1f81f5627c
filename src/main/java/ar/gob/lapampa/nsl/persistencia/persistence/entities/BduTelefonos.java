package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_TELEFONOS")
@NamedQueries(
    value = {@NamedQuery(name = "BduTelefonos.findAll", query = "SELECT b FROM BduTelefonos b")})
public class BduTelefonos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 4)
  @Column(name = "COD_PAIS")
  private String codPais;
  @Size(max = 4)
  @Column(name = "COD_AREA")
  private String codArea;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 10)
  @Column(name = "NUMERO_TEL")
  private String numeroTel;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @Size(max = 10)
  @Column(name = "NUMERO_INT")
  private String numeroInt;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduTelefonos", fetch = FetchType.LAZY)
  private Set<BduPersonasTelefonos> bduPersonasTelefonosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "telId", fetch = FetchType.LAZY)
  private Set<BduOrgFncTelefonos> bduOrgFncTelefonosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "telId", fetch = FetchType.LAZY)
  private Set<BduOrgPreTelefonos> bduOrgPreTelefonosSet;
  @JoinColumn(name = "TTL_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduTelefonosTipos ttlId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "telId", fetch = FetchType.LAZY)
  private Set<BduOrgOroTelefonos> bduOrgOroTelefonosSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "telId", fetch = FetchType.LAZY)
  private Set<BduOrgPrtTelefonos> bduOrgPrtTelefonosSet;

  public BduTelefonos(Long id) {
    this.id = id;
  }

  public BduTelefonos(Long id, String numeroTel, Character activo) {
    this.id = id;
    this.numeroTel = numeroTel;
    this.activo = activo;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduTelefonos[ id=" + id + " ]";
  }

}
