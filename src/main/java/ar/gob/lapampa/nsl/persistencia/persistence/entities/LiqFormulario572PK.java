package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class LiqFormulario572PK implements Serializable {

  private static final long serialVersionUID = 726324043299573317L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "DED_ID")
  private long dedId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PRS_ID")
  private long prsId;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqFormulario572PK[ dedId=" + dedId + ", prsId="
        + prsId + " ]";
  }

}
