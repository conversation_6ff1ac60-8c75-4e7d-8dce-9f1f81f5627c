package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ORGANISMOS_PRE")
@SequenceGenerator(name = "BDU_ORP_SEQ", sequenceName = "BDU_ORP_SEQ", allocationSize = 1)
public class BduOrganismosPre implements Serializable {

  private static final long serialVersionUID = 1842712143513983515L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BDU_ORP_SEQ")
  private Long id;

  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Column(name = "CODIGO")
  private String codigo;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ODT_ID")
  private long odtId;

  @Size(max = 32)
  @Column(name = "TABLA_SIAF")
  private String tablaSiaf;

  @Column(name = "ID_TABLA_SIAF")
  private Long idTablaSiaf;

  @Column(name = "DMC_ID")
  private Long dmcId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDesde;

  @Column(name = "NVL_ID")
  private Long nvlId;

  @Column(name = "ORP_ID_BASE")
  private Long orpIdBase;

  @OneToMany(mappedBy = "orpId", fetch = FetchType.LAZY)
  private Set<BduOrganismosPre> bduOrganismosPreSet;

  @JoinColumn(name = "ORP_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduOrganismosPre orpId;

  @JoinColumn(name = "BUE_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduUnidadEjecutora bueId;

  @JoinColumn(name = "EMP_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.LAZY)
  private RrhhEmpresa empId;

  @Column(name = "FECHA_VIG_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigDesde;

  @Column(name = "FECHA_VIG_HASTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigHasta;

  public BduOrganismosPre(Long id) {
    this.id = id;
  }

}
