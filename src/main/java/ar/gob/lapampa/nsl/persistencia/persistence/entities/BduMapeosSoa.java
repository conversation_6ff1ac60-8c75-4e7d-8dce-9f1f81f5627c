package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_MAPEOS_SOA")
@NamedQueries(
    value = {@NamedQuery(name = "BduMapeosSoa.findAll", query = "SELECT b FROM BduMapeosSoa b")})
public class BduMapeosSoa implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "TBL_ID")
  private long tblId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "TBL_EXT")
  private long tblExt;
  @Column(name = "COMPORTAMIENTO")
  private Long comportamiento;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "mpsId", fetch = FetchType.LAZY)
  private Set<BduValoresSoa> bduValoresSoaSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "mpsId", fetch = FetchType.LAZY)
  private Set<BduSoaEventosTipos> bduSoaEventosTiposSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "mpsId", fetch = FetchType.LAZY)
  private Set<BduSoaVuelcos> bduSoaVuelcosSet;

  public BduMapeosSoa(Long id) {
    this.id = id;
  }

  public BduMapeosSoa(Long id, long tblId, long tblExt) {
    this.id = id;
    this.tblId = tblId;
    this.tblExt = tblExt;
  }

  public Long getId() {
    return id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduMapeosSoa[ id=" + id + " ]";
  }

}
