package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_CODIGOS_VERSIONES")
@SequenceGenerator(name = "LIQ_COV_SEQ", sequenceName = "LIQ_COV_SEQ", allocationSize = 1)
public class LiqCodigosVersiones implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_COV_SEQ")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ITEM")
  private Long item;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "REMUNERATIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean remunerativo;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "COMPUTABLE_MINIMO")
  @Convert(converter = YesNoConverter.class)
  private Boolean computableMinimo;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "COMPUTABLE_SAC")
  private Character computableSac;

  @Column(name = "PERDIDAS_PROPORCIONALES")
  private Character perdidasProporcionales;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "OFICIAL")
  private Character oficial;

  @Column(name = "MONTO_FIJO")
  private BigDecimal montoFijo;

  @Column(name = "TOPE_FIJO")
  private BigDecimal topeFijo;

  @Size(max = 4000)
  @Column(name = "COND_PERCEPCION")
  private String condPercepcion;

  @Size(max = 4000)
  @Column(name = "COND_VARIABLES_MONTO")
  private String condVariablesMonto;

  @Size(max = 4000)
  @Column(name = "FORMULA_MONTO")
  private String formulaMonto;

  @Size(max = 4000)
  @Column(name = "FORMULA_TOPE")
  private String formulaTope;

  @Size(max = 50)
  @Column(name = "CDAD")
  private String cdad;

  @Size(max = 4000)
  @Column(name = "OBSERVACIONES")
  private String observaciones;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "VALOR_PTO")
  private BigDecimal valorPto;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "COMPUTABLE_GANANCIAS")
  @Convert(converter = YesNoConverter.class)
  private Boolean computableGanancias;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "COMPUTABLE_SEG_VIDA")
  @Convert(converter = YesNoConverter.class)
  private Boolean computableSegVida;

  @Column(name = "TIPO_CALCULO_MONTO")
  private Long tipoCalculoMonto;

  @Column(name = "TIPO_CALCULO_TOPE")
  private Long tipoCalculoTope;

  @ManyToMany(mappedBy = "liqCodigosVersionesSet", fetch = FetchType.LAZY)
  private List<LiqSetVersiones> liqSetVersionesSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "covId", fetch = FetchType.LAZY)
  private Set<LiqFirmasDetalle> liqFirmasDetalleSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "covId", fetch = FetchType.LAZY)
  private Set<LiqBasicosCategorias> liqBasicosCategoriasSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "covId", fetch = FetchType.LAZY)
  private Set<LiqCalculoCod6> liqCalculoCod6Set;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "covId", fetch = FetchType.LAZY)
  private Set<LiqCodVerIncomp> liqCodVerIncompSet;

  @JoinColumn(name = "COD_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private LiqCodigos codId;

  public LiqCodigosVersiones(Long id) {
    this.id = id;
  }

}
