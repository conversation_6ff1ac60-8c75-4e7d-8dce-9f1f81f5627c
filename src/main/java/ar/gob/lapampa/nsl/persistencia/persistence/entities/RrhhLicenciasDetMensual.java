package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_LICENCIAS_DET_MENSUAL")
@NamedQueries(value = {@NamedQuery(name = "RrhhLicenciasDetMensual.findAll",
    query = "SELECT r FROM RrhhLicenciasDetMensual r")})
public class RrhhLicenciasDetMensual implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected RrhhLicenciasDetMensualPK rrhhLicenciasDetMensualPK;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "CANT_DIAS")
  private long cantDias;
  // @JoinColumns({
  // @JoinColumn(name = "LIC_OCU_ID", referencedColumnName = "OCU_ID",
  // insertable = false, updatable = false),
  // @JoinColumn(name = "LIC_ITEM", referencedColumnName = "ITEM", insertable
  // = false, updatable = false)})
  // @ManyToOne(optional = false, fetch = FetchType.LAZY)
  // private RrhhLicencias rrhhLicencias;

  public RrhhLicenciasDetMensual(RrhhLicenciasDetMensualPK rrhhLicenciasDetMensualPK) {
    this.rrhhLicenciasDetMensualPK = rrhhLicenciasDetMensualPK;
  }

  public RrhhLicenciasDetMensual(RrhhLicenciasDetMensualPK rrhhLicenciasDetMensualPK,
      long cantDias) {
    this.rrhhLicenciasDetMensualPK = rrhhLicenciasDetMensualPK;
    this.cantDias = cantDias;
  }

  public RrhhLicenciasDetMensual(long licOcuId, long licItem, short anio, short mes) {
    this.rrhhLicenciasDetMensualPK = new RrhhLicenciasDetMensualPK(licOcuId, licItem, anio, mes);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhLicenciasDetMensual[ rrhhLicenciasDetMensualPK="
        + rrhhLicenciasDetMensualPK + " ]";
  }

}
