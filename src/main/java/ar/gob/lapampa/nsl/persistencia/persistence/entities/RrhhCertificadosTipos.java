package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_CERTIFICADOS_TIPOS")
@NamedQueries(value = {@NamedQuery(name = "RrhhCertificadosTipos.findAll",
    query = "SELECT r FROM RrhhCertificadosTipos r")})
public class RrhhCertificadosTipos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @OneToMany(mappedBy = "crtId", fetch = FetchType.LAZY)
  private Set<RrhhCapacitaciones> rrhhCapacitacionesSet;

  public RrhhCertificadosTipos(Long id) {
    this.id = id;
  }

  public RrhhCertificadosTipos(Long id, Character activo) {
    this.id = id;
    this.activo = activo;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhCertificadosTipos[ id=" + id + " ]";
  }

}
