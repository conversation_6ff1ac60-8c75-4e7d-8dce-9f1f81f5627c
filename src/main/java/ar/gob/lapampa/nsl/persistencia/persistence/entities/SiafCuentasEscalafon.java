package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "SIAF_CUENTAS_ESCALAFON")
@NamedQueries(value = {@NamedQuery(name = "SiafCuentasEscalafon.findAll",
    query = "SELECT s FROM SiafCuentasEscalafon s")})
public class SiafCuentasEscalafon implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @JoinColumn(name = "COD_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqCodigos codId;
  @JoinColumn(name = "ESC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PreEscalafones escId;
  @JoinColumn(name = "PLT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private PrePlantasTipos pltId;
  @JoinColumn(name = "CUP_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private SiafCuentasPresupuestarias cupId;

  public SiafCuentasEscalafon(Long id) {
    this.id = id;
  }

}
