package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class BduOrgOroVinRetPK implements Serializable {

  private static final long serialVersionUID = -342685064026013119L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ORO_ID_ORIGEN")
  private long oroIdOrigen;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ORO_ID_DESTINO")
  private long oroIdDestino;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "RET_ID")
  private long retId;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduOrgOroVinRetPK[ oroIdOrigen=" + oroIdOrigen
        + ", oroIdDestino=" + oroIdDestino + ", retId=" + retId + " ]";
  }

}
