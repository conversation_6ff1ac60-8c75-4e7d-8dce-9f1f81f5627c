package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_METADATOS_DOMINIOS")
@NamedQueries(value = {@NamedQuery(name = "BduMetadatosDominios.findAll",
    query = "SELECT b FROM BduMetadatosDominios b")})
public class BduMetadatosDominios implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @JoinColumn(name = "DMN_ID", referencedColumnName = "ID")
  @OneToOne(fetch = FetchType.LAZY)
  private BduDominios dmnId;
  @JoinColumn(name = "ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @OneToOne(optional = false, fetch = FetchType.LAZY)
  private BduMetadatos bduMetadatos;

  public BduMetadatosDominios(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduMetadatosDominios[ id=" + id + " ]";
  }

}
