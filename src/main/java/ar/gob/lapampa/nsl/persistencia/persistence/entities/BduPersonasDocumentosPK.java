package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class BduPersonasDocumentosPK implements Serializable {

  private static final long serialVersionUID = 3945518498967487656L;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "TDC_ID")
  private long tdcId;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 32)
  @Column(name = "NUMERO")
  private String numero;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "OCURRENCIA")
  private short ocurrencia;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduPersonasDocumentosPK[ tdcId=" + tdcId
        + ", numero=" + numero + ", ocurrencia=" + ocurrencia + " ]";
  }

}
