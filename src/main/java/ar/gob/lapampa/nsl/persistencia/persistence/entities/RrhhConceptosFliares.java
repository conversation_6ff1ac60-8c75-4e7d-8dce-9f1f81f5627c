package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_CONCEPTOS_FLIARES")
@SequenceGenerator(name = "RRHH_COF_SEQ", sequenceName = "RRHH_COF_SEQ", allocationSize = 1)
public class RrhhConceptosFliares implements Serializable {

  private static final long serialVersionUID = 564002988800959623L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_COF_SEQ")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_VIG_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigDesde;

  @Column(name = "FECHA_VIG_HASTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigHasta;

  @JoinColumn(name = "VNC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.EAGER)
  private BduVinculos vncId;

  @Column(name = "FECHA_EVENTO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaEvento;

  @Column(name = "FECHA_APROBACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAprobacion;

  @Size(max = 4000)
  @Column(name = "COMENTARIO")
  private String comentario;

  @Column(name = "FECHA_ANULACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAnulacion;

  @Column(name = "ESTADO")
  private String estado;

  @Column(name = "FECHA_CIERRE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaCierre;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;

  @Basic(optional = false)
  @Column(name = "CERTIFICADO")
  @Convert(converter = YesNoConverter.class)
  private Boolean certificado;

  @Basic(optional = false)
  @Column(name = "FECHA_CERTIFICADO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaCertificado;

  @Basic(optional = false)
  @Column(name = "ANIO_CERTIFICADO")
  private Long anioCertificado;

  @Basic(optional = false)
  @Column(name = "ORIGEN")
  private String origen;

  @Basic(optional = false)
  @Column(name = "CUISE")
  private Long cuise;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduPersonas prsId;

  @JoinColumn(name = "CFT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhConceptosFliaresTipos cftId;

  @JoinColumn(name = "OCU_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private RrhhOcupaciones ocuId;

  @JoinColumn(name = "EST_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.LAZY)
  private RrhhEstablecimientos estId;

  @Column(name = "GRADO", nullable = true)
  private Integer grado;

  public RrhhConceptosFliares(Long id) {
    this.id = id;
  }

}
