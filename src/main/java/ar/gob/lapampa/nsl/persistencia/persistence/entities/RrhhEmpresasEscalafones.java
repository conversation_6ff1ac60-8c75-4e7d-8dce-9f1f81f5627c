package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_EMPRESAS_ESCALAFONES")
public class RrhhEmpresasEscalafones implements Serializable {

    private static final long serialVersionUID = -7154314118947109209L;

    @JoinColumn(name = "EMP_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private RrhhEmpresa empresa;

    @JoinColumn(name = "ESC_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private PreEscalafones convenio;
    @Id
    private Long id;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
}
