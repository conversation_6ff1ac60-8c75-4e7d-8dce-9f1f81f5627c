package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_MTD_VALORES_NUM")
@NamedQueries(value = {
    @NamedQuery(name = "BduMtdValoresNum.findAll", query = "SELECT b FROM BduMtdValoresNum b")})
public class BduMtdValoresNum implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID_PRIMARIO")
  private long idPrimario;
  @Column(name = "VALOR")
  private BigInteger valor;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;
  @Size(max = 1000)
  @Column(name = "USUARIO_MOV")
  private String usuarioMov;
  @Column(name = "FECHA_MOV")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaMov;
  @JoinColumn(name = "MTD_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduMetadatos mtdId;
  @JoinColumn(name = "ODT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduOrigenesDatos odtId;

  public BduMtdValoresNum(Long id) {
    this.id = id;
  }

  public BduMtdValoresNum(Long id, long idPrimario, Date fecha) {
    this.id = id;
    this.idPrimario = idPrimario;
    this.fecha = fecha;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduMtdValoresNum[ id=" + id + " ]";
  }

}
