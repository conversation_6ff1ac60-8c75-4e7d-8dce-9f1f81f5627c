package ar.gob.lapampa.nsl.persistencia.models;

import java.io.Serializable;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ListadoDetalleConceptosConveniosModel extends BaseRRHHDTO implements Serializable {

    private static final long serialVersionUID = 6251378807197719736L;

    private Long convenioNro;
    private String convenioNombre;
    private String codigoNro;
    private String concepto;
    private Double haberes;
    private Double descuentos;
    private String codigoEmpresa;
    private String descripcionEmpresa;
    private Long afiliado;
    private Long ocuId;
    private String apellido;
    private String nombre;
    private Integer mesRetroactivo;
    private Integer anioRetroactivo;
    /**
     * Obtiene Nombre de class name solo
     *
     * @return String
     */
    @Override
    public String getBeanName() {
        return ListadoDetalleConceptosConveniosModel.class.getSimpleName();
    }

    /**
     * Obtiene Nombre de package + class name
     *
     * @return String
     */
    @Override
    public String getCualifiedName() {
        return ListadoDetalleConceptosConveniosModel.class.getCanonicalName();
    }

}
