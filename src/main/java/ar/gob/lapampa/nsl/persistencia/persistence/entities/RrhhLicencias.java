package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_LICENCIAS")
public class RrhhLicencias implements Serializable {

  private static final long serialVersionUID = 3081497230881169022L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Basic(optional = false)
  @Column(name = "FECHA_VIG_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigDesde;

  @Column(name = "FECHA_VIG_HASTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigHasta;

  @Column(name = "ANIO")
  private Short anio;

  @Column(name = "HORA_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date horaDesde;

  @Column(name = "HORA_HASTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date horaHasta;

  @Column(name = "CANT_DIAS_AUTORIZADOS")
  private Long cantDiasAutorizados;

  @Column(name = "NOR_ID")
  private Long norId;

  @Column(name = "PER_FAMILIAR_ID")
  private Long perFamiliarId;

  @Column(name = "RECONOCIM_APLICADO")
  private String reconocimAplicado;

  @Column(name = "FECHA_APROBACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAprobacion;

  @Column(name = "ESTADO")
  private String estado;

  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;

  @Column(name = "COMENTARIO")
  private String comentario;

  @Column(name = "FECHA_ANULACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAnulacion;

  @Column(name = "FECHA_CIERRE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaCierre;

  @Column(name = "DOMICILIO_REC")
  private String domicilioRec;

  @Column(name = "SOLICITA_JM")
  private String solicitaJm;

  @Column(name = "LIC_ID_SOA")
  private Long licIdSoa;

  @Column(name = "NOR_NRO")
  private Long norNro;

  @Column(name = "ANIO_IMPUTACION")
  private Short anioImputacion;

  @Column(name = "COMENTARIO_APROBACION")
  private String comentarioAprobacion;

  @Basic(optional = false)
  @Column(name = "CDAD_OBLIGACIONES")
  private BigDecimal cantidadObligaciones;

  @JoinColumn(name = "LTI_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhLicenciasTipos ltiId;

  @JoinColumn(name = "LST_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhLicenciasSubTipos lstId;

  @JoinColumn(name = "LRE_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhLugaresReconocimientos lreId;

  @JoinColumn(name = "OCU_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhOcupaciones ocuId;

}
