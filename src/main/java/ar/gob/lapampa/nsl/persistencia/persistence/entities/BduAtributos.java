package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ATRIBUTOS")
@NamedQueries(
    value = {@NamedQuery(name = "BduAtributos.findAll", query = "SELECT b FROM BduAtributos b")})
public class BduAtributos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MAPEADO_SOA")
  private Character mapeadoSoa;
  @Column(name = "MODO_SOA")
  private Short modoSoa;
  @JoinColumn(name = "SEM_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduSemanticas semId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduAtributos", fetch = FetchType.LAZY)
  private Set<BduAtrComprt> bduAtrComprtSet;
  @OneToOne(cascade = CascadeType.ALL, mappedBy = "bduAtributos", fetch = FetchType.LAZY)
  private BduMetadatos bduMetadatos;
  @OneToOne(cascade = CascadeType.ALL, mappedBy = "bduAtributos", fetch = FetchType.LAZY)
  private BduCampos bduCampos;

  public BduAtributos(Long id) {
    this.id = id;
  }

  public BduAtributos(Long id, Character mapeadoSoa) {
    this.id = id;
    this.mapeadoSoa = mapeadoSoa;
  }

}
