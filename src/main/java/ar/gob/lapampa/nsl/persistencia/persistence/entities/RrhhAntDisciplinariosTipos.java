package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_ANT_DISCIPLINARIOS_TIPOS")
@NamedQueries(value = {@NamedQuery(name = "RrhhAntDisciplinariosTipos.findAll",
    query = "SELECT r FROM RrhhAntDisciplinariosTipos r")})
public class RrhhAntDisciplinariosTipos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Column(name = "PORC_GOCE_HABERES")
  private BigDecimal porcGoceHaberes;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @JoinTable(name = "RRHH_ESC_ANT_DISC_TIPOS",
      joinColumns = {@JoinColumn(name = "ADT_ID", referencedColumnName = "ID")},
      inverseJoinColumns = {@JoinColumn(name = "ESC_ID", referencedColumnName = "ID")})
  @ManyToMany(fetch = FetchType.LAZY)
  private Set<PreEscalafones> preEscalafonesSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "adtId", fetch = FetchType.LAZY)
  private Set<RrhhAntDisciplinarios> rrhhAntDisciplinariosSet;

  public RrhhAntDisciplinariosTipos(Long id) {
    this.id = id;
  }

  public RrhhAntDisciplinariosTipos(Long id, Character activo) {
    this.id = id;
    this.activo = activo;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhAntDisciplinariosTipos[ id=" + id + " ]";
  }

}
