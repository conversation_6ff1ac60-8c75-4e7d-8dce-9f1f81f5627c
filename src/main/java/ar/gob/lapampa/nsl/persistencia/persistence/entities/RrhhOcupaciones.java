package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_OCUPACIONES")
public class RrhhOcupaciones implements Serializable {

  private static final long serialVersionUID = 221717654943902890L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private BduPersonasFisicas prsId;

  @JoinColumn(name = "PLT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PrePlantasTipos pltId;

  @JoinColumn(name = "JOT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhJornadasTipos rrhhJornadasTipos;

  @Column(name = "FECHA_BAJA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaBaja;

  @JoinColumn(name = "MBA_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhMotivosBaja rrhhMotivosBaja;

  @Column(name = "LEGAJO")
  private Long legajo;

  @Column(name = "FECHA_ALTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAlta;

  @JoinColumn(name = "ESC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private PreEscalafones escId;

  @JoinColumn(name = "CAT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private PreCategorias catId;

  @JoinColumn(name = "EST_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhEstablecimientos estId;

  @JoinColumn(name = "FUN_ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhFunciones funId;

  @Column(precision = 10, scale = 2)
  private BigDecimal monto;

  @Column(name = "CDAD_HORAS", precision = 7)
  private BigDecimal cdadHoras;

  @JoinColumn(name = "CEQ_CAT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PreCategorias ceqCatId;

  @Column(name = "FUNCION")
  private Long funcion;

  @JoinColumn(name = "PRE_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PrePresupuestos preId;

  @JoinColumn(name = "OCU_REEMP_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhOcupaciones ocuReempId;

  @JoinColumn(name = "AFE_OCU_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private RrhhOcupaciones afeOcuId;

  @Column(length = 2000)
  private String observaciones;

  @Column(name = "SRV_ID")
  private Long srvId;

  @Column(name = "OBSERVACIONES_RECIBOS", length = 4000)
  private String observacionesRecibos;

  @Column(length = 2)
  private String auxiliar;

  @JoinColumn(name = "EMP_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private RrhhEmpresa empId;

  @OneToMany(mappedBy = "ocuId", fetch = FetchType.LAZY)
  private List<RrhhOcupacionesConceptos> rrhhOcupacionesConceptos;

  @OneToMany(mappedBy = "ocuId", fetch = FetchType.LAZY)
  private List<RrhhOcupacionesTareas> rrhhOcupacionesTareas;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "ocuId", fetch = FetchType.LAZY)
  private List<RrhhOcupacionesMov> rrhhOcupacionesMov;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "ocuId", fetch = FetchType.LAZY)
  private List<RrhhConceptosFliares> rrhhConceptosFliares;

  @JoinColumn(name = "UOR_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduOrganismosPre uorId;

  public RrhhOcupaciones(BduPersonasFisicas prsId) {

    this.prsId = prsId;
  }

}
