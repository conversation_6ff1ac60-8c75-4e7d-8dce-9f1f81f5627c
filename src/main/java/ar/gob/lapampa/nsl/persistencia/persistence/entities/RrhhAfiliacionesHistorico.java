package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_AFILIACIONES_HISTORICO")
@NamedQueries(value = {@NamedQuery(name = "RrhhAfiliacionesHistorico.findAll",
    query = "SELECT r FROM RrhhAfiliacionesHistorico r")})
public class RrhhAfiliacionesHistorico implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Column(name = "AFI_ID")
  private Long afiId;
  @Size(max = 50)
  @Column(name = "ACCION")
  private String accion;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_HIST")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaHist;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 20)
  @Column(name = "USUARIO")
  private String usuario;
  @Column(name = "FECHA_ALTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAlta;
  @Column(name = "FECHA_BAJA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaBaja;

  public RrhhAfiliacionesHistorico(Long id) {
    this.id = id;
  }

  public RrhhAfiliacionesHistorico(Long id, Date fechaHist, String usuario) {
    this.id = id;
    this.fechaHist = fechaHist;
    this.usuario = usuario;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhAfiliacionesHistorico[ id=" + id + " ]";
  }

}
