package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class BduOrgFncVinRetPK implements Serializable {

  private static final long serialVersionUID = 4283074365671789239L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ORF_ID_ORIGEN")
  private long orfIdOrigen;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ORF_ID_DESTINO")
  private long orfIdDestino;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "RET_ID")
  private long retId;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduOrgFncVinRetPK[ orfIdOrigen=" + orfIdOrigen
        + ", orfIdDestino=" + orfIdDestino + ", retId=" + retId + " ]";
  }

}
