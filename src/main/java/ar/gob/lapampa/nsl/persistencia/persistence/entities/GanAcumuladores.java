package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_ACUMULADORES")
public class GanAcumuladores implements Serializable {

  private static final long serialVersionUID = -7759855598255770958L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Column(name = "CODIGO")
  private String codigo;

  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Column(name = "NOMBRE_CORTO")
  private String nombreCorto;

  @Column(name = "TIPO")
  private Long tipo;

  @Column(name = "FECHA_VIGENCIA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;

  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @OneToOne(fetch = FetchType.LAZY, mappedBy = "acumulador")
  private GanAcumuladoresInfo acumuladoresInfo;

}
