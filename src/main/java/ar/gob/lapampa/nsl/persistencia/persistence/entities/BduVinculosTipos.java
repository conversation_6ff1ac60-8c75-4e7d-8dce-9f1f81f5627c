package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.List;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_VINCULOS_TIPOS")
public class BduVinculosTipos implements Serializable {

  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Column(name = "PARA_PERSONA_FIS")
  private Character paraPersonaFis;

  @Column(name = "PARA_PERSONA_JUR")
  private Character paraPersonaJur;

  @Column(name = "COMPORTAMIENTO")
  private Long comportamiento;

  @Size(max = 200)
  @Column(name = "DESCRIPCION_INVERSA")
  private String descripcionInversa;

  @JoinColumn(name = "GRV_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduVinculosGrupos grvId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "BIDIRECCIONAL")
  @Convert(converter = YesNoConverter.class)
  private Boolean bidireccional;

  @OneToMany(mappedBy = "tvnId", fetch = FetchType.LAZY)
  private List<BduVinculos> vinculosList;

  public BduVinculosTipos(Long id) {
    this.id = id;
  }

}
