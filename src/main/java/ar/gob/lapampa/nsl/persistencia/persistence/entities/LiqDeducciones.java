package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_DEDUCCIONES")
@NamedQueries(value = {
    @NamedQuery(name = "LiqDeducciones.findAll", query = "SELECT l FROM LiqDeducciones l")})
public class LiqDeducciones implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MONTO")
  private BigDecimal monto;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "TIPO")
  private short tipo;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "liqDeducciones", fetch = FetchType.LAZY)
  private Set<LiqDeduccionesPersonasDet> liqDeduccionesPersonasDetSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "liqDeducciones", fetch = FetchType.LAZY)
  private Set<LiqFormulario572> liqFormulario572Set;

  public LiqDeducciones(Long id) {
    this.id = id;
  }

  public LiqDeducciones(Long id, String descripcion, BigDecimal monto, short tipo) {
    this.id = id;
    this.descripcion = descripcion;
    this.monto = monto;
    this.tipo = tipo;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqDeducciones[ id=" + id + " ]";
  }

}
