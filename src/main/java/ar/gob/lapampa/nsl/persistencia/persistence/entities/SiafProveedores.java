package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "SIAF_PROVEEDORES")
@NamedQueries(value = {
    @NamedQuery(name = "SiafProveedores.findAll", query = "SELECT s FROM SiafProveedores s")})
public class SiafProveedores implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 10)
  @Column(name = "CODIGO")
  private String codigo;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "RAZON_SOCIAL")
  private String razonSocial;
  @OneToMany(mappedBy = "proId", fetch = FetchType.LAZY)
  private Set<LiqCodigos> liqCodigosSet;

  public SiafProveedores(Long id) {
    this.id = id;
  }

  public SiafProveedores(Long id, String codigo, String razonSocial) {
    this.id = id;
    this.codigo = codigo;
    this.razonSocial = razonSocial;
  }

}
