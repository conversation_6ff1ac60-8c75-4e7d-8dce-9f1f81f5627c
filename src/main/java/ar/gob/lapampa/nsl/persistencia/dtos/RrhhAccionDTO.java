package ar.gob.lapampa.nsl.persistencia.dtos;

import java.io.Serializable;
import java.util.List;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RrhhAccionDTO extends BaseRRHHDTO implements Serializable {

  private static final long serialVersionUID = -6156257257977236140L;
  private String nombre;
  private String endpoint;
  private String nota;
  private List<RrhhRolDTO> roles;

  public RrhhAccionDTO(Long id) {
    super();
    this.id = id;
  }

  public RrhhAccionDTO(List<RrhhRolDTO> roles) {
    super();
    this.roles = roles;
  }

  public String getEntityBeanName() {
    return RrhhAccionDTO.class.getName();
  }

}
