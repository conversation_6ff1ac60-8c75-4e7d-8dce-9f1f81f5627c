package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_PUNTOS_CATEGORIAS")
@SequenceGenerator(name = "LIQ_PUC_SEQ", sequenceName = "LIQ_PUC_SEQ", allocationSize = 1)
public class LiqPuntosCategorias implements Serializable {

  private static final long serialVersionUID = -3170039345944563943L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "LIQ_PUC_SEQ")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "PUNTO")
  private BigDecimal punto;

  @JoinColumn(name = "COV_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqCodigosVersiones covId;

  @JoinColumn(name = "CAT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private PreCategorias catId;

  @Basic(optional = false)
  @Column(name = "EQUIVALENCIA_HORAS")
  private BigDecimal equivalenciaHoras;

  public LiqPuntosCategorias(BigDecimal punto, LiqCodigosVersiones covId, PreCategorias catId,
      BigDecimal equivalenciaHoras) {
    this.punto = punto;
    this.covId = covId;
    this.catId = catId;
    this.equivalenciaHoras = equivalenciaHoras;
  }

}
