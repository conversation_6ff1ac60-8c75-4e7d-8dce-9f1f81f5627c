package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_METADATOS")
@NamedQueries(
    value = {@NamedQuery(name = "BduMetadatos.findAll", query = "SELECT b FROM BduMetadatos b")})
public class BduMetadatos implements Serializable {

  private static final long serialVersionUID = -8693550020842327163L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "TIPO")
  private Character tipo;

  @Size(max = 400)
  @Column(name = "FORMATO")
  private String formato;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "mtdId", fetch = FetchType.LAZY)
  private Set<BduMtdValoresNum> bduMtdValoresNumSet;

  @OneToMany(mappedBy = "mtdId", fetch = FetchType.LAZY)
  private Set<BduOrigenesDatos> bduOrigenesDatosSet;

  @JoinColumn(name = "ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @OneToOne(optional = false, fetch = FetchType.LAZY)
  private BduAtributos bduAtributos;

  @JoinColumn(name = "TBL_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduTablas tblId;

  @OneToOne(cascade = CascadeType.ALL, mappedBy = "bduMetadatos", fetch = FetchType.LAZY)
  private BduMetadatosDominios bduMetadatosDominios;

  public BduMetadatos(Long id) {
    this.id = id;
  }

  public BduMetadatos(Long id, Character tipo) {
    this.id = id;
    this.tipo = tipo;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduMetadatos[ id=" + id + " ]";
  }

}
