package ar.gob.lapampa.nsl.persistencia.controllers;

import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import ar.gob.lapampa.nsl.persistencia.dtos.GenericResponseDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.RrhhUsuarioLogDTO;
import ar.gob.lapampa.nsl.persistencia.dtos.requests.UsuarioLogRequestDTO;
import ar.gob.lapampa.nsl.persistencia.security.CustomSecurityEvaluator;
import ar.gob.lapampa.nsl.persistencia.services.RrhhUsuarioLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

/**
 * RRHHUsuarioLog Controller
 *
 * <AUTHOR> NSL
 *
 */
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/v1/app/usuarios-logs")
public class UsuarioLogController {

  private final CustomSecurityEvaluator securityEvaluator;

  @Autowired
  private RrhhUsuarioLogService usuarioLogService;

  public UsuarioLogController(CustomSecurityEvaluator securityEvaluator) {
    this.securityEvaluator = securityEvaluator;
  }

  @Operation(summary = "Listado paginado con filtros de logs de usuarios RRHH",
      description = "Recibe  parámetros  por body con filtro y paginado", tags = {"Usuarios Logs"},
      hidden = false, deprecated = false)
  @ApiResponses({@ApiResponse(responseCode = "200", description = "Listado de una pagina"),
      @ApiResponse(responseCode = "400", description = "Bad Request")})
  @PostMapping("/listar")
  @Transactional
  // @PreAuthorize("hasAuthority('PERMISSION_USERS_READ') and
  // hasRole('PRESUPUESTO')")
  public @ResponseBody GenericResponseDTO listar(@RequestBody UsuarioLogRequestDTO request) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    // Set<String> permisosRequeridos = Set.of("USER_READ", "USER_WRITE");
    Set<String> permisosRequeridos = Set.of();
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR", "AUDITOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/usuarios-logs/listar", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return usuarioLogService.listar(request);
  }

  @Operation(summary = "Registra un nuevo log de usuarios RRHH",
      description = "Recibe  parámetros  por body", tags = {"Usuarios Logs"}, hidden = false,
      deprecated = false)
  @ApiResponses({@ApiResponse(responseCode = "200", description = "Listado de una pagina"),
      @ApiResponse(responseCode = "400", description = "Bad Request")})
  @PostMapping("/registrar")
  @Transactional
  // @PreAuthorize("hasAuthority('PERMISSION_USERS_READ') and
  // hasRole('PRESUPUESTO')")
  public @ResponseBody GenericResponseDTO crear(@RequestBody RrhhUsuarioLogDTO request) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    // Set<String> permisosRequeridos = Set.of("USER_READ", "USER_WRITE");
    Set<String> permisosRequeridos = Set.of();
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR", "AUDITOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/usuarios-logs/registrar", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return usuarioLogService.registrar(request);
  }

  @Operation(summary = "Modifica un log de usuarios RRHH por Id",
      description = "Recibe  parámetros  por body e id por path", tags = {"Usuarios Logs"},
      hidden = false, deprecated = false)
  @ApiResponses({@ApiResponse(responseCode = "200", description = "Listado de una pagina"),
      @ApiResponse(responseCode = "400", description = "Bad Request")})
  @PutMapping("/modificar/{id}")
  @Transactional
  // @PreAuthorize("hasAuthority('PERMISSION_USERS_READ') and
  // hasRole('PRESUPUESTO')")
  public @ResponseBody GenericResponseDTO modificar(@RequestBody RrhhUsuarioLogDTO request,
      @PathVariable Long id) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    // Set<String> permisosRequeridos = Set.of("USER_READ", "USER_WRITE");
    Set<String> permisosRequeridos = Set.of();
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR", "AUDITOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/usuarios-logs/registrar", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return usuarioLogService.modificar(request, id);
  }

  @Operation(summary = "Borra un log de usuarios RRHH por su Id",
      description = "Recibe  parámetros  por path", tags = {"Usuarios Logs"}, hidden = false,
      deprecated = false)
  @ApiResponses({@ApiResponse(responseCode = "200", description = "Listado de una pagina"),
      @ApiResponse(responseCode = "400", description = "Bad Request")})
  @DeleteMapping("/borrar/{id}")
  @Transactional
  // @PreAuthorize("hasAuthority('PERMISSION_USERS_READ') and
  // hasRole('PRESUPUESTO')")
  public @ResponseBody GenericResponseDTO borrar(@PathVariable Long id) {
    // Obtener la autenticación actual
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    // Definir los permisos y roles requeridos
    // Set<String> permisosRequeridos = Set.of("USER_READ", "USER_WRITE");
    Set<String> permisosRequeridos = Set.of();
    Set<String> rolesRequeridos = Set.of("ADMINISTRADOR", "AUDITOR");

    // Verificar los permisos y roles usando el CustomSecurityEvaluator
    securityEvaluator.verificarPermisos("/api/v1/app/usuarios-logs/registrar", authentication,
        rolesRequeridos, permisosRequeridos, "OR", "AND");

    // Si llega aquí, el usuario tiene acceso
    return usuarioLogService.eliminar(id);
  }
}
