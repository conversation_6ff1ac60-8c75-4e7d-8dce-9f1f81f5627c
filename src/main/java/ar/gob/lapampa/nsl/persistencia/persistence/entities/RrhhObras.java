package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_OBRAS")
@NamedQueries(
    value = {@NamedQuery(name = "RrhhObras.findAll", query = "SELECT r FROM RrhhObras r")})
public class RrhhObras implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "FECHA_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDesde;
  @Column(name = "FECHA_HASTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaHasta;
  @Column(name = "FINALIZADA")
  private Character finalizada;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "EST_ID")
  private long estId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "rrhhObras", fetch = FetchType.LAZY)
  private Set<RrhhOcupacionesObras> rrhhOcupacionesObrasSet;

  public RrhhObras(Long id) {
    this.id = id;
  }

  public RrhhObras(Long id, long estId) {
    this.id = id;
    this.estId = estId;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhObras[ id=" + id + " ]";
  }

}
