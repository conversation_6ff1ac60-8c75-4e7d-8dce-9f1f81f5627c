package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * The persistent class for the PRE_COMBINACION_PRESUPUESTARIA database table.
 * 
 * <AUTHOR>
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_UNIDADORG_CAT")
@SequenceGenerator(name = "PRE_UNIDADORG_CAT_SEQ", sequenceName = "PRE_UNIDADORG_CAT_SEQ",
    allocationSize = 1)
public class PreUnidadOrgCat implements Serializable {

  private static final long serialVersionUID = 838385871576435496L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PRE_UNIDADORG_CAT_SEQ")
  private Long id;

  @JoinColumn(name = "UOR_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private BduOrganismosPre uorId; // Unidad Organizacional NVL 3

  @JoinColumn(name = "CAT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private PreCategorias catId;

  @Temporal(TemporalType.DATE)
  @Column(name = "FECHA")
  private Date fecha;

  @Temporal(TemporalType.DATE)
  @Column(name = "FECHA_BAJA")
  private Date fechaBaja;

  private String activo;

  @OneToMany(mappedBy = "unicatId", fetch = FetchType.EAGER)
  private List<PreCargosPresupuestarios> cargos;

}
