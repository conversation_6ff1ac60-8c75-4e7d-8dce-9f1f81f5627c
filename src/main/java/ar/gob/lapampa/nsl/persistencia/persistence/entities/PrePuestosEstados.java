package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_PUESTOS_ESTADOS")
@SequenceGenerator(name = "PRE_EST_ID", sequenceName = "PRE_EST_ID", allocationSize = 1)
public class PrePuestosEstados implements Serializable {

  private static final long serialVersionUID = -9033553121583089302L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PRE_EST_ID")
  private Long id;

  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  @JoinColumn(name = "PUESTO_ID", nullable = false, updatable = false)
  private PrePuestos puestoId;

  @JoinColumn(name = "ESTADO_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private ParmPuestosEstados estadoId;

  @JoinColumn(name = "OCU_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private RrhhOcupaciones ocuId;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private BduPersonasFisicas prsId;

  @Column(name = "LEGAJO")
  private Long legajo;

  @Column(name = "TIPO_NORMA")
  private String tipoNorma;

  @Column(name = "NRO_NORMA")
  private String nroNorma;

  @Column(name = "ANIO_NORMA")
  private Long anioNorma;

  @Column(name = "ANIO_EXPEDIENTE")
  private Long anioExpediente;

  @Column(name = "NRO_EXPEDIENTE")
  private String nroExpediente;

  @Column(name = "FECHA_INICIO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaInicio;

  @Column(name = "FECHA_FIN")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaFin;

  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;

  @Column(name = "OBSERVACIONES")
  private String observaciones;

  @Column(name = "FECHA_ANULACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAnulacion;

  @Column(name = "ANULADO")
  private String anulado;

  @JoinColumn(name = "COMBPRES_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private PreCombinacionPresupuestaria combpresId;

  @JoinColumn(name = "CAT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private PreCategorias catId;

  @Column(name = "ANIO_REESTRUCTURA")
  private Long anioReestructura;

  @Column(name = "NRO_REESTRUCTURA")
  private Long nroReestructura;

  @JoinColumn(name = "USUARIO_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private RrhhUsuario usuarioId;

  @Column(name = "NRO_PUESTO_ASIGNADO")
  private Long nroPuestoAsignado;

  public PrePuestosEstados(Long id) {

    this.id = id;
  }

}
