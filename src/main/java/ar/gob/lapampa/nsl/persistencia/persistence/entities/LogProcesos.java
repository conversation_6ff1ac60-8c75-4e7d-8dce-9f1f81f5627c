package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LOG_PROCESOS")
@NamedQueries(
    value = {@NamedQuery(name = "LogProcesos.findAll", query = "SELECT l FROM LogProcesos l")})
public class LogProcesos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 50)
  @Column(name = "TIPO_LOG")
  private String tipoLog;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 50)
  @Column(name = "OBJETO_TIPO")
  private String objetoTipo;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 1000)
  @Column(name = "OBJETO_NOMBRE")
  private String objetoNombre;
  @Size(max = 50)
  @Column(name = "MODULO")
  private String modulo;
  @Column(name = "ERRORNUM")
  private Long errornum;
  @Size(max = 4000)
  @Column(name = "ERRORMESSAGE")
  private String errormessage;
  @Size(max = 100)
  @Column(name = "USUARIO_LOGUEADO")
  private String usuarioLogueado;
  @Size(max = 100)
  @Column(name = "USUARIO_AUTENTICADO")
  private String usuarioAutenticado;
  @Size(max = 100)
  @Column(name = "TABLA")
  private String tabla;
  @Column(name = "TABLA_VALOR_ID")
  private Long tablaValorId;
  @Size(max = 4000)
  @Column(name = "COMENTARIO")
  private String comentario;

  public LogProcesos(Long id) {
    this.id = id;
  }

  public LogProcesos(Long id, Date fecha, String tipoLog, String objetoTipo, String objetoNombre) {
    this.id = id;
    this.fecha = fecha;
    this.tipoLog = tipoLog;
    this.objetoTipo = objetoTipo;
    this.objetoNombre = objetoNombre;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LogProcesos[ id=" + id + " ]";
  }

}
