package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_ACC_LAB_TIPOS")
@NamedQueries(value = {
    @NamedQuery(name = "RrhhAccLabTipos.findAll", query = "SELECT r FROM RrhhAccLabTipos r")})
public class RrhhAccLabTipos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 1000)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @OneToMany(mappedBy = "altId", fetch = FetchType.LAZY)
  private Set<RrhhAccLaborales> rrhhAccLaboralesSet;

  public RrhhAccLabTipos(Long id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhAccLabTipos[ id=" + id + " ]";
  }

}
