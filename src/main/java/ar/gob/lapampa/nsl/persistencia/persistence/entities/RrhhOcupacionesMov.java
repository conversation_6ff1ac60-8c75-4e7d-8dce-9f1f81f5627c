package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_OCUPACIONES_MOV")
public class RrhhOcupacionesMov implements Serializable {

  private static final long serialVersionUID = 4134215393179355789L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @JoinColumn(name = "OCU_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhOcupaciones ocuId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ITEM")
  private Long item;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fecha;

  @Column(name = "CDAD_HORAS")
  private Integer cdadHoras;

  @JoinColumn(name = "EST_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhEstablecimientos estId;

  @Column(name = "AFE_ACT_ID")
  private Long afeActId;

  @Column(name = "NOR_ID")
  private Long norId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_VIG_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigDesde;

  @Column(name = "FECHA_VIG_HASTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaVigHasta;

  @Column(name = "MONTO")
  private BigDecimal monto;

  @Size(max = 4000)
  @Column(name = "OBSERVACION")
  private String observacion;

  @Column(name = "FECHA_APROBACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAprobacion;

  @Size(max = 4000)
  @Column(name = "COMENTARIO")
  private String comentario;

  @Column(name = "FECHA_ANULACION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaAnulacion;

  @Size(max = 1)
  @Column(name = "ESTADO")
  private String estado;

  @Column(name = "FECHA_CIERRE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaCierre;

  @JoinColumn(name = "CEQ_CAT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private PreCategorias ceqCatId;

  @Size(max = 1000)
  @Column(name = "USUARIO_MOV")
  private String usuarioMov;

  @Column(name = "FECHA_MOV")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaMov;

  @Column(name = "NOR_NRO")
  private Long norNro;

  @JoinColumn(name = "OCU_REEMP_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhOcupaciones ocuReempId;

  @Size(max = 1)
  @Column(name = "ACTUALIZADO")
  private String actualizado;

  @JoinColumn(name = "PPR_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private PrePlantaPresupuestaria pprId;

  @JoinColumn(name = "PLT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private PrePlantasTipos pltId;

  @JoinColumn(name = "FUN_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhFunciones funId;

  @JoinColumn(name = "JOT_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhJornadasTipos jotId;

  // @JoinColumns({ @JoinColumn(name = "LIC_OCU_ID", referencedColumnName =
  // "OCU_ID"), @JoinColumn(name = "LIC_ITEM", referencedColumnName = "ITEM")
  // })
  // @ManyToOne(fetch = FetchType.LAZY)
  // private RrhhLicencias rrhhLicencias;

  @JoinColumn(name = "MBA_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private RrhhMotivosBaja mbaId;

  @JoinColumn(name = "MOT_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private RrhhMovimientosTipos motId;


  @JoinColumn(name = "CAT_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(fetch = FetchType.EAGER)
  private PreCategorias catId;

}
