package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_DOMINIOS_DATOS")
@NamedQueries(value = {
    @NamedQuery(name = "BduDominiosDatos.findAll", query = "SELECT b FROM BduDominiosDatos b")})
public class BduDominiosDatos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Size(max = 8)
  @Column(name = "CODIGO_CORTO")
  private String codigoCorto;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "ESTADO")
  private Character estado;
  @JoinColumn(name = "DMN_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduDominios dmnId;

  public BduDominiosDatos(Long id) {
    this.id = id;
  }

  public BduDominiosDatos(Long id, String descripcion) {
    this.id = id;
    this.descripcion = descripcion;
  }

}
