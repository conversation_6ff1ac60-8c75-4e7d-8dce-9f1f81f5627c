package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import org.hibernate.type.YesNoConverter;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_CATEGORIAS")
@SequenceGenerator(name = "PRE_CAT_SEQ", sequenceName = "PRE_CAT_SEQ", allocationSize = 1)
public class PreCategorias implements Serializable {

  private static final long serialVersionUID = 1158654498403346013L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PRE_CAT_SEQ")
  private Long id;

  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @JoinColumn(name = "TRA_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private PreTramos traId;

  @Column(name = "ORDEN")
  private Long orden;

  @Nonnull
  @Column(name = "ACTIVO")
  @Convert(converter = YesNoConverter.class)
  private Boolean activo;

  @Column(name = "NRO")
  private Long nro;

  @Column(name = "HABER_BASE")
  private BigDecimal haberBase;

  @Nonnull
  @Column(name = "PRESUPUESTADA")
  @Convert(converter = YesNoConverter.class)
  private Boolean presupuestada;

  @Column(name = "ADMITE_HS")
  @Convert(converter = YesNoConverter.class)
  private Boolean admiteHoras;

  @OneToMany(mappedBy = "catId", fetch = FetchType.LAZY)
  private List<LiqVariables> liqVariablesList;

  @OneToMany(mappedBy = "catId", fetch = FetchType.LAZY)
  private List<LiqBasicosCategorias> liqBasicosCategoriasList;

  @OneToMany(mappedBy = "catId", fetch = FetchType.LAZY)
  private List<LiqPuntosCategorias> liqPuntosCategoriasList;

  public PreCategorias(Long id) {

    this.id = id;
  }

  public PreCategorias(Long id, String descripcion) {

    this.id = id;
    this.descripcion = descripcion;
  }

  public PreCategorias(Long id, String descripcion, Long nro) {

    this.id = id;
    this.descripcion = descripcion;
    this.nro = nro;
  }

}
