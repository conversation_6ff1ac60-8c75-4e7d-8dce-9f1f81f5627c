package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_CARGA_TRANSPORTE")
@NamedQueries(value = {
    @NamedQuery(name = "LiqCargaTransporte.findAll", query = "SELECT l FROM LiqCargaTransporte l")})
public class LiqCargaTransporte implements Serializable {
  private static final long serialVersionUID = 1L;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "DNI")
  private long dni;
  @Column(name = "OCU_ID")
  private Long ocuId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "CANTIDAD")
  private int cantidad;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "MES")
  private short mes;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ANIO")
  private short anio;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PROCESADO")
  private Character procesado;
  @Column(name = "OMNIBUS")
  private Character omnibus;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  public LiqCargaTransporte(Long id) {
    this.id = id;
  }

  public LiqCargaTransporte(Long id, long dni, int cantidad, short mes, short anio,
      Character procesado) {
    this.id = id;
    this.dni = dni;
    this.cantidad = cantidad;
    this.mes = mes;
    this.anio = anio;
    this.procesado = procesado;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqCargaTransporte[ id=" + id + " ]";
  }

}
