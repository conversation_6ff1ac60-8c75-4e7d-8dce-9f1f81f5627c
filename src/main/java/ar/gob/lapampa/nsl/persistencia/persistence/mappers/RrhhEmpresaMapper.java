package ar.gob.lapampa.nsl.persistencia.persistence.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import ar.gob.lapampa.nsl.datatransfer.RrhhEmpresasDTO;
import ar.gob.lapampa.nsl.persistencia.models.EmpresaModel;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.RrhhEmpresa;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RrhhEmpresaMapper {

    RrhhEmpresaMapper INSTANCE = Mappers.getMapper(RrhhEmpresaMapper.class);

    // TODO: Ignoramos todas la listas para evitar un dto ciclico ver de mejorar
    @Mapping(target = "roles", ignore = true)
    @Mapping(target = "lotes", ignore = true)
    @Mapping(target = "convenios", ignore = true)
    RrhhEmpresasDTO rrhhEmpresaToRrhhEmpresaDto(RrhhEmpresa empresa);

    static EmpresaModel rrhhEmpresaToEmpresaModel(RrhhEmpresa empresa) {
        EmpresaModel model = new EmpresaModel(empresa.getId());
        model.setCodigo(empresa.getCodigo());
        model.setDescripcion(empresa.getDescripcion());
       // model.setConvenios(empresa.getConvenios());
        return model;
    }

}
