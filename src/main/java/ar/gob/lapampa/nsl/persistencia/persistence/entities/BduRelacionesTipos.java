package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_RELACIONES_TIPOS")
@NamedQueries(value = {
    @NamedQuery(name = "BduRelacionesTipos.findAll", query = "SELECT b FROM BduRelacionesTipos b")})
public class BduRelacionesTipos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduRelacionesTipos", fetch = FetchType.LAZY)
  private Set<BduOrgPreVinRet> bduOrgPreVinRetSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduRelacionesTipos", fetch = FetchType.LAZY)
  private Set<BduOrgPrtVinRet> bduOrgPrtVinRetSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduRelacionesTipos", fetch = FetchType.LAZY)
  private Set<BduOrgFncVinRet> bduOrgFncVinRetSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduRelacionesTipos", fetch = FetchType.LAZY)
  private Set<BduOrgOroVinRet> bduOrgOroVinRetSet;

  public BduRelacionesTipos(Long id) {
    this.id = id;
  }

  public BduRelacionesTipos(Long id, String descripcion, Character activo) {
    this.id = id;
    this.descripcion = descripcion;
    this.activo = activo;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduRelacionesTipos[ id=" + id + " ]";
  }

}
