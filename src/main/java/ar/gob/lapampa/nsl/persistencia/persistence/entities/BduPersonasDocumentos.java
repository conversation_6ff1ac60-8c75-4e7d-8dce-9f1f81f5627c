package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PERSONAS_DOCUMENTOS")
@NamedQueries(value = {@NamedQuery(name = "BduPersonasDocumentos.findAll",
    query = "SELECT b FROM BduPersonasDocumentos b")})
public class BduPersonasDocumentos implements Serializable {

  private static final long serialVersionUID = 1L;

  @EmbeddedId
  protected BduPersonasDocumentosPK bduPersonasDocumentosPK;
  @Size(max = 200)
  @Column(name = "COMENTARIO")
  private String comentario;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "FECHA_DESDE")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDesde;

  @Column(name = "FECHA_HASTA")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaHasta;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ODT_ID")
  private long odtId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "ACTIVO")
  private Character activo;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduPersonas prsId;

  public BduPersonasDocumentos(BduPersonasDocumentosPK bduPersonasDocumentosPK) {
    this.bduPersonasDocumentosPK = bduPersonasDocumentosPK;
  }

  public BduPersonasDocumentos(BduPersonasDocumentosPK bduPersonasDocumentosPK, Date fechaDesde,
      long odtId, Character activo) {
    this.bduPersonasDocumentosPK = bduPersonasDocumentosPK;
    this.fechaDesde = fechaDesde;
    this.odtId = odtId;
    this.activo = activo;
  }

  public BduPersonasDocumentos(long tdcId, String numero, short ocurrencia) {
    this.bduPersonasDocumentosPK = new BduPersonasDocumentosPK(tdcId, numero, ocurrencia);
  }

}
