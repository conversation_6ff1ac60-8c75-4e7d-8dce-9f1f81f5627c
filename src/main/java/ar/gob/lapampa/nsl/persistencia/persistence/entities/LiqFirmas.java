package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_FIRMAS")
@NamedQueries(
    value = {@NamedQuery(name = "LiqFirmas.findAll", query = "SELECT l FROM LiqFirmas l")})
public class LiqFirmas implements Serializable {
  private static final long serialVersionUID = 1L;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private BigDecimal id;
  @Lob
  @Column(name = "HASH_ID")
  private byte[] hashId;
  @OneToMany(mappedBy = "firId", fetch = FetchType.LAZY)
  private Set<LiqFirmasOcupaciones> liqFirmasOcupacionesSet;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "liqFirmas", fetch = FetchType.LAZY)
  private Set<LiqFirmasDetalle> liqFirmasDetalleSet;

  public LiqFirmas(BigDecimal id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqFirmas[ id=" + id + " ]";
  }

}
