package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "BDU_DOMICILIOS")
@NamedQueries(
    value = {@NamedQuery(name = "BduDomicilios.findAll", query = "SELECT b FROM BduDomicilios b")})
public class BduDomicilios implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Column(name = "BRR_ID")
  private Long brrId;
  @Column(name = "CLL_ID")
  private Long cllId;
  @Column(name = "CDR_ID")
  private Long cdrId;
  @Column(name = "NUMERACION")
  private Integer numeracion;
  @Size(max = 8)
  @Column(name = "PISO")
  private String piso;
  @Size(max = 8)
  @Column(name = "DEPTO")
  private String depto;
  @Size(max = 20)
  @Column(name = "UF")
  private String uf;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ODT_ID")
  private long odtId;
  @Size(max = 200)
  @Column(name = "CALLE")
  private String calle;
  @Size(max = 4000)
  @Column(name = "REFERENCIAS")
  private String referencias;
  @Size(max = 32)
  @Column(name = "LATITUD")
  private String latitud;
  @Size(max = 32)
  @Column(name = "LONGITUD")
  private String longitud;
  @JoinColumn(name = "LCD_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduLocalidades lcdId;
  @OneToMany(cascade = CascadeType.ALL, mappedBy = "dmcId", fetch = FetchType.LAZY)
  private Set<BduPersonasDomicilios> bduPersonasDomiciliosSet;

  public BduDomicilios(Long id) {
    this.id = id;
  }

  public BduDomicilios(Long id, long odtId) {
    this.id = id;
    this.odtId = odtId;
  }

}
