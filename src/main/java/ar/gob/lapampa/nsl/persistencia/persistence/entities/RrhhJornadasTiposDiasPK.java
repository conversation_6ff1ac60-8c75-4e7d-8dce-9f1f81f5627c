package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class RrhhJornadasTiposDiasPK implements Serializable {

  private static final long serialVersionUID = -8413412164455271538L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "JOR_ID")
  private long jorId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "DSE_ID")
  private long dseId;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.RrhhJornadasTiposDiasPK[ jorId=" + jorId
        + ", dseId=" + dseId + " ]";
  }

}
