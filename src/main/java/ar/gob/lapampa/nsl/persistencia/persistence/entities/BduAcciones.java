package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.persistence.Basic;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_ACCIONES")
@NamedQueries(
    value = {@NamedQuery(name = "BduAcciones.findAll", query = "SELECT b FROM BduAcciones b")})
public class BduAcciones implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Basic(optional = false)
  @NotNull
  @Column(name = "ID")
  private Long id;

  @Basic(optional = false)
  @NotNull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;

  @Column(name = "NIVEL")
  private Short nivel;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "accId", fetch = FetchType.LAZY)
  private Set<BduEventosTipos> bduEventosTiposSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "bduAcciones", fetch = FetchType.LAZY)
  private Set<BduFuncAccPerfiles> bduFuncAccPerfilesSet;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "accId", fetch = FetchType.LAZY)
  private Set<BduFuncAccionesSis> bduFuncAccionesSisSet;

  public BduAcciones(final Long id) {
    this.id = id;
  }

  public BduAcciones(final Long id, final String descripcion) {
    this.id = id;
    this.descripcion = descripcion;
  }

}
