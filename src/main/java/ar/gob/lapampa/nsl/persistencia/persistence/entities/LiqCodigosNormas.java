package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_CODIGOS_NORMAS")
@NamedQueries(value = {
    @NamedQuery(name = "LiqCodigosNormas.findAll", query = "SELECT l FROM LiqCodigosNormas l")})
public class LiqCodigosNormas implements Serializable {
  private static final long serialVersionUID = 1L;
  @EmbeddedId
  protected LiqCodigosNormasPK liqCodigosNormasPK;
  @Column(name = "NOR_NRO")
  private Long norNro;
  @JoinColumn(name = "COD_ID", referencedColumnName = "ID", insertable = false, updatable = false)
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private LiqCodigos liqCodigos;

  public LiqCodigosNormas(LiqCodigosNormasPK liqCodigosNormasPK) {
    this.liqCodigosNormasPK = liqCodigosNormasPK;
  }

  public LiqCodigosNormas(long codId, long norId) {
    this.liqCodigosNormasPK = new LiqCodigosNormasPK(codId, norId);
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqCodigosNormas[ liqCodigosNormasPK="
        + liqCodigosNormasPK + " ]";
  }

}
