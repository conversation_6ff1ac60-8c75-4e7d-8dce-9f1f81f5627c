package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PERSONAS_VIEW")
public class BduPersonasFisicasView implements Serializable {

  private static final long serialVersionUID = 1818593742338123004L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "APELLIDO")
  private String apellido;

  @Size(max = 200)
  @Column(name = "NOMBRE")
  private String nombres;

  @JoinColumn(name = "DOCUMENTONID", referencedColumnName = "ID")
  @OneToOne(optional = false, fetch = FetchType.LAZY)
  private BduDocumentosTiposNucleo tdcId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "PersonasNNroDoc")
  private String nroDocumento;

  @Column(name = "FECHA_NACIMIENTO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaNacimiento;

  public BduPersonasFisicasView(Long id) {
this.id = id;
  }

}
