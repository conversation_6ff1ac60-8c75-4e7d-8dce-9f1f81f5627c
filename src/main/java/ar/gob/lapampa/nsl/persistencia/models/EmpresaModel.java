package ar.gob.lapampa.nsl.persistencia.models;

import java.io.Serializable;
import java.util.List;
import com.querydsl.core.annotations.QueryProjection;
import ar.gob.lapampa.nsl.datatransfer.BaseRRHHDTO;
import ar.gob.lapampa.nsl.persistencia.persistence.entities.PreEscalafones;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EmpresaModel extends BaseRRHHDTO implements Serializable {

    private static final long serialVersionUID = -3098667973055099213L;

    private String descripcion;
    private String codigo;
    private List<PreEscalafones> convenios;

    @QueryProjection
    public EmpresaModel(Long id) {
        super();
        this.id = id;
    }

    public List<PreEscalafones> getConvenios() {
        return convenios;
    }

    public void setConvenios(List<PreEscalafones> convenios) {
        this.convenios = convenios;
    }

    /**
     * Obtiene Nombre de class name solo
     *
     * @return String
     */
    @Override
    public String getBeanName() {
        return EmpresaModel.class.getSimpleName();
    }

    /**
     * Obtiene Nombre de package + class name
     *
     * @return String
     */
    @Override
    public String getCualifiedName() {
        return EmpresaModel.class.getCanonicalName();
    }
}
