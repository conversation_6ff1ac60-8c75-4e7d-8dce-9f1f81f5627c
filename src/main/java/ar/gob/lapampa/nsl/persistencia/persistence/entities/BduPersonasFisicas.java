package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_PERSONAS_FISICAS")
@SequenceGenerator(name = "BDU_PRF_SEQ", sequenceName = "BDU_PRF_SEQ", allocationSize = 1)
public class BduPersonasFisicas implements Serializable {

  @Serial
  private static final long serialVersionUID = -2962014799512978558L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "BDU_PRF_SEQ")
  private Long id;

  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "APELLIDO")
  private String apellido;

  @Size(max = 200)
  @Column(name = "NOMBRES")
  private String nombres;

  @Column(name = "FECHA_NACIMIENTO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaNacimiento;

  @Column(name = "FECHA_DEFUNCION")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaDefuncion;

  @Column(name = "LCD_ID")
  private Long lcdId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "SEXO")
  private Character sexo;

  @JoinColumn(name = "ECL_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduEstadoCivil eclId;

  @Size(max = 400)
  @Column(name = "LUGAR_NACIMIENTO")
  private String lugarNacimiento;

  @JoinColumn(name = "TNA_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduNacionalidadTipo tnaId;

  @JoinColumn(name = "ID", referencedColumnName = "ID")
  @OneToOne(optional = false, fetch = FetchType.EAGER)
  private BduPersonas bduPersonas;

  @Column(name = "NRO_AFILIADO")
  private Long nroAfiliado;

  @Column(name = "CUIL")
  private Long cuil;


  // esta relacion se hace para poder joiniar con detalle_lote de esa manera cuando
  // se listen las personas que no tienen cuenta bancaria se considere la exclusion de aquellos
  // registros
  // que pertenecen a detalle_lote
  @OneToOne(mappedBy = "prsId", fetch = FetchType.LAZY)
  private RhrhDetalleLote detalleLote;

  @OneToMany(mappedBy = "prsId", fetch = FetchType.LAZY)
  private List<RrhhPersonasDatos> cuenta;


  public List<RrhhPersonasDatos> getCuenta() {
    return cuenta;
  }

  public void setCuenta(List<RrhhPersonasDatos> cuenta) {
    this.cuenta = cuenta;
  }

  public BduPersonasFisicas(Long id) {
this.id = id;
  }

  public BduPersonasFisicas(String apellido, String nombres) {
this.apellido = apellido;
    this.nombres = nombres;
  }

  public BduPersonasFisicas(Long id, String apellido, Character sexo) {
this.id = id;
    this.apellido = apellido;
    this.sexo = sexo;
  }

}
