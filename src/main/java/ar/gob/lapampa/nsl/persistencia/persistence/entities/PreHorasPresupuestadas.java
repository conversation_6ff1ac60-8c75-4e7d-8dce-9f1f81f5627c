package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "PRE_HORAS_PRESUPUESTADAS")
@NamedQueries(value = {@NamedQuery(name = "PreHorasPresupuestadas.findAll",
    query = "SELECT p FROM PreHorasPresupuestadas p")})
public class PreHorasPresupuestadas implements Serializable {
  private static final long serialVersionUID = 1L;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private BigDecimal id;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "PPR_ID")
  private BigInteger pprId;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "HORAS_PRESUP")
  private BigInteger horasPresup;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "HORAS_OCUPADAS")
  private BigInteger horasOcupadas;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "HORAS_RESERVADAS")
  private BigInteger horasReservadas;
  @Column(name = "PLANTA_PERMANENTE")
  private Character plantaPermanente;

  public PreHorasPresupuestadas(BigDecimal id) {
    this.id = id;
  }

  public PreHorasPresupuestadas(BigDecimal id, BigInteger pprId, BigInteger horasPresup,
      BigInteger horasOcupadas, BigInteger horasReservadas) {
    this.id = id;
    this.pprId = pprId;
    this.horasPresup = horasPresup;
    this.horasOcupadas = horasOcupadas;
    this.horasReservadas = horasReservadas;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.PreHorasPresupuestadas[ id=" + id + " ]";
  }

}
