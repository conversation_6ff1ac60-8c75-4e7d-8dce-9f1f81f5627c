package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Embeddable
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class LiqDeduccionesPersonasDetPK implements Serializable {

  private static final long serialVersionUID = -1285048311302230893L;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "DDP_ID")
  private long ddpId;

  @Basic(optional = false)
  @Nonnull
  @Column(name = "DED_ID")
  private long dedId;

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqDeduccionesPersonasDetPK[ ddpId=" + ddpId
        + ", dedId=" + dedId + " ]";
  }

}
