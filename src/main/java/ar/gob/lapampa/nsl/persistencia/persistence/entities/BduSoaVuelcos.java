package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_SOA_VUELCOS")
@NamedQueries(
    value = {@NamedQuery(name = "BduSoaVuelcos.findAll", query = "SELECT b FROM BduSoaVuelcos b")})
public class BduSoaVuelcos implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Column(name = "VERSIONADO")
  private BigInteger versionado;
  @Column(name = "FECHA_SERVIDOR")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaServidor;
  @Column(name = "FECHA_SER_EXT")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaSerExt;
  @Column(name = "FECHA_INICIO")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaInicio;
  @Column(name = "FECHA_FIN")
  @Temporal(TemporalType.TIMESTAMP)
  private Date fechaFin;
  @Column(name = "ESTADO")
  private Long estado;
  @Column(name = "TUPLAS")
  private Long tuplas;
  @Column(name = "TUPLAS_PROCESADAS")
  private Long tuplasProcesadas;
  @Column(name = "TUPLAS_ERROR")
  private Long tuplasError;
  @OneToMany(mappedBy = "vlcId", fetch = FetchType.LAZY)
  private Set<BduValoresSoaEventos> bduValoresSoaEventosSet;
  @JoinColumn(name = "MPS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.LAZY)
  private BduMapeosSoa mpsId;

  public BduSoaVuelcos(Long id) {
    this.id = id;
  }

  public BduSoaVuelcos(Long id, String descripcion) {
    this.id = id;
    this.descripcion = descripcion;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduSoaVuelcos[ id=" + id + " ]";
  }

}
