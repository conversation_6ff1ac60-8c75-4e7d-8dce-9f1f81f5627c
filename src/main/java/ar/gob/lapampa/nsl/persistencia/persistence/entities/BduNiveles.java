package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_NIVELES")
@NamedQueries(
    value = {@NamedQuery(name = "BduNiveles.findAll", query = "SELECT b FROM BduNiveles b")})
public class BduNiveles implements Serializable {
  private static final long serialVersionUID = 1L;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "TIPO_ORGANISMO")
  private Character tipoOrganismo;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "NIVEL")
  private short nivel;
  @Size(max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "FORMATO")
  private short formato;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @OneToMany(mappedBy = "nvlId", fetch = FetchType.LAZY)
  private Set<BduOrganismosPrt> bduOrganismosPrtSet;
  @OneToMany(mappedBy = "nvlId", fetch = FetchType.LAZY)
  private Set<BduOrganismosOrg> bduOrganismosOrgSet;

  public BduNiveles(Long id) {
    this.id = id;
  }

  public BduNiveles(Long id, Character tipoOrganismo, short nivel, short formato) {
    this.id = id;
    this.tipoOrganismo = tipoOrganismo;
    this.nivel = nivel;
    this.formato = formato;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduNiveles[ id=" + id + " ]";
  }

}
