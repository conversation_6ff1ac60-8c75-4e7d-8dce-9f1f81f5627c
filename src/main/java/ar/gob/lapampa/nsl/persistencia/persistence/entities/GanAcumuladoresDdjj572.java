package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "GAN_ACUMULADORES_DDJJ572")
@SequenceGenerator(name = "GAN_ACUMULADORES_DDJJ572_SQ",
    sequenceName = "GAN_ACUMULADORES_DDJJ572_SQ", allocationSize = 1)
public class GanAcumuladoresDdjj572 implements Serializable {

  private static final long serialVersionUID = 2440281596665542673L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "GAN_ACUMULADORES_DDJJ572_SQ")
  private Long id;

  @Column(name = "ACUMULADOR")
  private Long acumulador;

  @Column(name = "COEFICIENTE")
  private Long coeficiente;

  @Column(name = "CONCEPTO_F572")
  private Long conceptoF572;

  @Column(name = "SIGNO")
  private Long signo;

}
