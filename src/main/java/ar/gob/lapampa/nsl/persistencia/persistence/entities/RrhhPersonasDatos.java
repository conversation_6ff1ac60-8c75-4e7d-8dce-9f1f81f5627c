package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "RRHH_PERSONAS_DATOS")
@SequenceGenerator(name = "RRHH_PRS_DATOS_SEQ", sequenceName = "RRHH_PRS_DATOS_SEQ",
    allocationSize = 1)
public class RrhhPersonasDatos implements Serializable {

  private static final long serialVersionUID = 1709142549806621196L;

  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "RRHH_PRS_DATOS_SEQ")
  private Long id;

  @Column(name = "NRO_CTA")
  private String nroCuenta;

  @JoinColumn(name = "CAB_SUC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.EAGER)
  private LiqCabeceraBancoSucursal cabSucId;

  @JoinColumn(name = "PRS_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private BduPersonasFisicas prsId;

  @JoinColumn(name = "EMP_ID", referencedColumnName = "ID")
  @ManyToOne(optional = true, fetch = FetchType.EAGER)
  private RrhhEmpresa empId;

  @JoinColumn(name = "FORMA_PAGO_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private LiqFormasPago fopId;

  @Column(name = "VALOR_IAPS")
  private Long valorIaps;

  @Column(name = "SUELDO_EXTERNO")
  private BigDecimal sueldoExterno;

  @Column(name = "NRO_CTA_BERSA")
  private String nroCtaBersa;

  @Column(name = "DIGITO")
  private Short digito;

  @Column(name = "DENOMINACION")
  private String denominacion;

  @Column(name = "CBU")
  private String cbu;

  @JoinColumn(name = "TIPO_CTA_BANC_ID", referencedColumnName = "ID")
  @ManyToOne(optional = false, fetch = FetchType.EAGER)
  private LiqTipoCuentaBancaria tcbId;

  @Column(name = "BANCO")
  private String banco;

  @Column(name = "ESTADO")
  private String estado;

  @Column(name = "OBSERVACION")
  private String observacion;

  @Column(name = "FECHA_CAMBIO_ESTADO")
  private Date fechaCambioEstado;

  @Column(name = "FECHA_VIGENCIA")
  private Date fechaVigencia;

}
