package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.util.Set;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "BDU_FUNCIONES_MENU")
@NamedQueries(value = {
    @NamedQuery(name = "BduFuncionesMenu.findAll", query = "SELECT b FROM BduFuncionesMenu b")})
public class BduFuncionesMenu implements Serializable {
  private static final long serialVersionUID = 1L;
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private Long id;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION")
  private String descripcion;
  @Basic(optional = false)
  @Nonnull
  @Size(min = 1, max = 200)
  @Column(name = "DESCRIPCION_AMPL")
  private String descripcionAmpl;
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ORDEN")
  private long orden;
  @OneToMany(mappedBy = "bfmId", fetch = FetchType.LAZY)
  private Set<BduFuncionesMenu> bduFuncionesMenuSet;
  @JoinColumn(name = "BFM_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduFuncionesMenu bfmId;
  @JoinColumn(name = "FSI_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private BduFuncionesSis fsiId;

  public BduFuncionesMenu(Long id) {
    this.id = id;
  }

  public BduFuncionesMenu(Long id, String descripcion, String descripcionAmpl, long orden) {
    this.id = id;
    this.descripcion = descripcion;
    this.descripcionAmpl = descripcionAmpl;
    this.orden = orden;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.BduFuncionesMenu[ id=" + id + " ]";
  }

}
