package ar.gob.lapampa.nsl.persistencia.persistence.entities;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 *
 * <AUTHOR> NSL
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode
@Table(name = "LIQ_OCU_SIT_REV")
@NamedQueries(
    value = {@NamedQuery(name = "LiqOcuSitRev.findAll", query = "SELECT l FROM LiqOcuSitRev l")})
public class LiqOcuSitRev implements Serializable {
  private static final long serialVersionUID = 1L;
  // @Max(value=?) @Min(value=?)//if you know range of your decimal fields consider using these
  // annotations to enforce field validation
  @Id
  @Basic(optional = false)
  @Nonnull
  @Column(name = "ID")
  private BigDecimal id;
  @Column(name = "OCU_ID")
  private BigInteger ocuId;
  @Column(name = "MES")
  private BigInteger mes;
  @Column(name = "ANIO")
  private BigInteger anio;
  @Column(name = "ANT_LIQ_ANIOS")
  private BigInteger antLiqAnios;
  @Column(name = "ANT_LIQ_MESES")
  private BigInteger antLiqMeses;
  @Column(name = "ANT_LIQ_DIAS")
  private BigInteger antLiqDias;
  @Column(name = "EST_ID")
  private BigInteger estId;
  @Column(name = "CAT_ID")
  private BigInteger catId;
  @Column(name = "ACT_ID")
  private BigInteger actId;
  @Column(name = "AFE_ACT_ID")
  private BigInteger afeActId;
  @Column(name = "CEQ_CAT_ID")
  private BigInteger ceqCatId;
  @Column(name = "CDAD_DIAS_TRABAJADOS")
  private BigInteger cdadDiasTrabajados;
  @Column(name = "CDAD_DIAS_FUNCION")
  private BigInteger cdadDiasFuncion;
  @Column(name = "CDAD_DIAS_NORMALES")
  private BigInteger cdadDiasNormales;
  @Column(name = "CDAD_DIAS_ESPECIALES")
  private BigInteger cdadDiasEspeciales;
  @JoinColumn(name = "PRC_ID", referencedColumnName = "ID")
  @ManyToOne(fetch = FetchType.LAZY)
  private LiqProcesos prcId;

  public LiqOcuSitRev(BigDecimal id) {
    this.id = id;
  }

  @Override
  public String toString() {
    return "ar.gob.lapampa.nsl.persistencia.entities.LiqOcuSitRev[ id=" + id + " ]";
  }

}
