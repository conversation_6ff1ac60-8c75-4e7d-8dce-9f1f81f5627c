application: Servicio Persistencia
config-origin: Service yml file
environment: ${RUNTIME_ENVIRONMENT:none}
eureka:
  instance:
    hostname: localhost
    instance-id: ${spring.application.name}:${random.uuid}
management:
  endpoint:
    configprops:
      show-values: ALWAYS
    env:
      post:
        enabled: true
      show-values: ALWAYS
    health:
      show-details: ALWAYS
    quartz:
      show-values: ALWAYS
    refresh:
      access: unrestricted
    restart:
      access: none
    shutdown:
      access: none
  endpoints:
    web:
      exposure:
        include: '*'
  health:
    db:
      enabled: false
project:
  description: '@project.description@'
  version: '@project.version@'
server:
  port: 0
spring:
  application:
    name: servicio-persistencia
  cloud:
    bus:
      enabled: true
    compatibility-verifier:
      enabled: false
    config:
      password: s3cr3t
      uri: http://localhost:8888
      username: root
    refresh:
      enabled: true
  datasource:
    hikari:
      connection-test-query: SELECT 1 FROM DUAL
      connection-timeout: 20000
      idle-timeout: 60000
      leak-detection-threshold: 90000
      max-lifetime: 120000
      maximum-pool-size: 5
      minimum-idle: 1
      pool-name: HikariPool-persistencia
    password: rjqUsKi2mxwog74P4VLN
    type: com.zaxxer.hikari.HikariDataSource
    url: jdbc:oracle:thin:@*********:1521/DESA
    username: LAPAMPA
  jpa:
    generate-ddl: false
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.OracleDialect
    show-sql: false
  profiles:
    active: ${RUNTIME_ENVIRONMENT:desa}
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    config-url: /${spring.application.name}/v3/api-docs/swagger-config
    disable-swagger-default-url: true
    doc-expansion: none
    path: /swagger-ui
    url: /${spring.application.name}/v3/api-docs
---
logging:
  level:
    root: INFO
spring:
  banner:
    location: classpath:banner_local.txt
  config:
    activate:
      on-profile: local
  datasource:
    password: rjqUsKi2mxwog74P4VLN
    url: ***********************************
    username: LAPAMPA
---
logging:
  level:
    root: ERROR
spring:
  banner:
    location: classpath:banner.txt
  config:
    activate:
      on-profile: desa
  datasource:
    password: Cesida#2023
    url: jdbc:oracle:thin:@*********:1521/DESA
    username: LP_ADMIN
---
logging:
  level:
    root: ERROR
spring:
  banner:
    location: classpath:banner_test.txt
  config:
    activate:
      on-profile: test
  datasource:
    password: Cesida#2023
    url: *************************************
    username: LP_ADMIN
---
logging:
  level:
    root: ERROR
spring:
  banner:
    location: classpath:banner_prod.txt
  config:
    activate:
      on-profile: prod
  datasource:
    password: Cesida#2023
    url: *****************************************
    username: LP_ADMIN
