<?xml version="1.0"?>

<ruleset name="Custom Rules"
	xmlns="http://pmd.sourceforge.net/ruleset/2.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://pmd.sourceforge.net/ruleset/2.0.0 https://pmd.sourceforge.io/ruleset_2_0_0.xsd">

	<description>
		Rules del proyecto
	</description>

	<rule ref="category/java/bestpractices.xml"></rule>
	<rule ref="category/java/codestyle.xml"></rule>
	<rule ref="category/java/design.xml">
		 <exclude name="LoosePackageCoupling" />
	</rule>
	<rule ref="category/java/documentation.xml">
		<exclude name="CommentSize"></exclude>
	</rule>
	<rule ref="category/java/errorprone.xml"></rule>
	<rule ref="category/java/multithreading.xml"></rule>
	<rule ref="category/java/performance.xml"></rule>
	<rule ref="category/java/security.xml"></rule>

</ruleset>
