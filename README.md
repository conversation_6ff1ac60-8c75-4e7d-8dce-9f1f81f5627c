# Servicio de Persistencia

Este proyecto contiene todas las entitidades núcleo que serán necesarias en el Ecosistema de MicroServicios para el NSL

## Integración
- Importar el proyecto al IDE como un proyecto Maven (tradicional);
- Hacer un **Run As** -> **Maven Build...** en el Proyecto Importado
  - Añadir como Goals: **clean install**
- Hacer un **Run As** -> **Maven Build...** en el MicroServicio
  - Añadir como Goals: **clean install**

### DeLombok

El proyecto usa extensivamente anotaciones de Lombok y además contiene un *profile* en **pom.xml** que permite construir los sources generados por Lombok

- Hacer un **Run As** -> **Maven Build...** en el MicroServicio
  - Añadir como Goals: **clean lombok:delombok -Pdlombok**
  
Nos dará información de las clases procesadas ([delomboked]) y las que no tienen cambios ([unchanged])

Tendremos los sources para ver en la carpeta **target -> generated-sources -> delombok**
 

## Contribuyentes: 

**Equipo NSL somos**

 - Equipo de desarrollo `CeSiDa` La Pampa
 - Equipo de desarrollo `Be The Driver`


## Licencia

&copy; La Pampa 2023 - Todos los Derechos Reservados
