{"info": {"_postman_id": "e1557976-860e-4bc0-8aff-dce8a9bf1394", "name": "<PERSON><PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "14416183"}, "item": [{"name": "Checkers", "item": [{"name": "Check status", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-persistencia/estado", "host": ["{{Server}}"], "path": ["servicio-persistencia", "estado"]}}, "response": []}, {"name": "Check status Desa", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{DesaServer}}/servicio-persistencia/estado", "host": ["{{DesaServer}}"], "path": ["servicio-persistencia", "estado"]}}, "response": []}, {"name": "Check status Test", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{TestServer}}/servicio-persistencia/estado", "host": ["{{TestServer}}"], "path": ["servicio-persistencia", "estado"]}}, "response": []}, {"name": "Check status Copy", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-persistencia/actuator/health", "host": ["{{Server}}"], "path": ["servicio-persistencia", "actuator", "health"]}}, "response": []}, {"name": "Check status (port)", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "localhost:64001/estado", "host": ["localhost"], "port": "64001", "path": ["estado"]}}, "response": []}, {"name": "Check salidas logs", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-persistencia/logsTest", "host": ["{{Server}}"], "path": ["servicio-persistencia", "logsTest"]}}, "response": []}]}, {"name": "Docs", "item": [{"name": "Api-Docs", "request": {"method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-persistencia/v3/api-docs", "host": ["{{Server}}"], "path": ["servicio-persistencia", "v3", "api-docs"]}}, "response": []}, {"name": "Swagger", "request": {"method": "GET", "header": [], "url": {"raw": "{{TestServer}}/servicio-persistencia/swagger-ui/index.html", "host": ["{{TestServer}}"], "path": ["servicio-persistencia", "swagger-ui", "index.html"]}}, "response": []}, {"name": "Swagger Copy", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:52363/servicio-persistencia/swagger-ui/index.html", "protocol": "http", "host": ["localhost"], "port": "52363", "path": ["servicio-persistencia", "swagger-ui", "index.html"]}}, "response": []}]}, {"name": "API v1", "item": [{"name": "Usuarios", "item": [{"name": "Empresas", "item": [{"name": "Empresas listar (por gateway)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"activo\": true,\r\n    \"sortBy\": \"codigo\",\r\n    \"asc\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/empresas/listar", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "empresas", "listar"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "Empresas Modificar por Rol Id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"empresas\": [\r\n        {\r\n            \"id\": 1\r\n        },\r\n        {\r\n            \"id\": 2\r\n        },\r\n        {\r\n            \"id\": 5\r\n        },\r\n        {\r\n            \"id\": 8\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/empresas/modificar-por-rolId/3", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "empresas", "modificar-por-rolId", "3"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}]}, {"name": "Convenios", "item": [{"name": "Convenios listar (por gateway)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"id\": 1,\r\n    \"activo\": true,\r\n    \"sortBy\": \"codigo\",\r\n    \"asc\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/empresas/listarConveniosEmpresa", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "empresas", "listarConveniosEmpresa"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Permisos listar Disponibles (por gateway)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"nombre\": \"\",\r\n    \"endpoint\": \"\",\r\n    \"nota\": \"\",\r\n    \"size\": 500,\r\n    \"page\": 0,\r\n    \"sortBy\": \"nombre\", // por nombre o endpoint\r\n    \"asc\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/permisos/listar-permisos-disponibles", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "permisos", "listar-permisos-disponibles"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "Permisos Listar Por Rol (por gateway)", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"\",\r\n    \"email\": \"\",\r\n    \"size\": 20,\r\n    \"page\": 0,\r\n    \"sortBy\": \"persona.nombres\", // por username o persona.apellido o persona.nombres\r\n    \"asc\": true    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/permisos/listar-permisos-por-rol/1", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "permisos", "listar-permisos-por-rol", "1"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "Permisos Por Id (por gateway)", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"\",\r\n    \"email\": \"\",\r\n    \"size\": 20,\r\n    \"page\": 0,\r\n    \"sortBy\": \"persona.nombres\", // por username o persona.apellido o persona.nombres\r\n    \"asc\": true    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/permisos/por-id/8002", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "permisos", "por-id", "8002"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "Permisos Listar Todos Legacy (por gateway)", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"\",\r\n    \"email\": \"\",\r\n    \"size\": 20,\r\n    \"page\": 0,\r\n    \"sortBy\": \"persona.nombres\", // por username o persona.apellido o persona.nombres\r\n    \"asc\": true    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/permisos/listar-permisos-legacy", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "permisos", "listar-permisos-legacy"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "<PERSON><PERSON><PERSON> (por gateway)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"nombre\": \"USER_MODIFY\",\r\n    \"endpoint\": null,\r\n    \"nota\": \"Modificar usuarios\"    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/permisos/crear-permiso", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "permisos", "crear-permiso"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "<PERSON><PERSON><PERSON> (por gateway)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"nombre\": \"USER_MODIFY\",\r\n    \"endpoint\": null,\r\n    \"nota\": \"Modificar los usuarios\"    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/permisos/modificar-permiso/396", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "permisos", "modificar-permiso", "396"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "Permiso Modificar por Rol Id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"permisos\": [\r\n        {\r\n            \"id\": 8000\r\n        },\r\n        {\r\n            \"id\": 8001\r\n        },\r\n        {\r\n            \"id\": 2\r\n        },\r\n        {\r\n            \"id\": 6\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/permisos/modificar-por-rolId/1", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "permisos", "modificar-por-rolId", "1"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "<PERSON><PERSON><PERSON> (por gateway)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"nombre\": \"Testing_crud\",\r\n    \"descripcion\": \"Rol de prueba\"    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/permisos/eliminar-permiso/374", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "permisos", "eliminar-permiso", "374"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}]}, {"name": "Roles", "item": [{"name": "Roles listar Disponibles (por gateway)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"nombre\": \"\",\r\n    \"descripcion\": \"\",\r\n    \"size\": 20,\r\n    \"page\": 0,\r\n    \"sortBy\": \"nombre\", // por nombre o descripcion\r\n    \"asc\": true    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/roles/listar-roles-disponibles", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "roles", "listar-roles-disponibles"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "Roles listar <PERSON> (por gateway)", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"\",\r\n    \"email\": \"\",\r\n    \"size\": 20,\r\n    \"page\": 0,\r\n    \"sortBy\": \"persona.nombres\", // por username o persona.apellido o persona.nombres\r\n    \"asc\": true    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/roles/listar-roles-por-login/1007", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "roles", "listar-roles-por-login", "1007"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "Rol Por Id (por gateway) Copy", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"\",\r\n    \"email\": \"\",\r\n    \"size\": 20,\r\n    \"page\": 0,\r\n    \"sortBy\": \"persona.nombres\", // por username o persona.apellido o persona.nombres\r\n    \"asc\": true    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/roles/rol-por-id/1", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "roles", "rol-por-id", "1"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "<PERSON><PERSON> (por gateway)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"nombre\": \"Testing_crud\",\r\n    \"descripcion\": \"Rol de prueba\"    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/roles/crear-rol", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "roles", "crear-rol"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "Rol Modificar (por gateway)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"nombre\": \"Testing_cruds\",\r\n    \"descripcion\": \"Rol de prueba\"    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/roles/modificar-rol/121", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "roles", "modificar-rol", "121"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "<PERSON><PERSON> (por gateway)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"nombre\": \"Testing_crud\",\r\n    \"descripcion\": \"Rol de prueba\"    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/roles/eliminar-rol/121", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "roles", "eliminar-rol", "121"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}]}, {"name": "Logs", "item": [{"name": "Listar", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"\",\r\n    \"role\": \"\",\r\n    \"tipo\": \"\",\r\n    \"estado\": \"\",\r\n    \"creado\": \"\",\r\n    \"resultado\": \"\",\r\n    \"desde\": \"\",\r\n    \"hasta\": \"\",\r\n    \"size\": 10,\r\n    \"page\": 0,\r\n    \"sortBy\": \"creado\",\r\n    \"asc\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/usuarios-logs/listar", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "usuarios-logs", "listar"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"1000\",\r\n    \"tipo\": \"403\",\r\n    \"estado\": \"forbidden\",\r\n    \"resultado\": \"[{\\\"message\\\":\\\"De prueba\\\"}]\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/usuarios-logs/registrar", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "usuarios-logs", "registrar"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "Modificar", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"1000\",\r\n    \"tipo\": \"403\",\r\n    \"estado\": \"FORBIDDEN\",\r\n    \"resultado\": \"[{\\\"message\\\":\\\"De prueba\\\"}]\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/usuarios-logs/modificar/1", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "usuarios-logs", "modificar", "1"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "Bo<PERSON>r", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"1000\",\r\n    \"tipo\": \"403\",\r\n    \"estado\": \"forbidden\",\r\n    \"resultado\": \"[{\\\"message\\\":\\\"De prueba 2\\\"}]\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/usuarios-logs/borrar/2", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "usuarios-logs", "borrar", "2"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}]}, {"name": "Menus", "item": [{"name": "<PERSON><PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/menus/listar-menus-legacy", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "menus", "listar-menus-legacy"]}}, "response": []}, {"name": "<PERSON>u por Id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/menus/obtenerPorId/1", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "menus", "obtenerPorId", "1"]}}, "response": []}, {"name": "<PERSON><PERSON> borrar por Id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/menus/borrarPorId/835", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "menus", "borrarPorId", "835"]}}, "response": []}, {"name": "Menu modificar Id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"nombre\": \"Test\",\r\n    \"estado\": \"dashboard.test\",\r\n    \"menuPadreId\": null,\r\n    \"icono\": \"fas fa-home\",\r\n    \"abstracto\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/menus/actualizarPorId/834", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "menus", "actualizarPorId", "834"]}}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"nombre\": \"<PERSON><PERSON>ba\",\r\n    \"estado\": \"dashboard.test\",\r\n    \"menuPadreId\": null,\r\n    \"icono\": null,\r\n    \"abstracto\": false\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/menus/crear", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "menus", "crear"]}}, "response": []}, {"name": "Permiso Modificar por Rol Id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"menus\": [\r\n        {\r\n            \"id\": 1\r\n        },\r\n        {\r\n            \"id\": 2\r\n        },\r\n        {\r\n            \"id\": 5\r\n        },\r\n        {\r\n            \"id\": 8\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/menus/modificar-por-rolId/3", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "menus", "modificar-por-rolId", "3"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}]}, {"name": "Usuarios listar (por gateway)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"\",\r\n    \"names\": \"\",\r\n    \"demo\": \"\",\r\n    \"enNsl\": \"\",\r\n    \"active\": null,\r\n    \"size\": 20,\r\n    \"page\": 0,\r\n    \"sortBy\": null, //\"prsId.apellido\",\r\n    \"asc\": null    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/usuarios/listar", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "usuarios", "listar"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "Usuarios NSL listar (por gateway)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"username\": \"\",\r\n    \"email\": \"\",\r\n    \"apellidos\": \"\",\r\n    \"nombres\": \"\",\r\n    \"size\": 20,\r\n    \"page\": 0,\r\n    \"sortBy\": \"persona.nombres\", // por username o persona.apellido o persona.nombres\r\n    \"asc\": true    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/usuarios/listarnsl", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "usuarios", "listarnsl"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "Usuarios listar (por gateway) Desa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"\",\r\n    \"names\": \"\",\r\n    \"demo\": \"\",\r\n    \"enNsl\": \"\",\r\n    \"active\": null,\r\n    \"size\": 20,\r\n    \"page\": 0,\r\n    \"sortBy\": null, //\"prsId.apellido\",\r\n    \"asc\": null    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/usuarios/listar", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "usuarios", "listar"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "Usuarios listar rapido", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/usuarios/listar-rapido", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "usuarios", "listar-rapido"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "usuarios/auth", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/usuarios/auth", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "usuarios", "auth"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "usuarios/listar (al puerto)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"\",\r\n    \"names\": \"í\",\r\n    \"demo\": \"N\",\r\n    \"active\": true,\r\n    \"size\": 10,\r\n    \"page\": 1,\r\n    \"sortBy\": \"prsId.apellido\",\r\n    \"asc\": true    \r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64001/api/v1/app/usuarios/listar", "host": ["localhost"], "port": "64001", "path": ["api", "v1", "app", "usuarios", "listar"]}, "description": "Aqui apunta a localhost:64000\n\nfijar el port en bootstrap.yaml\n\nStartFragment\n\nserver:\n\nport: 64000\n\nEndFragmentNos\n\nNos ahorramos tiempo y recursos al no pasar por el servicio gateway"}, "response": [{"name": "usuarios/listar Ejemplo", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"login\": \"23\",\r\n    \"names\": \"\",\r\n    \"demo\": \"N\",\r\n    \"active\": true,\r\n    \"size\": 1,\r\n    \"page\": 0,\r\n    \"sortBy\": \"\",\r\n    \"asc\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/usuarios/listar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "usuarios", "listar"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Fri, 02 Jun 2023 10:45:27 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"resultado\": \"ESTADO_EXITO\",\n    \"mensaje\": \"Operación Exitosa\",\n    \"objeto\": {\n        \"content\": [\n            {\n                \"id\": 73,\n                \"login\": \"27242123013\",\n                \"password\": null,\n                \"estado\": null,\n                \"token\": null,\n                \"prsId\": {\n                    \"id\": 5778,\n                    \"ocurrencia\": null,\n                    \"nroDocumento\": null,\n                    \"odtId\": null,\n                    \"prsIdBase\": null,\n                    \"activo\": null,\n                    \"tdcId\": null,\n                    \"apellido\": \"DENDA\",\n                    \"nombres\": \"<PERSON>\",\n                    \"fechaNacimiento\": \"1974-12-14T03:00:00.000+00:00\",\n                    \"fechaDefuncion\": null,\n                    \"lcdId\": 24,\n                    \"sexo\": \"F\",\n                    \"lugarNacimiento\": null,\n                    \"bduPersonas\": {\n                        \"id\": 5778,\n                        \"ocurrencia\": 1,\n                        \"nroDocumento\": 24212301,\n                        \"odtId\": 14,\n                        \"prsIdBase\": null,\n                        \"activo\": true,\n                        \"tdcId\": {\n                            \"id\": 1,\n                            \"descripcion\": \"DOCUMENTO NACIONAL DE IDENTIDAD\",\n                            \"paraPersonaJur\": false,\n                            \"paraPersonaFis\": true,\n                            \"activo\": true,\n                            \"abreviatura\": \"DNI\",\n                            \"principal\": true,\n                            \"camposParaOrdenacion\": [\n                                \"abreviatura\"\n                            ],\n                            \"descripcionCombo\": \"DNI\",\n                            \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\",\n                            \"idConvertido\": 4,\n                            \"tipoDeOrden\": \"ASC\",\n                            \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                            \"valorCombo\": 1,\n                            \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduDocumentosTiposDTO\"\n                        },\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 5778,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasDTO\"\n                    },\n                    \"tnaId\": {\n                        \"id\": 1,\n                        \"descripcion\": \"SIN ESPECIFICAR\",\n                        \"abreviatura\": \" SES\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 1,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduNacionalidadTipoDTO\"\n                    },\n                    \"eclId\": {\n                        \"id\": 0,\n                        \"descripcion\": \" SIN ESPECIFICAR\",\n                        \"activo\": \"S\",\n                        \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\",\n                        \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                        \"valorCombo\": 0,\n                        \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduEstadoCivilDTO\"\n                    },\n                    \"cuil\": 27242123013,\n                    \"nombreCompleto\": null,\n                    \"nroAfiliado\": 51272,\n                    \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\",\n                    \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                    \"valorCombo\": 5778,\n                    \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.BduPersonasFisicasDTO\"\n                },\n                \"roles\": null,\n                \"convenios\": null,\n                \"menuAutorizados\": null,\n                \"estadosAutorizados\": null,\n                \"empresasAutorizadas\": null,\n                \"urlsAutorizadas\": null,\n                \"conveniosAutorizados\": null,\n                \"demo\": \"N\",\n                \"fechaBaja\": null,\n                \"isAdmin\": false,\n                \"remoteHost\": null,\n                \"rolActivo\": null,\n                \"beanName\": \"ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\",\n                \"packageName\": \"ar.gob.lapampa.nsl.datatransfer\",\n                \"valorCombo\": 73,\n                \"cualifiedName\": \"ar.gob.lapampa.nsl.datatransfer.ar.gob.lapampa.nsl.datatransfer.RrhhUsuariosDTO\"\n            }\n        ],\n        \"pageable\": {\n            \"sort\": {\n                \"empty\": true,\n                \"sorted\": false,\n                \"unsorted\": true\n            },\n            \"offset\": 0,\n            \"pageNumber\": 0,\n            \"pageSize\": 1,\n            \"unpaged\": false,\n            \"paged\": true\n        },\n        \"last\": false,\n        \"totalPages\": 30,\n        \"totalElements\": 30,\n        \"size\": 1,\n        \"number\": 0,\n        \"sort\": {\n            \"empty\": true,\n            \"sorted\": false,\n            \"unsorted\": true\n        },\n        \"first\": true,\n        \"numberOfElements\": 1,\n        \"empty\": false\n    },\n    \"codigoEstado\": 0\n}"}]}, {"name": "Usuarios Modificar por Rol Id", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"usuarios\": [\r\n        {\r\n            \"id\": 1\r\n        },\r\n        {\r\n            \"id\": 2\r\n        },\r\n        {\r\n            \"id\": 5\r\n        },\r\n        {\r\n            \"id\": 8\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/usuarios/modificar-por-rolId/3", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "usuarios", "modificar-por-rolId", "3"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}]}, {"name": "Ganancias", "item": [{"name": "ganancias/listar (por gateway)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"corrida\": 85021, //85021\r\n    \"agente\": null,\r\n    \"size\": 100,\r\n    \"page\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/ganancias/listar", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "ganancias", "listar"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "ganancias/listar (port 64000)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"corrida\": 85021,\r\n    \"agente\": null,\r\n    \"size\": 100,\r\n    \"page\": 10\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/ganancias/listar", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "ganancias", "listar"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "ganancias/listarPasos (por gateway)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"corrida\": 85021,\r\n    \"agente\": 15143,\r\n    \"size\": 1,\r\n    \"page\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{Server}}/servicio-persistencia/api/v1/app/ganancias/listarPasos", "host": ["{{Server}}"], "path": ["servicio-persistencia", "api", "v1", "app", "ganancias", "listarPasos"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}, {"name": "ganancias/listarPasos (por 64000)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "126475698745", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\r\n    \"corrida\": 85021,\r\n    \"agente\": 15143,\r\n    \"size\": 10,\r\n    \"page\": 1\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:64000/api/v1/app/ganancias/listarPasos", "host": ["localhost"], "port": "64000", "path": ["api", "v1", "app", "ganancias", "listarPasos"]}, "description": "Para el body\n\npage y size son obligatorios con estas condiciones\n\nsize >= 1 y page >= 0\n\nsort es enrutable por ejemplo prsId contiene el apellido y nombres\n\nsi deseamos ordenar por apellidos ascendente sería\n\n``` json\n\"sortBy\": \"prsId.apellido\",\n\"asc\": true\n\n```\n\nla búsqueda por string aplica a apellidos y nombres\n\ny es case insensitive. La busqueda por login es cuil como cadena (no se valida cuil)\n\nEjemplo estos filtros son equivalentes y traen todos los apellidos\n\ny/o nombres que contenga la subcadena \"AB\"\n\n\"names\" : \"AB\"\n\n\"names\" : \"aB\"\n\n\"names\" : \"ab\"\n\n\"names\" : \"Ab\""}, "response": []}]}, {"name": "Codigos", "item": [{"name": "Listar Codigos Por Convenios", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"empresaId\": 2,\r\n    \"convenio\": 383, //escalafón es lo mismo\r\n    \"tipoCodigoId\": 1,\r\n    \"subTipoCodigoId\": null,\r\n    \"procesoId\": 21774,\r\n    \"codigoId\": 5397, //concepto, es lo mismo\r\n    \"asc\": true,\r\n    \"sortBy\": \"codigoNro\", // codigoNro | codigoEmpresa | convenioNro | haberesSuma | cuentaHaberes | descuentosSuma | cuentaDescuentos, se puede ordenar por todos los campos de la tabla que se muestra\r\n    \"obtenerCantRegistros\": true, //para calcular el paginado\r\n    \"tipoListado\": \"totales\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ProdServer}}/servicio-persistencia/api/v1/app/codigos/listadoConceptosConvenio", "host": ["{{ProdServer}}"], "path": ["servicio-persistencia", "api", "v1", "app", "codigos", "listadoConceptosConvenio"]}}, "response": []}, {"name": "Listar Codigos Por Convenios Copy", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"empresaId\": null,\r\n    \"convenio\": null,\r\n    \"tipoCodigoId\": null,\r\n    \"subTipoCodigoId\": null,\r\n    \"procesoId\": 22240,\r\n    \"codigoId\": null,\r\n    \"asc\": true,\r\n    \"sortBy\": \"codigoNro\",\r\n    \"obtenerCantRegistros\": true,\r\n    \"tipoListado\": \"totales\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ProdServer}}/servicio-persistencia/api/v1/app/codigos/listadoConceptosConvenio", "host": ["{{ProdServer}}"], "path": ["servicio-persistencia", "api", "v1", "app", "codigos", "listadoConceptosConvenio"]}}, "response": []}]}, {"name": "Procesos", "item": [{"name": "Listar Codigos Por Convenios", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"anio\": 2023,\r\n    \"litId\": [\r\n        {\r\n            \"id\": 1\r\n        },\r\n        {\r\n            \"id\": 10\r\n        }\r\n    ],\r\n    \"mes\": 1,\r\n    \"sortBy\": \"id\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{ProdServer}}/servicio-persistencia/api/v1/app/procesos/listar", "host": ["{{ProdServer}}"], "path": ["servicio-persistencia", "api", "v1", "app", "procesos", "listar"]}}, "response": []}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "Server", "value": "localhost:8080", "type": "string"}, {"key": "TestServer", "value": "https://testnnslapi.lapampa.gob.ar", "type": "string"}, {"key": "DesaServer", "value": "https://desannslapi.lapampa.gob.ar", "type": "string"}, {"key": "ProdServer", "value": "https://nnslapi.lapampa.gob.ar", "type": "string"}]}