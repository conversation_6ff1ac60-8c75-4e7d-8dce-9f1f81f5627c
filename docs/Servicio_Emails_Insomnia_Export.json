{"_type": "export", "__export_format": 4, "__export_date": "2023-06-02T11:48:52.288Z", "__export_source": "insomnia.desktop.app:v2023.1.0", "resources": [{"_id": "req_87091a5789e642c99098610b5ea3f15f", "parentId": "fld_eb31d31da757405198facc9fe8562159", "modified": 1682080386899, "created": 1680110647106, "url": "{{Server}}/servicio-emails/api/v1/app/mail/preview", "name": "get/Preview", "description": "", "method": "POST", "body": {"params": [{"value": "<EMAIL>,<EMAIL>,<EMAIL>", "name": "to", "disabled": false}, {"value": "<EMAIL>", "name": "cc", "disabled": false}, {"value": "<EMAIL>", "name": "bcc", "disabled": false}, {"value": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "disabled": false}, {"value": "[<PERSON>rue<PERSON>] Plantilla con Adjuntos", "name": "subject", "disabled": false}, {"value": "VENCIMIENTOS MES DE DICIEMBRE A&Ntilde;O 2022", "name": "titleBody", "disabled": false}, {"value": "<p>2023 - Año del 40º Aniversario de la Restauración Democrática</p>\n<p>2023 - 70 años de la Primera Elección Democrática en La Pampa</p>\n<p>NO a Portezuelo en manos de Mendoza</p>\n<p>El Río Atuel también es Pampeano</p>\n<p>Donar órganos es salvar vidas</p>", "name": "headerBody", "disabled": false}, {"value": "La Direcci&oacute;n General de\n\t\tRentas de la Provincia le recuerda los vencimientos del mes</p>\n\t<table class=\"center\" style=\"width: 820px;\" cellspacing=\"5\"\n\t\tcellpadding=\"8\">\n\t\t<thead>\n\t\t\t<tr>\n\t\t\t\t<td style=\"width: 112.183px; background-color: #04f5f5;\">06/12/2022</td>\n\t\t\t\t<td style=\"width: 491.817px; background-color: #04f5f5;\">\n\t\t\t\t\t<p>Impuesto a los Veh&iacute;culos</p>\n\t\t\t\t\t<p>Cuota 6</p>\n\t\t\t\t</td>\n\t\t\t</tr>\n\t\t</thead>\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td\n\t\t\t\t\tstyle=\"width: 112.183px; background-color: #04f5f5; text-align: justify;\">12/12/2022</td>\n\t\t\t\t<td style=\"width: 491.817px; background-color: #04f5f5;\">\n\t\t\t\t\t<p style=\"text-align: justify;\">Impuesto sobre los Ingresos\n\t\t\t\t\t\tBrutos - Agentes de Recaudaci&oacute;n</p>\n\t\t\t\t\t<p style=\"text-align: justify;\">Mes de Noviembre</p>\n\t\t\t\t</td>\n\t\t\t</tr>\n\t\t</tbody>\n\t</table>\n\t<p>&nbsp;</p>\n\t<p style=\"text-align: justify;\">\n\t\t&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Los pagos\n\t\tpodran realizarse en el Banco de La Pampa o las agencias del banco por\n\t\tel sistema Pampa Pagos o en las receptorias Municipales.\n\t\tTambi&eacute;n a trav&eacute;s de la Red Link de cajeros\n\t\tautom&aacute;ticos, Homebanking, e-Banking, Pago F&aacute;cil,\n\t\tRapipago o mediante el servicio de pagos BtoB de la red Interbanking o\n\t\tpor el servicio de pagos VISA. Para el caso de Convenio Multilateral\n\t\tlos medios de pago son los autorizados por la Comisi&oacute;n\n\t\tArbitral. &nbsp; <strong><br /></strong>\n\t</p>\n\t<p>&nbsp;</p>\n\t<p>&nbsp;</p>\n\t<p>Atentamente.</p>\n\t<p>Direcci&oacute;n General de Rentas</p>\n\t<p>&nbsp;</p>\n\t\n\t<p>\n\t\t<strong>&nbsp;</strong>", "name": "messageBody", "disabled": false}, {"value": "NOTA: <strong>Por favor no responder este mail</strong>, ya que fue\n\t\t\tenviado automaticamente por nuestro sistema. Por cualquier consulta\n\t\t\t<NAME_EMAIL>", "name": "footerBody", "disabled": false}, {"value": "Rentas La Pampa", "name": "sender<PERSON>ame", "disabled": false}, {"value": "<EMAIL>,<EMAIL>,<EMAIL>", "name": "senderMail", "disabled": false}, {"value": "thymel<PERSON><PERSON>", "name": "templateEngine", "disabled": false}, {"value": "html-simple", "name": "templateName", "disabled": false}, {"value": "C:\\temp\\adjunto1.txt,C:\\temp\\adjunto2.txt,C:\\temp\\adjunto3.txt", "name": "attachments", "disabled": false}], "mimeType": "application/x-www-form-urlencoded"}, "parameters": [], "headers": [{"id": "pair_2343f25e8bf242428a3f0d5cef5a01d7", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "description": ""}], "authentication": {}, "metaSortKey": -1680110647107, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_eb31d31da757405198facc9fe8562159", "parentId": "fld_02e1617eb7cb4a668ef6bf75f0231038", "modified": 1680110647111, "created": 1680110647111, "name": "API v1", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1680110647111, "_type": "request_group"}, {"_id": "fld_02e1617eb7cb4a668ef6bf75f0231038", "parentId": "wrk_84a98bc958d946a3819d8ed9b72b1f91", "modified": 1685704630809, "created": 1680110647121, "name": "<PERSON><PERSON><PERSON>", "description": "", "environment": {"Server": "localhost:8080", "Local": "localhost:64000"}, "environmentPropertyOrder": {"&": ["Server", "Local"]}, "metaSortKey": -1680110647121, "_type": "request_group"}, {"_id": "wrk_84a98bc958d946a3819d8ed9b72b1f91", "parentId": null, "modified": 1680110699543, "created": 1680110654295, "name": "La Pampa", "description": "", "scope": "collection", "_type": "workspace"}, {"_id": "req_040a14b154b64344ad16efedf31ad28c", "parentId": "fld_eb31d31da757405198facc9fe8562159", "modified": 1682080317924, "created": 1680110647108, "url": "{{Server}}/servicio-emails/api/v1/app/mail/send/WithAttachment", "name": "send/WithAttachment", "description": "", "method": "POST", "body": {"params": [{"value": "<EMAIL>,<EMAIL>,<EMAIL>", "name": "to", "disabled": false}, {"value": "<EMAIL>", "name": "cc", "disabled": false}, {"value": "<EMAIL>", "name": "bcc", "disabled": false}, {"value": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "disabled": false}, {"value": "[<PERSON>rue<PERSON>] Plantilla con Adjuntos", "name": "subject", "disabled": false}, {"value": "VENCIMIENTOS MES DE DICIEMBRE A&Ntilde;O 2022", "name": "titleBody", "disabled": false}, {"value": "<p>2023 - Año del 40º Aniversario de la Restauración Democrática</p>\n<p>2023 - 70 años de la Primera Elección Democrática en La Pampa</p>\n<p>NO a Portezuelo en manos de Mendoza</p>\n<p>El Río Atuel también es Pampeano</p>\n<p>Donar órganos es salvar vidas</p>", "name": "headerBody", "disabled": false}, {"value": "La Direcci&oacute;n General de\n\t\tRentas de la Provincia le recuerda los vencimientos del mes</p>\n\t<table class=\"center\" style=\"width: 820px;\" cellspacing=\"5\"\n\t\tcellpadding=\"8\">\n\t\t<thead>\n\t\t\t<tr>\n\t\t\t\t<td style=\"width: 112.183px; background-color: #04f5f5;\">06/12/2022</td>\n\t\t\t\t<td style=\"width: 491.817px; background-color: #04f5f5;\">\n\t\t\t\t\t<p>Impuesto a los Veh&iacute;culos</p>\n\t\t\t\t\t<p>Cuota 6</p>\n\t\t\t\t</td>\n\t\t\t</tr>\n\t\t</thead>\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td\n\t\t\t\t\tstyle=\"width: 112.183px; background-color: #04f5f5; text-align: justify;\">12/12/2022</td>\n\t\t\t\t<td style=\"width: 491.817px; background-color: #04f5f5;\">\n\t\t\t\t\t<p style=\"text-align: justify;\">Impuesto sobre los Ingresos\n\t\t\t\t\t\tBrutos - Agentes de Recaudaci&oacute;n</p>\n\t\t\t\t\t<p style=\"text-align: justify;\">Mes de Noviembre</p>\n\t\t\t\t</td>\n\t\t\t</tr>\n\t\t</tbody>\n\t</table>\n\t<p>&nbsp;</p>\n\t<p style=\"text-align: justify;\">\n\t\t&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Los pagos\n\t\tpodran realizarse en el Banco de La Pampa o las agencias del banco por\n\t\tel sistema Pampa Pagos o en las receptorias Municipales.\n\t\tTambi&eacute;n a trav&eacute;s de la Red Link de cajeros\n\t\tautom&aacute;ticos, Homebanking, e-Banking, Pago F&aacute;cil,\n\t\tRapipago o mediante el servicio de pagos BtoB de la red Interbanking o\n\t\tpor el servicio de pagos VISA. Para el caso de Convenio Multilateral\n\t\tlos medios de pago son los autorizados por la Comisi&oacute;n\n\t\tArbitral. &nbsp; <strong><br /></strong>\n\t</p>\n\t<p>&nbsp;</p>\n\t<p>&nbsp;</p>\n\t<p>Atentamente.</p>\n\t<p>Direcci&oacute;n General de Rentas</p>\n\t<p>&nbsp;</p>\n\t\n\t<p>\n\t\t<strong>&nbsp;</strong>", "name": "messageBody", "disabled": false}, {"value": "NOTA: <strong>Por favor no responder este mail</strong>, ya que fue\n\t\t\tenviado automaticamente por nuestro sistema. Por cualquier consulta\n\t\t\t<NAME_EMAIL>", "name": "footerBody", "disabled": false}, {"value": "Rentas La Pampa", "name": "sender<PERSON>ame", "disabled": false}, {"value": "<EMAIL>,<EMAIL>,<EMAIL>", "name": "senderMail", "disabled": false}, {"value": "thymel<PERSON><PERSON>", "name": "templateEngine", "disabled": false}, {"value": "html-simple", "name": "templateName", "disabled": false}, {"value": "C:\\temp\\adjunto1.txt,C:\\temp\\adjunto2.txt,C:\\temp\\adjunto3.txt", "name": "attachments", "disabled": false}], "mimeType": "application/x-www-form-urlencoded"}, "parameters": [], "headers": [{"id": "pair_57eea7f54a494bc28b4505c72e4a5ce9", "name": "Content-Type", "value": "application/x-www-form-urlencoded", "description": ""}], "authentication": {}, "metaSortKey": -1680110647108, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_0845b26ea27c478284d219b82c2f3e03", "parentId": "fld_eb31d31da757405198facc9fe8562159", "modified": 1682080218228, "created": 1680110647110, "url": "{{Server}}/servicio-emails/api/v1/app/mail/send/Simple", "name": "send/MailSimple", "description": "", "method": "POST", "body": {"params": [], "mimeType": "application/x-www-form-urlencoded"}, "parameters": [{"name": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON>bal <PERSON>", "disabled": false}, {"name": "recipientEmail", "value": "<EMAIL>", "disabled": false}, {"name": "locale", "value": "en", "disabled": true}], "headers": [], "authentication": {}, "metaSortKey": -1680110647110, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_ae3a66a9c83a436babcebe36c7f18344", "parentId": "fld_420f870806524f51a6a808a3ae089590", "modified": 1682080100532, "created": 1680110647113, "url": "{{Server}}/servicio-emails/swagger-ui/index.html", "name": "Swagger", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1680110647113, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_420f870806524f51a6a808a3ae089590", "parentId": "fld_02e1617eb7cb4a668ef6bf75f0231038", "modified": 1680110647115, "created": 1680110647115, "name": "Docs", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1680110647115, "_type": "request_group"}, {"_id": "req_62acdd0c3fb64782b3431f6c6b0e0499", "parentId": "fld_420f870806524f51a6a808a3ae089590", "modified": 1682080085862, "created": 1680110647114, "url": "{{Server}}/servicio-emails/api-docs", "name": "Api-Docs", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1680110647114, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_b9f36e63d9b142398c218478e6a626c2", "parentId": "fld_3c0ccd6fbdeb4f6e9abfd751ae7383c8", "modified": 1682080060735, "created": 1680110647116, "url": "{{Server}}/servicio-emails/api/v1/app/envios/list?limit=3", "name": "Check flux envios lista", "description": "", "method": "GET", "body": {}, "parameters": [{"name": "limit", "value": "3", "disabled": false}], "headers": [], "authentication": {}, "metaSortKey": -1680110647116, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_3c0ccd6fbdeb4f6e9abfd751ae7383c8", "parentId": "fld_02e1617eb7cb4a668ef6bf75f0231038", "modified": 1680110647119, "created": 1680110647119, "name": "Checkers", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1680110647119, "_type": "request_group"}, {"_id": "req_e0ea0e647bed4663a9e174cc32c192a2", "parentId": "fld_3c0ccd6fbdeb4f6e9abfd751ae7383c8", "modified": 1682080014962, "created": 1680110647117, "url": "{{Server}}/servicio-emails/logsTest", "name": "Check salidas logs", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1680110647117, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_12dd2a37961d47d2bd81d31390148bed", "parentId": "fld_3c0ccd6fbdeb4f6e9abfd751ae7383c8", "modified": 1682079999675, "created": 1680110647118, "url": "{{Server}}/servicio-emails/estado", "name": "Check status", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1680110647118, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_f016ba54a31544b6a7d19af04e4dd1ef", "parentId": "fld_2080dc080c454fa8a79641563e72ced3", "modified": 1685705976238, "created": 1685705921251, "url": "{{Server}}/servicio-persistencia/estado", "name": "Check status", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1685705921251, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_2080dc080c454fa8a79641563e72ced3", "parentId": "fld_f4dead75e057416aa03808fcdef79443", "modified": 1685704725752, "created": 1685704691240, "name": "Checkers", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1685704711107.5, "_type": "request_group"}, {"_id": "fld_f4dead75e057416aa03808fcdef79443", "parentId": "wrk_84a98bc958d946a3819d8ed9b72b1f91", "modified": 1685704641402, "created": 1685704148024, "name": "<PERSON><PERSON><PERSON>", "description": "", "environment": {"Server": "localhost:8080", "Local": "localhost:64000"}, "environmentPropertyOrder": {"&": ["Server", "Local"]}, "metaSortKey": -1685704148024, "_type": "request_group"}, {"_id": "req_7b41fc7fec684c2d9f1d494573e1907f", "parentId": "fld_2080dc080c454fa8a79641563e72ced3", "modified": 1685706028742, "created": 1685705998054, "url": "{{Server}}/servicio-persistencia/logsTest", "name": "Check salidas logs", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1685705176716, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_876ba5d0a5c0406f8c2d09a5b9557dfa", "parentId": "fld_71a76168d4834316b0900728232895ae", "modified": 1685706302875, "created": 1685706049345, "url": "{{Server}}/servicio-persistencia/api-docs", "name": "Api-Docs", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1685706049345, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_71a76168d4834316b0900728232895ae", "parentId": "fld_f4dead75e057416aa03808fcdef79443", "modified": 1685704705271, "created": 1685704705271, "name": "Docs", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1685704705271, "_type": "request_group"}, {"_id": "req_505182b854af4c3bad5fcf1b1b530b44", "parentId": "fld_71a76168d4834316b0900728232895ae", "modified": 1685706421246, "created": 1685706400180, "url": "{{Server}}/servicio-persistencia/swagger-ui/index.html", "name": "Swagger", "description": "", "method": "GET", "body": {}, "parameters": [], "headers": [], "authentication": {}, "metaSortKey": -1685705985298, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "req_1a838da50d3541ad8a0112cfafba79c3", "parentId": "fld_a3371f47e068414d95294ff79c0ffe3e", "modified": 1685705636877, "created": 1685704159191, "url": "{{Server}}/servicio-persistencia/api/v1/app/usuarios/listar", "name": "usuarios/listar (por gateway)", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n    \"login\": \"23\",\n    \"names\": \"\",\n    \"demo\": \"N\",\n    \"active\": true,\n    \"size\": 1,\n    \"page\": 0,\n    \"sortBy\": \"prsId.apellido\",\n    \"asc\": true\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}], "authentication": {}, "metaSortKey": -1685704432181, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "fld_a3371f47e068414d95294ff79c0ffe3e", "parentId": "fld_f4dead75e057416aa03808fcdef79443", "modified": 1685704728296, "created": 1685704716944, "name": "API v1", "description": "", "environment": {}, "environmentPropertyOrder": null, "metaSortKey": -1685704432231, "_type": "request_group"}, {"_id": "req_752862dac6594dbc9517262f76e34cf9", "parentId": "fld_a3371f47e068414d95294ff79c0ffe3e", "modified": 1685705758487, "created": 1685705654944, "url": "{{Local}}/api/v1/app/usuarios/listar", "name": "usuarios/listar (al puerto)", "description": "", "method": "POST", "body": {"mimeType": "application/json", "text": "{\n    \"login\": \"23\",\n    \"names\": \"\",\n    \"demo\": \"N\",\n    \"active\": true,\n    \"size\": 1,\n    \"page\": 0,\n    \"sortBy\": \"prsId.apellido\",\n    \"asc\": true\n}"}, "parameters": [], "headers": [{"name": "Content-Type", "value": "application/json"}], "authentication": {}, "metaSortKey": -1682907539649.5, "isPrivate": false, "settingStoreCookies": true, "settingSendCookies": true, "settingDisableRenderRequestBody": false, "settingEncodeUrl": true, "settingRebuildPath": true, "settingFollowRedirects": "global", "_type": "request"}, {"_id": "env_377aedd62cb394240e3118d4069703ede834ad0c", "parentId": "wrk_84a98bc958d946a3819d8ed9b72b1f91", "modified": 1680110663857, "created": 1680110663857, "name": "Base Environment", "data": {}, "dataPropertyOrder": null, "color": null, "isPrivate": false, "metaSortKey": 1680110663857, "_type": "environment"}, {"_id": "jar_377aedd62cb394240e3118d4069703ede834ad0c", "parentId": "wrk_84a98bc958d946a3819d8ed9b72b1f91", "modified": 1680110663860, "created": 1680110663860, "name": "<PERSON><PERSON><PERSON>", "cookies": [], "_type": "cookie_jar"}, {"_id": "spc_c5811e6091cc4b66af1f37de4aab94bd", "parentId": "wrk_84a98bc958d946a3819d8ed9b72b1f91", "modified": 1680110654296, "created": 1680110654296, "fileName": "My Collection", "contents": "", "contentType": "yaml", "_type": "api_spec"}, {"_id": "env_7a9aab036a524ed6b2a3f574481f64c5", "parentId": "env_377aedd62cb394240e3118d4069703ede834ad0c", "modified": 1682079952139, "created": 1682079952139, "name": "New Environment", "data": {}, "dataPropertyOrder": null, "color": null, "isPrivate": false, "metaSortKey": 1682079952139, "_type": "environment"}]}